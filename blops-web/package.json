{"name": "blops", "version": "5.2.0", "private": true, "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 umi build", "build": "umi build", "deploy": "npm run build && npm run gh-pages", "dev": "npm run start:dev", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "umi g tmp", "lint": "umi g tmp && npm run lint:js && npm run lint:style && npm run lint:prettier && npm run tsc", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"src/**/*\" --end-of-line auto", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "openapi": "umi open<PERSON>i", "playwright": "playwright install && playwright test", "prettier": "prettier -c --write \"src/**/*\"", "serve": "umi-serve", "start": "cross-env UMI_ENV=dev umi dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none UMI_ENV=dev umi dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev umi dev", "start:no-ui": "cross-env UMI_UI=none UMI_ENV=dev umi dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev umi dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev umi dev", "test": "umi test", "test:component": "umi test ./src/components", "test:e2e": "node ./tests/run-tests.js", "tsc": "tsc --noEmit"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/charts": "^1.4.0", "@ant-design/icons": "^4.5.0", "@ant-design/pro-descriptions": "^1.9.0", "@ant-design/pro-form": "^1.43.0", "@ant-design/pro-layout": "^6.32.0-beta.4", "@ant-design/pro-table": "^2.56.0", "@umijs/route-utils": "^2.0.4", "antd": "^4.17.0", "classnames": "^2.2.6", "dayjs": "^1.10.7", "dva": "^2.4.1", "js-yaml": "^4.1.0", "lodash": "^4.17.11", "moment": "^2.29.1", "monaco-editor": "^0.52.2", "omit.js": "^2.0.2", "prismjs": "^1.25.0", "rc-menu": "^9.0.13", "rc-util": "^5.14.0", "react": "^16.14.0", "react-dev-inspector": "^1.1.1", "react-dom": "^16.14.0", "react-helmet-async": "^1.0.4", "react-json-view": "^1.21.3", "react-markdown": "^8.0.7", "react-monaco-editor": "^0.58.0", "react-scripts": "^4.0.0", "umi": "^3.5.0", "umi-serve": "^1.9.10", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0"}, "devDependencies": {"@ant-design/pro-cli": "^2.0.2", "@playwright/test": "^1.16.3", "@types/express": "^4.17.0", "@types/history": "^4.7.2", "@types/jest": "^26.0.0", "@types/lodash": "^4.14.144", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@types/react-helmet": "^6.1.0", "@types/react-monaco-editor": "^0.16.0", "@umijs/fabric": "^2.6.2", "@umijs/openapi": "^1.3.0", "@umijs/plugin-blocks": "^2.0.5", "@umijs/plugin-esbuild": "^1.0.1", "@umijs/plugin-openapi": "^1.2.0", "@umijs/preset-ant-design-pro": "^1.2.0", "@umijs/preset-dumi": "^1.1.7", "@umijs/preset-react": "^1.8.17", "@umijs/yorkie": "^2.0.3", "carlo": "^0.9.46", "cross-env": "^7.0.0", "cross-port-killer": "^1.1.1", "detect-installer": "^1.0.1", "enzyme": "^3.11.0", "eslint": "^7.1.0", "express": "^4.17.1", "gh-pages": "^3.0.0", "jsdom-global": "^3.0.2", "lint-staged": "^10.0.0", "mockjs": "^1.0.1-beta3", "monaco-editor-webpack-plugin": "^7.1.0", "prettier": "^2.3.2", "puppeteer-core": "^8.0.0", "stylelint": "^13.0.0", "typescript": "^4.2.2"}, "engines": {"node": ">=10.0.0"}, "__npminstall_done": false}