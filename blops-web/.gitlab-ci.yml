image: harbor.blacklake.tech/az/gitlab-ci-runtime:latest

variables:
  APP_NAME: "blops-web"

stages:
  - build

build-feature:
  stage: build
  only:
    - dev
    - feature
    - test
    - master
    - /^release-*/
    - /^feat-*/
  script:
    - /ci/scripts/login_harbor.sh
    - date -d @`git show -s --format=%ct` +"%Y%m%d%H%M%S" > ${CI_COMMIT_SHA}_commit_date
    - export BUILD_DATE=`cat ${CI_COMMIT_SHA}_commit_date`
    - export DOCKER_IMAGE=harbor.blacklake.tech/az/${APP_NAME}:${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHA}-${BUILD_DATE}
    - npm i
    - npm run build
    - docker build -t ${DOCKER_IMAGE} .
    - docker push ${DOCKER_IMAGE}


