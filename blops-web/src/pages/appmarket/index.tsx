import React, { useState, useEffect } from 'react';
import { <PERSON>Container } from '@ant-design/pro-layout';
import { Card, List, Button, Input, Select, Tag, Modal, Spin, message, Tabs, Row, Col, Tooltip, Form, Table, Space } from 'antd';
import { 
  PlusOutlined, 
  SearchOutlined, 
  SettingOutlined, 
  AppstoreOutlined, 
  CodeOutlined, 
  EditOutlined, 
  ReloadOutlined,
  DeleteOutlined,
  EyeOutlined
} from '@ant-design/icons';
import request from '@/utils/request';
import CodeEditor from '@/components/CodeEditor';
import yaml from 'js-yaml';
import styles from './index.less';

const { Option } = Select;
const { TabPane } = Tabs;

// 应用模板接口定义
interface AppTemplate {
  name: string;
  description: string;
  category: string;
  icon: string;
  version: string;
  maintainer: string;
  yamlTemplate: string;
  variables: TemplateVariable[];
}

// 模板变量接口定义
interface TemplateVariable {
  name: string;
  label: string;
  description: string;
  default_value: any;
  required: boolean;
  type: string;
  options?: { label: string; value: any }[];
}

// 部署参数接口定义
interface DeploymentParams {
  templateName: string;
  namespace: string;
  cluster: string;
  variables: { [key: string]: any };
}

// 部署记录接口定义
interface DeploymentRecord {
  id: string;
  name: string;
  templateName: string;
  namespace: string;
  cluster: string;
  status: string;
  createdAt: string;
  resources: string[];
}

const defaultYamlSample = `# Sample YAML Deployment Template
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{.name}}
spec:
  replicas: {{.replicas}}
  selector:
    matchLabels:
      app: {{.name}}
  template:
    metadata:
      labels:
        app: {{.name}}
    spec:
      containers:
      - name: {{.name}}
        image: {{.image}}
        ports:
        - containerPort: {{.port}}`;

const AppMarket: React.FC = () => {
  // 状态定义
  const [loading, setLoading] = useState<boolean>(false);
  const [templates, setTemplates] = useState<AppTemplate[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<AppTemplate[]>([]);
  const [searchText, setSearchText] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [categories, setCategories] = useState<string[]>([]);
  
  // 弹窗状态
  const [deployModalVisible, setDeployModalVisible] = useState<boolean>(false);
  const [yamlModalVisible, setYamlModalVisible] = useState<boolean>(false);
  const [selectedTemplate, setSelectedTemplate] = useState<AppTemplate | null>(null);
  const [generatedYaml, setGeneratedYaml] = useState<string>('');
  
  // 表单
  const [form] = Form.useForm();
  const [deployForm] = Form.useForm();
  
  // 集群和命名空间 - 使用固定的集群列表而不是动态获取
  const [clusters, setClusters] = useState<{ name: string; displayName?: string }[]>([]);
  const [selectedCluster, setSelectedCluster] = useState<string>('');
  
  // 模板编辑状态
  const [templateModalVisible, setTemplateModalVisible] = useState<boolean>(false);
  const [isEditingTemplate, setIsEditingTemplate] = useState<boolean>(false);
  const [templateForm] = Form.useForm();
  const [yamlContent, setYamlContent] = useState(defaultYamlSample);
  const [variablesList, setVariablesList] = useState<TemplateVariable[]>([]);
  
  // 部署记录状态
  const [activeTabKey, setActiveTabKey] = useState<string>('templates');
  const [deploymentRecords, setDeploymentRecords] = useState<DeploymentRecord[]>([]);
  const [deploymentLoading, setDeploymentLoading] = useState<boolean>(false);
  
  // 获取应用模板列表
  const fetchTemplates = async () => {
    setLoading(true);
    try {
      const response = await request('/api/v1/app-market/templates/_list', {
        method: 'POST',
        data: {
          page: 1,
          page_size: 100,
          search: searchText,
          category: selectedCategory,
        },
      });
      
      if (response && response.code === 200 && response.data) {
        setTemplates(response.data.templates);
        setFilteredTemplates(response.data.templates);
        
        // 提取所有类别
        const allCategories = Array.from(
          new Set(response.data.templates.map((t: AppTemplate) => t.category))
        ) as string[];
        setCategories(allCategories);
      } else {
        message.error('获取应用模板失败');
      }
    } catch (error) {
      console.error('获取应用模板失败:', error);
      message.error('获取应用模板失败');
    } finally {
      setLoading(false);
    }
  };
  
  // 首次加载时获取模板，不再获取集群列表
  useEffect(() => {
    fetchTemplates();
    // 写死固定的集群列表
    const fixedClusters = [
      { name: 'ali-test', displayName: 'ali-test' },
      { name: 'ali-prod', displayName: 'ali-prod' },
      { name: 'ali-guotai', displayName: 'ali-guotai' },
      { name: 'hwyx-prod', displayName: 'hwyx-prod' },
      { name: 'ali-v3', displayName: 'ali-v3' },
    ];
    setClusters(fixedClusters);
    setSelectedCluster(fixedClusters[0].name);
  }, []);
  
  // 获取部署记录
  const fetchDeploymentRecords = async () => {
    setDeploymentLoading(true);
    try {
      // 改为调用后端已部署应用接口
      const response = await request('/api/appmarket/deployed', {
        method: 'GET',
      });
      // 兼容后端直接返回 Array 或者 { data: Array }
      const rawList = Array.isArray(response.data)
        ? response.data
        : Array.isArray(response.data?.data)
          ? response.data.data
          : [];

      if (rawList.length > 0) {
        // 字段映射：template -> templateName, cluster -> cluster
        const records: DeploymentRecord[] = rawList.map((item: any) => ({
          id: String(item.id),
          name: item.name,
          templateName: item.template,
          namespace: item.namespace,
          cluster: item.cluster,
          status: item.status,
          createdAt: item.createdAt,
          resources: item.resources || [],
        }));
        setDeploymentRecords(records);
      } else {
        // 虽然请求成功，但数据为空或格式不对
        setDeploymentRecords([]);
        // 根据需求，这里可以不提示或提示
        // message.warn('暂无已部署应用');
      }
    } catch (error) {
      console.error('获取部署记录失败:', error);
      message.error('获取部署记录失败');
    } finally {
      setDeploymentLoading(false);
    }
  };
  
  // 部署成功后更新部署记录
  const refreshDeploymentRecords = () => {
    if (activeTabKey === 'deployments') {
      fetchDeploymentRecords();
    }
  };
  
  // 切换标签页时获取相应数据
  useEffect(() => {
    if (activeTabKey === 'deployments') {
      fetchDeploymentRecords();
    }
  }, [activeTabKey]);
  
  // 处理部署
  const handleDeploy = (template: AppTemplate) => {
    setSelectedTemplate(template);
    setDeployModalVisible(true);
    
    // 设置表单初始值
    const initialValues: { [key: string]: any } = {
      cluster: selectedCluster,
      namespace: 'default',
    };
    
    // 添加模板变量的默认值
    template.variables.forEach((variable) => {
      initialValues[variable.name] = variable.default_value;
    });
    
    deployForm.setFieldsValue(initialValues);
  };
  
  // 提交部署
  const submitDeploy = async () => {
    if (!selectedTemplate) return;
    
    try {
      const values = await deployForm.validateFields();
      const { cluster, ...variables } = values;
      
      const deploymentParams: DeploymentParams = {
        templateName: selectedTemplate.name,
        namespace: 'default',
        cluster,
        variables,
      };
      
      setLoading(true);
      // 改为 POST 新增已部署应用
      const payload = {
        templateName: selectedTemplate.name,
        cluster,
        namespace: 'default',
        variables,
      };
      const response = await request('/api/appmarket/deployed', {
        method: 'POST',
        data: payload,
      });
      if (response && response.code === 200) {
        message.success('应用部署成功');
        setDeployModalVisible(false);
        // 刷新列表
        refreshDeploymentRecords();
      } else {
        message.error('部署失败: ' + (response.message || '未知错误'));
      }
    } catch (error) {
      console.error('部署失败:', error);
      message.error('部署失败，请检查参数');
    } finally {
      setLoading(false);
    }
  };
  
  // 渲染表单控件
  const renderFormItem = (variable: TemplateVariable) => {
    const { name, label, description, type, options, required } = variable;
    
    switch (type) {
      case 'string':
        return (
          <Form.Item 
            key={name} 
            label={label} 
            name={name} 
            tooltip={description}
            rules={[{ required, message: `请输入${label}` }]}
          >
            <Input placeholder={`请输入${label}`} />
          </Form.Item>
        );
        
      case 'number':
        return (
          <Form.Item 
            key={name} 
            label={label} 
            name={name} 
            tooltip={description}
            rules={[{ required, message: `请输入${label}` }]}
          >
            <Input type="number" placeholder={`请输入${label}`} />
          </Form.Item>
        );
        
      case 'boolean':
        return (
          <Form.Item 
            key={name} 
            label={label} 
            name={name} 
            tooltip={description}
            valuePropName="checked"
            rules={[{ required, message: `请选择${label}` }]}
          >
            <Select>
              <Option value={true}>是</Option>
              <Option value={false}>否</Option>
            </Select>
          </Form.Item>
        );
        
      case 'select':
        return (
          <Form.Item 
            key={name} 
            label={label} 
            name={name} 
            tooltip={description}
            rules={[{ required, message: `请选择${label}` }]}
          >
            <Select placeholder={`请选择${label}`}>
              {options?.map((option) => (
                <Option key={String(option.value)} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        );
        
      default:
        return (
          <Form.Item 
            key={name} 
            label={label} 
            name={name} 
            tooltip={description}
            rules={[{ required, message: `请输入${label}` }]}
          >
            <Input placeholder={`请输入${label}`} />
          </Form.Item>
        );
    }
  };
  
  // 处理编辑模板
  const handleEditTemplate = (template: AppTemplate) => {
    setSelectedTemplate(template);
    setIsEditingTemplate(true);
    setTemplateModalVisible(true);
    setVariablesList(template.variables);
    
    templateForm.setFieldsValue({
      name: template.name,
      description: template.description,
      category: template.category,
      icon: template.icon,
      version: template.version,
      maintainer: template.maintainer,
      yamlTemplate: template.yamlTemplate,
    });
    setYamlContent(template.yamlTemplate);
  };
  
  // 处理添加模板
  const handleAddTemplate = () => {
    setSelectedTemplate(null);
    setIsEditingTemplate(false);
    setTemplateModalVisible(true);
    setVariablesList([]);
    
    templateForm.resetFields();
    templateForm.setFieldsValue({ yamlTemplate: defaultYamlSample });
    setYamlContent(defaultYamlSample);
  };
  
  // 处理添加变量
  const handleAddVariable = () => {
    setVariablesList([
      ...variablesList,
      {
        name: '',
        label: '',
        description: '',
        default_value: '',
        required: false,
        type: 'string',
      }
    ]);
  };
  
  // 处理删除变量
  const handleRemoveVariable = (index: number) => {
    const newVariables = [...variablesList];
    newVariables.splice(index, 1);
    setVariablesList(newVariables);
  };
  
  // 处理变量变化
  const handleVariableChange = (index: number, field: string, value: any) => {
    const newVariables = [...variablesList];
    newVariables[index] = {
      ...newVariables[index],
      [field]: value
    };
    setVariablesList(newVariables);
  };
  
  // 提交模板表单
  const submitTemplateForm = async () => {
    try {
      const values = await templateForm.validateFields();
      const templateData = {
        ...values,
        variables: variablesList,
      };
      
      setLoading(true);
      
      const url = isEditingTemplate
        ? `/api/v1/app-market/templates/${selectedTemplate?.name}`
        : '/api/v1/app-market/templates';
      
      const method = isEditingTemplate ? 'PUT' : 'POST';
      
      const response = await request(url, {
        method,
        data: templateData,
      });
      
      if (response && response.code === 200) {
        message.success(isEditingTemplate ? '模板更新成功' : '模板添加成功');
        setTemplateModalVisible(false);
        fetchTemplates(); // 重新加载模板列表
      } else {
        message.error((isEditingTemplate ? '更新' : '添加') + '模板失败: ' + (response.message || '未知错误'));
      }
    } catch (error) {
      console.error('提交模板表单失败:', error);
      message.error('表单验证失败，请检查填写的内容');
    } finally {
      setLoading(false);
    }
  };
  
  // 删除模板
  const handleDeleteTemplate = async (template: AppTemplate) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除模板"${template.name}"吗？此操作不可恢复。`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        setLoading(true);
        try {
          const response = await request(`/api/v1/app-market/templates/${template.name}`, {
            method: 'DELETE',
          });
          
          if (response && response.code === 200) {
            message.success('模板删除成功');
            fetchTemplates(); // 重新加载模板列表
          } else {
            message.error('删除模板失败: ' + (response.message || '未知错误'));
          }
        } catch (error) {
          console.error('删除模板失败:', error);
          message.error('删除模板失败');
        } finally {
          setLoading(false);
        }
      }
    });
  };
  
  // 删除部署记录
  const handleDeleteRecord = (record: DeploymentRecord) => {
    Modal.confirm({
      title: '确认删除已部署应用',
      content: `确定删除应用 "${record.name}" 吗？此操作不可恢复。`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        setDeploymentLoading(true);
        try {
          // 调用 DELETE 删除单个已部署应用
          await request(`/api/appmarket/deployed/${record.id}`, {
            method: 'DELETE',
          });
          message.success('删除成功');
          fetchDeploymentRecords();
        } catch (err) {
          console.error(err);
          message.error('删除失败');
        } finally {
          setDeploymentLoading(false);
        }
      },
    });
  };
  
  // 更新部署记录
  const handleUpdateRecord = async (record: DeploymentRecord) => {
    setLoading(true);
    try {
      // 调用 PUT 更新单个已部署应用
      const resp = await request(`/api/appmarket/deployed/${record.id}`, {
        method: 'PUT',
        data: {
          name: record.name,
          namespace: record.namespace,
          cluster: record.cluster,
        },
      });
      if (resp && resp.code === 200) {
        message.success('更新成功');
        fetchDeploymentRecords();
      } else {
        message.error('更新失败: ' + (resp.message || '未知错误'));
      }
    } catch (err) {
      console.error(err);
      message.error('更新失败');
    } finally {
      setLoading(false);
    }
  };
  
  // 部署记录列
  const deploymentColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
        title: '应用名称',
        dataIndex: 'name',
        key: 'name',
        width: 150,
      },
    {
      title: '关联模板',
      dataIndex: 'templateName',
      key: 'templateName',
      width: 150,
    },
    {
      title: '命名空间',
      dataIndex: 'namespace',
      key: 'namespace',
      width: 150,
    },
    {
      title: '集群',
      dataIndex: 'cluster',
      key: 'cluster',
      width: 150,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (text: string) => {
        let color = 'green';
        if (text === 'Failed') {
          color = 'red';
        } else if (text === 'Pending') {
          color = 'orange';
        }
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
    },
    {
      title: '资源',
      dataIndex: 'resources',
      key: 'resources',
      render: (resources: any[]) => (
        <div>
          {resources.map((resource, index) => {
             const displayText = typeof resource === 'object' && resource.resourceName
               ? `${resource.kind}: ${resource.resourceName}`
               : resource;
             return (
               <Tag key={index} color="blue" style={{ margin: '2px' }}>
                 {displayText}
               </Tag>
             );
          })}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_: any, record: DeploymentRecord) => (
        <Space size="small">
          <Button 
            type="text" 
            size="small"
            icon={<EyeOutlined />}
            onClick={() => message.info('查看功能后续实现')}
          >
            查看
          </Button>
          <Button 
            type="text" 
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleUpdateRecord(record)}
          >
            编辑
          </Button>
          <Button 
            type="text" 
            danger 
            size="small"
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteRecord(record)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];
  
  return (
    <PageContainer title="应用市场">
      <Card>
        {/* 添加集群和命名空间选择器 */}
        <div style={{ marginBottom: 20, display: 'flex', alignItems: 'center' }}>
          <div style={{ marginRight: 20, display: 'flex', alignItems: 'center' }}>
            <span style={{ marginRight: 10 }}>集群:</span>
            <Select 
              style={{ width: 200 }}
              value={selectedCluster}
              onChange={(value) => {
                setSelectedCluster(value);
              }}
            >
              {clusters.map((cluster) => (
                <Option key={cluster.name} value={cluster.name}>
                  {cluster.displayName || cluster.name}
                </Option>
              ))}
            </Select>
          </div>
          <Input 
            placeholder="搜索应用" 
            prefix={<SearchOutlined />} 
            style={{ width: 200, marginRight: 16 }}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
          />
        </div>

        <Tabs 
          activeKey={activeTabKey} 
          onChange={setActiveTabKey}
          tabBarExtraContent={
            activeTabKey === 'deployments' ? (
              <Button 
                icon={<ReloadOutlined />} 
                onClick={fetchDeploymentRecords}
                loading={deploymentLoading}
              >
                刷新
              </Button>
            ) : (
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={handleAddTemplate}
              >
                添加模板
              </Button>
            )
          }
        >
          <TabPane tab="应用模板" key="templates">
            <div className={styles.toolbar}>
              <Select
                placeholder="按类别筛选"
                style={{ width: 160, marginRight: 16 }}
                allowClear
                value={selectedCategory}
                onChange={(value) => setSelectedCategory(value)}
              >
                {categories.map((category) => (
                  <Option key={category} value={category}>{category}</Option>
                ))}
              </Select>
            </div>
            
            <Spin spinning={loading}>
              <List
                grid={{ gutter: 16, xs: 1, sm: 2, md: 2, lg: 3, xl: 4, xxl: 6 }}
                dataSource={filteredTemplates}
                locale={{ emptyText: '暂无应用模板' }}
                renderItem={(template) => (
                  <List.Item key={template.name}>
                    <Card
                      hoverable
                      cover={
                        <div className={styles.cardCover}>
                          <AppstoreOutlined style={{ fontSize: 40 }} />
                        </div>
                      }
                      actions={[
                        <Tooltip key="deploy" title="部署">
                          <SettingOutlined onClick={() => handleDeploy(template)} />
                        </Tooltip>,
                        <Tooltip key="edit" title="编辑">
                          <EditOutlined onClick={() => handleEditTemplate(template)} />
                        </Tooltip>,
                        <Tooltip key="yaml" title="查看YAML">
                          <CodeOutlined onClick={() => {
                            setSelectedTemplate(template);
                            setGeneratedYaml(template.yamlTemplate);
                            setYamlModalVisible(true);
                          }} />
                        </Tooltip>,
                      ]}
                    >
                      <Card.Meta
                        title={
                          <div>
                            {template.name}
                            <Tag color="blue" style={{ marginLeft: 8 }}>v{template.version}</Tag>
                          </div>
                        }
                        description={
                          <div>
                            <div>{template.description}</div>
                            <div style={{ marginTop: 8 }}>
                              <Tag color="green">{template.category}</Tag>
                            </div>
                          </div>
                        }
                      />
                    </Card>
                  </List.Item>
                )}
              />
            </Spin>
          </TabPane>
          
          <TabPane tab="已部署应用" key="deployments">
            <Spin spinning={deploymentLoading}>
              <Table
                columns={deploymentColumns}
                dataSource={deploymentRecords}
                rowKey="id"
                pagination={{ pageSize: 10 }}
                scroll={{ x: 1200 }}
              />
            </Spin>
          </TabPane>
        </Tabs>
      </Card>
      
      {/* 部署弹窗 */}
      <Modal
        title={`部署 ${selectedTemplate?.name || ''}`}
        open={deployModalVisible}
        onCancel={() => setDeployModalVisible(false)}
        width={700}
        footer={[
          <Button key="cancel" onClick={() => setDeployModalVisible(false)}>
            取消
          </Button>,
          <Button 
            key="deploy" 
            type="primary" 
            loading={loading}
            onClick={submitDeploy}
          >
            部署
          </Button>,
        ]}
      >
        <Form
          form={deployForm}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item 
                label="集群" 
                name="cluster"
                rules={[{ required: true, message: '请选择集群' }]}
              >
                <Select 
                  placeholder="请选择集群"
                  onChange={(value) => {
                    setSelectedCluster(value as string);
                    deployForm.setFieldsValue({ namespace: 'default' });
                  }}
                >
                  {clusters.map((cluster) => (
                    <Option key={cluster.name} value={cluster.name}>
                      {cluster.displayName || cluster.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          {/* 隐藏的命名空间字段，始终为 "default" */}
          <Form.Item name="namespace" initialValue="default" hidden>
            <Input />
          </Form.Item>
          
          <Tabs defaultActiveKey="basic">
            <TabPane tab="基本配置" key="basic">
              {selectedTemplate?.variables
                .filter(v => ['name', 'image', 'replicas', 'port'].includes(v.name))
                .map(renderFormItem)}
            </TabPane>
            <TabPane tab="资源配置" key="resources">
              {selectedTemplate?.variables
                .filter(v => ['memoryRequest', 'cpuRequest', 'memoryLimit', 'cpuLimit'].includes(v.name))
                .map(renderFormItem)}
            </TabPane>
            <TabPane tab="服务配置" key="service">
              {selectedTemplate?.variables
                .filter(v => ['enableService', 'servicePort', 'serviceType'].includes(v.name))
                .map(renderFormItem)}
            </TabPane>
            {/* 数据库配置 (仅对MySQL等数据库类型应用显示) */}
            {selectedTemplate?.category === "数据库" && (
              <TabPane tab="数据库配置" key="database">
                {selectedTemplate?.variables
                  .filter(v => ['rootPassword', 'createDatabase', 'databaseName', 'createUser', 'username', 'password'].includes(v.name))
                  .map(renderFormItem)}
              </TabPane>
            )}
            {/* 持久化配置 */}
            {selectedTemplate?.variables.some(v => v.name === 'enablePersistence') && (
              <TabPane tab="持久化配置" key="persistence">
                {selectedTemplate?.variables
                  .filter(v => ['enablePersistence', 'pvcName', 'storageSize', 'storageClassName'].includes(v.name))
                  .map(renderFormItem)}
              </TabPane>
            )}
            {/* 监控配置 (仅对Prometheus等监控类型应用显示) */}
            {selectedTemplate?.category === "监控" && (
              <TabPane tab="监控配置" key="monitoring">
                {selectedTemplate?.variables
                  .filter(v => ['configMapName', 'scrapeInterval', 'evaluationInterval', 'alertmanagerEnabled', 'alertmanagerHost', 'alertmanagerPort'].includes(v.name))
                  .map(renderFormItem)}
              </TabPane>
            )}
          </Tabs>
        </Form>
      </Modal>
      
      {/* YAML预览弹窗 */}
      <Modal
        title="YAML预览"
        open={yamlModalVisible}
        onCancel={() => setYamlModalVisible(false)}
        width={800}
        footer={[
          <Button key="close" onClick={() => setYamlModalVisible(false)}>
            关闭
          </Button>,
        ]}
      >
        <CodeEditor
          value={generatedYaml}
          language="yaml"
          height="500px"
          readOnly
        />
      </Modal>
      
      {/* 模板编辑弹窗 */}
      <Modal
        title={isEditingTemplate ? "编辑模板" : "添加模板"}
        open={templateModalVisible}
        onCancel={() => setTemplateModalVisible(false)}
        width={800}
        footer={[
          <Button key="cancel" onClick={() => setTemplateModalVisible(false)}>
            取消
          </Button>,
          <Button
            key="delete"
            danger
            style={{ display: isEditingTemplate ? 'inline-block' : 'none' }}
            onClick={() => selectedTemplate && handleDeleteTemplate(selectedTemplate)}
          >
            删除
          </Button>,
          <Button 
            key="submit" 
            type="primary" 
            loading={loading}
            onClick={submitTemplateForm}
          >
            提交
          </Button>,
        ]}
      >
        <Form
          form={templateForm}
          layout="vertical"
        >
          <Tabs defaultActiveKey="basic">
            <TabPane tab="基本信息" key="basic">
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item 
                    label="模板名称" 
                    name="name"
                    rules={[{ required: true, message: '请输入模板名称' }]}
                  >
                    <Input placeholder="请输入模板名称" disabled={isEditingTemplate} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item 
                    label="版本" 
                    name="version"
                    rules={[{ required: true, message: '请输入版本号' }]}
                  >
                    <Input placeholder="请输入版本号，例如：1.0.0" />
                  </Form.Item>
                </Col>
              </Row>
              
              <Form.Item 
                label="描述" 
                name="description"
                rules={[{ required: true, message: '请输入模板描述' }]}
              >
                <Input placeholder="请输入模板描述" />
              </Form.Item>
              
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item 
                    label="类别" 
                    name="category"
                    rules={[{ required: true, message: '请输入或选择类别' }]}
                  >
                    <Select
                      placeholder="请输入或选择类别"
                      showSearch
                      allowClear
                    >
                      {/* <Option key="网络" value="网络">网络</Option>
                      <Option key="日志" value="日志">日志</Option>
                      <Option key="监控" value="监控">监控</Option> */}
                      {categories.map((category) => (
                        <Option key={category} value={category}>{category}</Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item 
                    label="维护者" 
                    name="maintainer"
                    rules={[{ required: true, message: '请输入维护者' }]}
                  >
                    <Input placeholder="请输入维护者" />
                  </Form.Item>
                </Col>
              </Row>
              
              <Form.Item 
                label="图标" 
                name="icon"
              >
                <Input placeholder="请输入图标代码或URL（可选）" />
              </Form.Item>
            </TabPane>
            
            <TabPane tab="YAML模板" key="yaml">
              <Form.Item 
                label="YAML模板" 
                name="yamlTemplate"
                rules={[{ required: true, message: '请输入YAML模板' }]}
              >
                <div style={{ width: '100%' }}>
                  <CodeEditor
                    language="yaml"
                    height="300px"
                    value={yamlContent}
                    onChange={(value) => {
                      setYamlContent(value);
                      templateForm.setFieldsValue({ yamlTemplate: value });
                    }}
                  />
                </div>
              </Form.Item>
              <div style={{ marginBottom: 16 }}>
                <p>
                  <strong>提示：</strong> 使用 <code>{'{{.变量名}}'}</code> 语法定义变量，例如：<code>{'{{.replicas}}'}</code>
                </p>
              </div>
            </TabPane>
            
            <TabPane tab="变量定义" key="variables">
              {variablesList.map((variable, index) => (
                <Card 
                  key={index} 
                  style={{ marginBottom: 16 }} 
                  size="small"
                  title={`变量 ${index + 1}`}
                  extra={
                    <Button 
                      danger 
                      size="small" 
                      onClick={() => handleRemoveVariable(index)}
                    >
                      删除
                    </Button>
                  }
                >
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item label="变量名">
                        <Input 
                          placeholder="变量名，如：replicas" 
                          value={variable.name}
                          onChange={(e) => handleVariableChange(index, 'name', e.target.value)}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="显示名称">
                        <Input 
                          placeholder="显示名称，如：副本数" 
                          value={variable.label}
                          onChange={(e) => handleVariableChange(index, 'label', e.target.value)}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                  
                  <Form.Item label="描述">
                    <Input 
                      placeholder="描述，如：部署的Pod副本数量" 
                      value={variable.description}
                      onChange={(e) => handleVariableChange(index, 'description', e.target.value)}
                    />
                  </Form.Item>
                  
                  <Row gutter={16}>
                    <Col span={8}>
                      <Form.Item label="类型">
                        <Select
                          value={variable.type}
                          onChange={(value) => handleVariableChange(index, 'type', value)}
                        >
                          <Option value="string">字符串</Option>
                          <Option value="number">数字</Option>
                          <Option value="boolean">布尔值</Option>
                          <Option value="select">下拉选择</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="默认值">
                        <Input 
                          placeholder="默认值" 
                          value={variable.default_value}
                          onChange={(e) => handleVariableChange(index, 'default_value', e.target.value)}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="是否必填">
                        <Select
                          value={variable.required}
                          onChange={(value) => handleVariableChange(index, 'required', value)}
                        >
                          <Option value={true}>是</Option>
                          <Option value={false}>否</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>
              ))}
              
              <Button 
                type="dashed" 
                block 
                icon={<PlusOutlined />} 
                onClick={handleAddVariable}
              >
                添加变量
              </Button>
            </TabPane>
          </Tabs>
        </Form>
      </Modal>
    </PageContainer>
  );
};

export default AppMarket; 