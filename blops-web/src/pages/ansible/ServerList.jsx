import React, {PureComponent} from 'react';
import {utils} from "umi";
import {timeFormatToLocal} from "@/utils/time";
import {PageHeaderWrapper} from "@ant-design/pro-layout";
import {Card, Pagination, Table, Tag, Form, Input,Button, Select } from "antd";
import {connect} from "dva";
import moment from 'moment';

import { FormInstance } from 'antd/es/form';
const { Option } = Select;

@connect(({ansible}) => ({
  ansible,
}))
class ServerList extends PureComponent {

  onFinish = (values) => {
    const {dispatch} = this.props;
    dispatch({
      type: "ansible/deploy",
      payload: {
        server: values.server,
        ipaddr: values.ipaddr,
      }
    });
  }

  render() {

    return (
      <PageHeaderWrapper>
        <Card>
          <Form
            name="basic"
            labelCol={{
              span: 4,
            }}
            wrapperCol={{
              span: 20,
            }}
            initialValues={{
              remember: true,
            }}
            onFinish={this.onFinish}
            autoComplete="off"
          >
            <Form.Item
              label="Servername"
              name="server"
              rules={[
                {
                  required: true,
                  message: 'Please input your server!',
                },
              ]}
            >
  <Select
    showSearch
    placeholder="Select a Server"
    optionFilterProp="children"
    // onChange={onChange}
    // onSearch={onSearch}
    filterOption={(input, option) =>
      option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
    }
  >
    <Option value="consul">consul</Option>
    <Option value="prometheus">prometheus</Option>
    <Option value="jdk">jdk</Option>
    <Option value="redis">redis</Option>
    <Option value="zookeeper">zookeeper</Option>
    <Option value="kafka">kafka</Option>
    <Option value="elasticsearch">elasticsearch</Option>
    <Option value="kibana">kibana</Option>
    <Option value="rocketmq">rocketmq</Option>
    <Option value="canal">canal</Option>
    <Option value="node_exporter">node_exporter</Option>
    <Option value="cat">cat</Option>
  </Select>              
            </Form.Item>

            <Form.Item
              label="IPaddress"
              name="ipaddr"
              rules={[
                {
                  required: true,
                  message: 'Please input your ip!',
                },
              ]}
            >
              <Input/>
            </Form.Item>



            <Form.Item
              wrapperCol={{
                offset: 4,
                span: 20,
              }}
            >
              <Button type="primary" htmlType="submit">
                提交
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </PageHeaderWrapper>
    );
  }
}

export default ServerList;
