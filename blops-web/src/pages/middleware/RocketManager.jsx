import React, { useState, useEffect, useMemo } from 'react';
import { Tabs, Empty, Spin, message, Menu, Button, Tooltip } from 'antd';
import { request } from 'umi';
import EmbeddedPage from '@/components/EmbeddedPage';
import { LinkOutlined } from '@ant-design/icons';

const { SubMenu } = Menu;

/**
 * RocketManagerPage 组件
 * 用于展示 RocketMQ Manager 的嵌入式页面，并提供环境和链接切换功能。
 */
const RocketManagerPage = () => {
  const [loading, setLoading] = useState(true);
  const [rocketManagerLinks, setRocketManagerLinks] = useState([]);
  const [activeKey, setActiveKey] = useState('');
  const [loadedLinks, setLoadedLinks] = useState({}); // 跟踪已加载的链接以防止重新加载

  // 使用 useMemo 按环境对链接进行分组
  const groupedLinks = useMemo(() => {
    const grouped = {};
    rocketManagerLinks.forEach(link => {
      const envName = link.envInfo?.name || '未分类';
      if (!grouped[envName]) {
        grouped[envName] = [];
      }
      grouped[envName].push(link);
    });
    return grouped;
  }, [rocketManagerLinks]);

  useEffect(() => {
    fetchRocketManagerLinks();
  }, []);

  /**
   * 获取 RocketMQ Manager 类型的链接列表
   */
  const fetchRocketManagerLinks = async () => {
    setLoading(true);
    try {
      const res = await request('/api/v1/middleware/link?with_env_info=true', {
        method: 'GET',
      });
      
      if (res.result === 'SUCCESS') {
        // 仅过滤 rocket-manager 类型的链接
        const filteredLinks = (res.data || []).filter(link => link.type === 'rocket-manager');
        setRocketManagerLinks(filteredLinks);
        
        // 如果有链接，则设置第一个链接为活动链接
        if (filteredLinks.length > 0) {
          const firstLinkId = filteredLinks[0].id.toString();
          setActiveKey(firstLinkId);
          
          // 标记第一个链接为已加载
          setLoadedLinks(prev => ({
            ...prev,
            [firstLinkId]: true
          }));
        }
      } else {
        message.error('获取 RocketMQ Manager 链接失败');
      }
    } catch (error) {
      message.error('获取 RocketMQ Manager 链接出错');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 处理菜单点击事件，切换活动的链接
   * @param {object} param0 - 包含点击的菜单项 key 的对象
   */
  const handleMenuClick = ({ key }) => {
    setActiveKey(key);
    
    // 点击时标记链接为已加载
    if (!loadedLinks[key]) {
      setLoadedLinks(prev => ({
        ...prev,
        [key]: true
      }));
    }
  };
  
  /**
   * 渲染嵌入式页面内容
   * @returns {JSX.Element} 渲染的 iframe 内容区域
   */
  const renderContent = () => {
    return (
      <div style={{ height: '100%', width: '100%', position: 'relative' }}>
        {rocketManagerLinks.map(link => {
          const linkId = link.id.toString();
          // 仅当链接至少被点击过一次时才创建 iframe
          if (!loadedLinks[linkId]) return null;
          
          return (
            <div 
              key={linkId}
              style={{ 
                height: '100%', 
                width: '100%', 
                position: 'absolute',
                top: 0,
                left: 0,
                visibility: activeKey === linkId ? 'visible' : 'hidden'
              }}
            >
              <EmbeddedPage 
                src={link.url} 
                title={`${link.envInfo?.name || ''} - ${link.name}`} 
              />
            </div>
          );
        })}
      </div>
    );
  };
  
  /**
   * 渲染菜单项标题，包含链接名称和跳转按钮
   * @param {object} link - 链接对象
   * @returns {JSX.Element} 渲染的菜单项标题
   */
  const renderMenuItemTitle = (link) => {
    return (
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
        <span>{link.name}</span>
        <Tooltip title="跳转链接">
          <Button 
            type="text" 
            size="small" 
            icon={<LinkOutlined />}
            onClick={(e) => {
              e.stopPropagation(); // 防止触发菜单项点击
              window.open(link.url, '_blank'); // 打开新标签页跳转
            }}
          />
        </Tooltip>
      </div>
    );
  };

  return (
    <div style={{ height: 'calc(100vh - 120px)', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
      {loading ? (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" tip="加载中..." />
        </div>
      ) : rocketManagerLinks.length === 0 ? (
        <Empty 
          description="未找到 RocketMQ Manager 链接，请先在环境及链接页面配置 RocketMQ Manager 链接" 
          style={{ margin: '100px 0' }}
        />
      ) : (
        <div style={{ display: 'flex', height: '100%' }}>
          <div style={{ width: 200, borderRight: '1px solid #f0f0f0' }}>
            <Menu
              mode="inline"
              selectedKeys={[activeKey]}
              onClick={handleMenuClick}
              style={{ height: '100%', borderRight: 0 }}
            >
              {Object.entries(groupedLinks).map(([envName, links]) => (
                <SubMenu key={envName} title={envName}>
                  {links.map(link => (
                    <Menu.Item key={link.id.toString()}>
                      {renderMenuItemTitle(link)}
                    </Menu.Item>
                  ))}
                </SubMenu>
              ))}
            </Menu>
          </div>
          <div style={{ flex: 1, padding: '0 0 0 16px', height: '100%', overflow: 'auto' }}>
            {renderContent()}
          </div>
        </div>
      )}
    </div>
  );
};

export default RocketManagerPage;