import React, { useState, useEffect, useMemo } from 'react';
import { Tabs, Empty, Spin, message, Menu, Button, Tooltip } from 'antd';
import { request } from 'umi';
import EmbeddedPage from '@/components/EmbeddedPage';
import { LinkOutlined } from '@ant-design/icons'; // Changed from CopyOutlined

const { TabPane } = Tabs;
const { SubMenu } = Menu;

const PrometheusPage = () => {
  const [loading, setLoading] = useState(true);
  const [prometheusLinks, setPrometheusLinks] = useState([]);
  const [activeKey, setActiveKey] = useState('');
  // Track loaded links to prevent reloading
  const [loadedLinks, setLoadedLinks] = useState({});

  // Group links by environment using useMemo
  const groupedLinks = useMemo(() => {
    const grouped = {};
    prometheusLinks.forEach(link => {
      const envName = link.envInfo?.name || '未分类';
      if (!grouped[envName]) {
        grouped[envName] = [];
      }
      grouped[envName].push(link);
    });
    return grouped;
  }, [prometheusLinks]);

  useEffect(() => {
    fetchPrometheusLinks();
  }, []);

  const fetchPrometheusLinks = async () => {
    setLoading(true);
    try {
      const res = await request('/api/v1/middleware/link?with_env_info=true', {
        method: 'GET',
      });
      
      if (res.result === 'SUCCESS') {
        // Filter to only prometheus type links
        const filteredLinks = (res.data || []).filter(link => link.type === 'prometheus');
        setPrometheusLinks(filteredLinks);
        
        // Set the first link as active if available
        if (filteredLinks.length > 0) {
          const firstLinkId = filteredLinks[0].id.toString();
          setActiveKey(firstLinkId);
          
          // Mark the first link as loaded
          setLoadedLinks(prev => ({
            ...prev,
            [firstLinkId]: true
          }));
        }
      } else {
        message.error('获取Prometheus链接失败');
      }
    } catch (error) {
      message.error('获取Prometheus链接出错');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleMenuClick = ({ key }) => {
    setActiveKey(key);
    
    // Mark the link as loaded when clicked
    if (!loadedLinks[key]) {
      setLoadedLinks(prev => ({
        ...prev,
        [key]: true
      }));
    }
    
    // Removed commented out code for copyToClipboard
  };
  
  // Function to copy text to clipboard - REMOVED as it's no longer used
  // const copyToClipboard = (text, name) => {
  //   navigator.clipboard.writeText(text).then(
  //     () => {
  //       message.success(`已复制 ${name} 链接到剪贴板`);
  //     },
  //     (err) => {
  //       message.error('复制链接失败');
  //       console.error('复制失败:', err);
  //     }
  //   );
  // };

  const renderContent = () => {
    return (
      <div style={{ height: '100%', width: '100%', position: 'relative' }}>
        {prometheusLinks.map(link => {
          const linkId = link.id.toString();
          // Only create the iframe if the link has been clicked at least once
          if (!loadedLinks[linkId]) return null;
          
          return (
            <div 
              key={linkId}
              style={{ 
                height: '100%', 
                width: '100%', 
                position: 'absolute',
                top: 0,
                left: 0,
                visibility: activeKey === linkId ? 'visible' : 'hidden'
              }}
            >
              <EmbeddedPage 
                src={link.url} 
                title={`${link.envInfo?.name || ''} - ${link.name}`} 
              />
            </div>
          );
        })}
      </div>
    );
  };
  
  // Custom title renderer for menu items with copy button
  const renderMenuItemTitle = (link) => {
    return (
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
        <span>{link.name}</span>
        <Tooltip title="跳转链接"> {/* Changed title */}
          <Button 
            type="text" 
            size="small" 
            icon={<LinkOutlined />} /* Changed icon */
            onClick={(e) => {
              e.stopPropagation(); // 防止触发菜单项点击
              window.open(link.url, '_blank'); // Changed to open link
            }}
          />
        </Tooltip>
      </div>
    );
  };

  return (
    <div style={{ height: 'calc(100vh - 120px)', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
      {loading ? (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" tip="加载中..." />
        </div>
      ) : prometheusLinks.length === 0 ? (
        <Empty 
          description="未找到Prometheus链接，请先在环境及链接页面配置Prometheus链接" 
          style={{ margin: '100px 0' }}
        />
      ) : (
        <div style={{ display: 'flex', height: '100%' }}>
          <div style={{ width: 200, borderRight: '1px solid #f0f0f0' }}>
            <Menu
              mode="inline"
              selectedKeys={[activeKey]}
              onClick={handleMenuClick}
              style={{ height: '100%', borderRight: 0 }}
            >
              {Object.entries(groupedLinks).map(([envName, links]) => (
                <SubMenu key={envName} title={envName}>
                  {links.map(link => (
                    <Menu.Item key={link.id.toString()}>
                      {renderMenuItemTitle(link)}
                    </Menu.Item>
                  ))}
                </SubMenu>
              ))}
            </Menu>
          </div>
          <div style={{ flex: 1, padding: '0 0 0 16px', height: '100%', overflow: 'auto' }}>
            {renderContent()}
          </div>
        </div>
      )}
    </div>
  );
};

export default PrometheusPage;