import React, { useState, useEffect } from 'react';
import { Card, Tabs, Button, Table, Modal, Form, Input, Space, Select, Popconfirm, message } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { request } from 'umi';

const { TabPane } = Tabs;
const { Option } = Select;

const EnvLinks = () => {
  // 状态管理
  const [envList, setEnvList] = useState([]);
  const [linkList, setLinkList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [envModalVisible, setEnvModalVisible] = useState(false);
  const [linkModalVisible, setLinkModalVisible] = useState(false);
  const [currentEnv, setCurrentEnv] = useState(null);
  const [currentLink, setCurrentLink] = useState(null);
  const [envForm] = Form.useForm();
  const [linkForm] = Form.useForm();
  const [activeKey, setActiveKey] = useState('1');

  // 初始化加载数据
  useEffect(() => {
    fetchEnvList();
    fetchLinkList();
  }, []);

  // 获取环境列表
  const fetchEnvList = async () => {
    setLoading(true);
    try {
      const res = await request('/api/v1/middleware/env', {
        method: 'GET',
      });
      if (res.result === 'SUCCESS') {
        setEnvList(res.data || []);
      } else {
        message.error('获取环境列表失败');
      }
    } catch (error) {
      message.error('获取环境列表出错');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // 获取链接列表
  const fetchLinkList = async () => {
    setLoading(true);
    try {
      const res = await request('/api/v1/middleware/link?with_env_info=true', {
        method: 'GET',
      });
      if (res.result === 'SUCCESS') {
        setLinkList(res.data || []);
      } else {
        message.error('获取链接列表失败');
      }
    } catch (error) {
      message.error('获取链接列表出错');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // 添加/编辑环境
  const handleEnvOk = async () => {
    try {
      const values = await envForm.validateFields();
      setLoading(true);
      
      const url = currentEnv ? '/api/v1/middleware/env' : '/api/v1/middleware/env';
      const method = currentEnv ? 'PUT' : 'POST';
      const data = currentEnv ? { ...values, id: currentEnv.id } : values;
      
      const res = await request(url, {
        method,
        data,
      });
      
      if (res.result === 'SUCCESS') {
        message.success(currentEnv ? '更新环境成功' : '添加环境成功');
        setEnvModalVisible(false);
        envForm.resetFields();
        setCurrentEnv(null);
        fetchEnvList();
      } else {
        message.error(currentEnv ? '更新环境失败' : '添加环境失败');
      }
    } catch (error) {
      message.error('操作失败');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // 添加/编辑链接
  const handleLinkOk = async () => {
    try {
      const values = await linkForm.validateFields();
      setLoading(true);
      
      const url = currentLink ? '/api/v1/middleware/link' : '/api/v1/middleware/link';
      const method = currentLink ? 'PUT' : 'POST';
      const data = currentLink ? { ...values, id: currentLink.id } : values;
      
      const res = await request(url, {
        method,
        data,
      });
      
      if (res.result === 'SUCCESS') {
        message.success(currentLink ? '更新链接成功' : '添加链接成功');
        setLinkModalVisible(false);
        linkForm.resetFields();
        setCurrentLink(null);
        fetchLinkList();
      } else {
        message.error(currentLink ? '更新链接失败' : '添加链接失败');
      }
    } catch (error) {
      message.error('操作失败');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // 删除环境
  const handleDeleteEnv = async (id) => {
    setLoading(true);
    try {
      const res = await request(`/api/v1/middleware/env/${id}`, {
        method: 'DELETE',
      });
      
      if (res.result === 'SUCCESS') {
        message.success('删除环境成功');
        fetchEnvList();
      } else {
        message.error('删除环境失败');
      }
    } catch (error) {
      message.error('删除环境出错');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // 删除链接
  const handleDeleteLink = async (id) => {
    setLoading(true);
    try {
      const res = await request(`/api/v1/middleware/link/${id}`, {
        method: 'DELETE',
      });
      
      if (res.result === 'SUCCESS') {
        message.success('删除链接成功');
        fetchLinkList();
      } else {
        message.error('删除链接失败');
      }
    } catch (error) {
      message.error('删除链接出错');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // 编辑环境
  const handleEditEnv = (record) => {
    setCurrentEnv(record);
    envForm.setFieldsValue({
      name: record.name,
      description: record.description || '',
    });
    setEnvModalVisible(true);
  };

  // 编辑链接
  const handleEditLink = (record) => {
    setCurrentLink(record);
    linkForm.setFieldsValue({
      name: record.name,
      type: record.type,
      url: record.url,
      env: record.env,
      description: record.description || '',
    });
    setLinkModalVisible(true);
  };

  // 环境表格列定义
  const envColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '环境名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      render: (_, record) => (
        <Space>
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => handleEditEnv(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此环境吗？关联的链接也将被删除。"
            onConfirm={() => handleDeleteEnv(record.id)}
            okText="是"
            cancelText="否"
          >
            <Button danger icon={<DeleteOutlined />} size="small">
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 链接表格列定义
  const linkColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '链接名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: '环境',
      dataIndex: 'envInfo',
      key: 'envInfo',
      render: (envInfo) => envInfo?.name,
    },
    {
      title: 'URL',
      dataIndex: 'url',
      key: 'url',
      ellipsis: true,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      render: (_, record) => (
        <Space>
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => handleEditLink(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此链接吗？"
            onConfirm={() => handleDeleteLink(record.id)}
            okText="是"
            cancelText="否"
          >
            <Button danger icon={<DeleteOutlined />} size="small">
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card>
        <Tabs 
          activeKey={activeKey} 
          onChange={setActiveKey}
          tabBarExtraContent={
            activeKey === '1' ? (
              <Button 
                type="primary" 
                icon={<PlusOutlined />} 
                onClick={() => {
                  setCurrentEnv(null);
                  envForm.resetFields();
                  setEnvModalVisible(true);
                }}
              >
                添加环境
              </Button>
            ) : (
              <Button 
                type="primary" 
                icon={<PlusOutlined />} 
                onClick={() => {
                  setCurrentLink(null);
                  linkForm.resetFields();
                  setLinkModalVisible(true);
                }}
              >
                添加链接
              </Button>
            )
          }
        >
          <TabPane tab="环境管理" key="1">
            <Table 
              rowKey="id"
              columns={envColumns} 
              dataSource={envList} 
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
          <TabPane tab="链接管理" key="2">
            <Table 
              rowKey="id"
              columns={linkColumns} 
              dataSource={linkList} 
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 环境表单模态框 */}
      <Modal
        title={currentEnv ? '编辑环境' : '添加环境'}
        open={envModalVisible}
        onOk={handleEnvOk}
        onCancel={() => {
          setEnvModalVisible(false);
          setCurrentEnv(null);
          envForm.resetFields();
        }}
        confirmLoading={loading}
      >
        <Form
          form={envForm}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="环境名称"
            rules={[{ required: true, message: '请输入环境名称' }]}
          >
            <Input placeholder="如：阿里云、华为云等" />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea rows={3} placeholder="环境描述信息" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 链接表单模态框 */}
      <Modal
        title={currentLink ? '编辑链接' : '添加链接'}
        open={linkModalVisible}
        onOk={handleLinkOk}
        onCancel={() => {
          setLinkModalVisible(false);
          setCurrentLink(null);
          linkForm.resetFields();
        }}
        confirmLoading={loading}
      >
        <Form
          form={linkForm}
          layout="vertical"
        >
          <Form.Item
            name="env"
            label="所属环境"
            rules={[{ required: true, message: '请选择所属环境' }]}
          >
            <Select placeholder="选择链接所属环境">
              {envList.map(env => (
                <Option key={env.id} value={env.id}>{env.name}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="name"
            label="链接名称"
            rules={[{ required: true, message: '请输入链接名称' }]}
          >
            <Input placeholder="链接名称" />
          </Form.Item>
          <Form.Item
            name="type"
            label="链接类型"
            rules={[{ required: true, message: '请选择链接类型' }]}
          >
            <Select placeholder="选择链接类型">
              <Option value="kibana">Kibana</Option>
              <Option value="prometheus">Prometheus</Option>
              <Option value="kafka-manager">Kafka Manager</Option>
              <Option value="rocket-manager">RocketMQ Manager</Option> {/* 新增的选项 */}
              <Option value="other">其他</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="url"
            label="链接URL"
            rules={[{ required: true, message: '请输入链接URL' }]}
          >
            <Input placeholder="例如：https://example.com/kibana" />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea rows={2} placeholder="链接描述信息" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default EnvLinks;