import React, { useState } from 'react';
import { Card, Space, Button, Typography, Divider, Tag, Alert, Row, Col } from 'antd';
import { 
  RobotOutlined, 
  BulbOutlined, 
  ThunderboltOutlined,
  EyeOutlined,
  SettingOutlined,
  MobileOutlined,
  KeyboardOutlined,
  AccessibilityOutlined
} from '@ant-design/icons';
import AIAssistant from '@/components/AIAssistant';
import styles from './index.less';

const { Title, Paragraph, Text } = Typography;

const AIAssistantDemo: React.FC = () => {
  const [theme, setTheme] = useState<'light' | 'dark'>('light');
  const [enableDragResize, setEnableDragResize] = useState(true);

  const features = [
    {
      icon: <EyeOutlined />,
      title: '现代化视觉设计',
      description: '渐变背景、卡片式消息气泡、优雅阴影效果',
      color: '#1890ff'
    },
    {
      icon: <SettingOutlined />,
      title: '深色/浅色主题',
      description: '支持主题切换，自动保存用户偏好设置',
      color: '#722ed1'
    },
    {
      icon: <ThunderboltOutlined />,
      title: '打字动画效果',
      description: '模拟AI实时回复，提升交互体验',
      color: '#fa541c'
    },
    {
      icon: <KeyboardOutlined />,
      title: '快捷键支持',
      description: 'Ctrl+K打开，Ctrl+Enter发送，Esc关闭',
      color: '#13c2c2'
    },
    {
      icon: <MobileOutlined />,
      title: '响应式设计',
      description: '完美适配桌面、平板、手机等设备',
      color: '#52c41a'
    },
    {
      icon: <AccessibilityOutlined />,
      title: '无障碍设计',
      description: '符合WCAG 2.1标准，支持屏幕阅读器',
      color: '#eb2f96'
    }
  ];

  const shortcuts = [
    { key: 'Ctrl + K', description: '打开/聚焦AI助手' },
    { key: 'Ctrl + Enter', description: '发送消息' },
    { key: 'Shift + Enter', description: '换行' },
    { key: 'Esc', description: '关闭AI助手' },
  ];

  return (
    <div className={styles.demoContainer}>
      <div className={styles.header}>
        <Title level={1} className={styles.title}>
          <RobotOutlined className={styles.titleIcon} />
          AI助手组件演示
        </Title>
        <Paragraph className={styles.subtitle}>
          体验全新升级的AI助手界面，参考业界主流AI聊天工具的最佳设计实践
        </Paragraph>
      </div>

      <Alert
        message="使用提示"
        description="点击右下角的AI助手按钮开始体验，或使用快捷键 Ctrl+K 快速打开"
        type="info"
        showIcon
        className={styles.alert}
      />

      <Row gutter={[24, 24]}>
        <Col xs={24} lg={16}>
          <Card title="✨ 核心功能特性" className={styles.featureCard}>
            <Row gutter={[16, 16]}>
              {features.map((feature, index) => (
                <Col xs={24} sm={12} key={index}>
                  <Card size="small" className={styles.miniCard}>
                    <Space>
                      <div 
                        className={styles.featureIcon}
                        style={{ color: feature.color }}
                      >
                        {feature.icon}
                      </div>
                      <div>
                        <Text strong>{feature.title}</Text>
                        <br />
                        <Text type="secondary" className={styles.featureDesc}>
                          {feature.description}
                        </Text>
                      </div>
                    </Space>
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="⌨️ 快捷键" className={styles.shortcutCard}>
            <Space direction="vertical" style={{ width: '100%' }}>
              {shortcuts.map((shortcut, index) => (
                <div key={index} className={styles.shortcutItem}>
                  <Tag color="blue" className={styles.shortcutKey}>
                    {shortcut.key}
                  </Tag>
                  <Text className={styles.shortcutDesc}>
                    {shortcut.description}
                  </Text>
                </div>
              ))}
            </Space>
          </Card>

          <Card title="🎛️ 演示控制" className={styles.controlCard}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div className={styles.controlItem}>
                <Text>主题模式：</Text>
                <Space>
                  <Button 
                    size="small"
                    type={theme === 'light' ? 'primary' : 'default'}
                    onClick={() => setTheme('light')}
                  >
                    浅色
                  </Button>
                  <Button 
                    size="small"
                    type={theme === 'dark' ? 'primary' : 'default'}
                    onClick={() => setTheme('dark')}
                  >
                    深色
                  </Button>
                </Space>
              </div>
              
              <div className={styles.controlItem}>
                <Text>拖拽调整：</Text>
                <Button 
                  size="small"
                  type={enableDragResize ? 'primary' : 'default'}
                  onClick={() => setEnableDragResize(!enableDragResize)}
                >
                  {enableDragResize ? '已启用' : '已禁用'}
                </Button>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>

      <Divider />

      <Card title="🚀 新功能亮点" className={styles.highlightCard}>
        <Row gutter={[24, 24]}>
          <Col xs={24} md={8}>
            <div className={styles.highlight}>
              <BulbOutlined className={styles.highlightIcon} />
              <Title level={4}>智能交互</Title>
              <Paragraph>
                支持消息状态指示、打字动画、消息反馈等智能交互功能，
                提供类似ChatGPT的专业体验。
              </Paragraph>
            </div>
          </Col>
          
          <Col xs={24} md={8}>
            <div className={styles.highlight}>
              <ThunderboltOutlined className={styles.highlightIcon} />
              <Title level={4}>性能优化</Title>
              <Paragraph>
                采用React.memo、useCallback等优化技术，
                支持虚拟滚动和懒加载，确保流畅的用户体验。
              </Paragraph>
            </div>
          </Col>
          
          <Col xs={24} md={8}>
            <div className={styles.highlight}>
              <AccessibilityOutlined className={styles.highlightIcon} />
              <Title level={4}>无障碍支持</Title>
              <Paragraph>
                完整的键盘导航、屏幕阅读器支持、高对比度模式，
                符合WCAG 2.1无障碍设计标准。
              </Paragraph>
            </div>
          </Col>
        </Row>
      </Card>

      <Card title="📱 响应式演示" className={styles.responsiveCard}>
        <Paragraph>
          AI助手组件完美适配不同设备尺寸：
        </Paragraph>
        <Space wrap>
          <Tag color="green">桌面端 (>768px): 完整功能 + 拖拽调整</Tag>
          <Tag color="orange">平板端 (481-768px): 简化操作 + 核心功能</Tag>
          <Tag color="red">手机端 (≤480px): 全屏显示 + 触控优化</Tag>
        </Space>
      </Card>

      {/* AI助手组件 */}
      <AIAssistant
        defaultTheme={theme}
        enableDragResize={enableDragResize}
        maxWidth={600}
        minWidth={320}
      />
    </div>
  );
};

export default AIAssistantDemo;
