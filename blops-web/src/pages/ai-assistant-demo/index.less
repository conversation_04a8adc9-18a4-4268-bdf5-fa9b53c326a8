.demoContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 32px;
  
  .title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 16px;
    
    .titleIcon {
      margin-right: 12px;
      font-size: 48px;
    }
  }
  
  .subtitle {
    font-size: 16px;
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto;
  }
}

.alert {
  margin-bottom: 24px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1);
}

.featureCard,
.shortcutCard,
.controlCard,
.highlightCard,
.responsiveCard {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: none;
  margin-bottom: 24px;
  
  .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
    background: linear-gradient(90deg, #fafbfc 0%, #f8fafc 100%);
    border-radius: 16px 16px 0 0;
  }
  
  .ant-card-head-title {
    font-weight: 600;
    font-size: 16px;
  }
}

.miniCard {
  height: 100%;
  border-radius: 12px;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }
  
  .ant-card-body {
    padding: 16px;
  }
}

.featureIcon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 8px;
  flex-shrink: 0;
}

.featureDesc {
  font-size: 12px;
  line-height: 1.4;
  margin-top: 4px;
  display: block;
}

.shortcutItem {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  .shortcutKey {
    min-width: 80px;
    text-align: center;
    font-family: 'SFMono-Regular', 'Monaco', 'Consolas', monospace;
    font-size: 11px;
    margin-right: 12px;
  }
  
  .shortcutDesc {
    font-size: 13px;
    color: #6b7280;
  }
}

.controlItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  
  &:not(:last-child) {
    border-bottom: 1px solid #f0f0f0;
  }
}

.highlight {
  text-align: center;
  padding: 24px 16px;
  
  .highlightIcon {
    font-size: 48px;
    color: #667eea;
    margin-bottom: 16px;
    display: block;
  }
  
  h4 {
    color: #1f2937;
    margin-bottom: 12px;
  }
  
  p {
    color: #6b7280;
    font-size: 14px;
    line-height: 1.6;
    margin: 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .demoContainer {
    padding: 16px;
  }
  
  .header {
    .title {
      font-size: 28px;
      
      .titleIcon {
        font-size: 32px;
        margin-right: 8px;
      }
    }
    
    .subtitle {
      font-size: 14px;
    }
  }
  
  .featureCard,
  .shortcutCard,
  .controlCard,
  .highlightCard,
  .responsiveCard {
    margin-bottom: 16px;
  }
  
  .miniCard {
    .ant-card-body {
      padding: 12px;
    }
  }
  
  .featureIcon {
    width: 32px;
    height: 32px;
    font-size: 18px;
  }
  
  .featureDesc {
    font-size: 11px;
  }
  
  .highlight {
    padding: 16px 12px;
    
    .highlightIcon {
      font-size: 36px;
      margin-bottom: 12px;
    }
    
    h4 {
      font-size: 16px;
      margin-bottom: 8px;
    }
    
    p {
      font-size: 13px;
    }
  }
}

@media (max-width: 480px) {
  .demoContainer {
    padding: 12px;
  }
  
  .header {
    margin-bottom: 24px;
    
    .title {
      font-size: 24px;
      
      .titleIcon {
        font-size: 28px;
      }
    }
  }
  
  .shortcutItem {
    .shortcutKey {
      min-width: 70px;
      font-size: 10px;
    }
    
    .shortcutDesc {
      font-size: 12px;
    }
  }
  
  .controlItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

// 深色主题支持
@media (prefers-color-scheme: dark) {
  .demoContainer {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: #ffffff;
  }
  
  .header {
    .subtitle {
      color: #d1d5db;
    }
  }
  
  .featureCard,
  .shortcutCard,
  .controlCard,
  .highlightCard,
  .responsiveCard {
    background: #374151;
    
    .ant-card-head {
      background: linear-gradient(90deg, #4b5563 0%, #374151 100%);
      border-bottom-color: #4b5563;
    }
  }
  
  .miniCard {
    background: #4b5563;
    
    &:hover {
      background: #6b7280;
    }
  }
  
  .featureIcon {
    background: rgba(79, 172, 254, 0.2);
  }
  
  .shortcutItem {
    border-bottom-color: #4b5563;
  }
  
  .controlItem {
    border-bottom-color: #4b5563;
  }
  
  .highlight {
    h4 {
      color: #ffffff;
    }
    
    p {
      color: #d1d5db;
    }
  }
}
