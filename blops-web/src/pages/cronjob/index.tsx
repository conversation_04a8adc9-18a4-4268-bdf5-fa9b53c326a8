import React, { useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Card, Select, Table, Button, Modal, Form, Input, message, Space, Spin, Tag, Tooltip, Popconfirm, Tabs } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, ReloadOutlined, ExportOutlined, PlayCircleOutlined } from '@ant-design/icons';
import cluster from '@/services/cluster';
import * as cronjobService from '@/services/cronjob';
import CodeEditor from '@/components/CodeEditor';
import yaml from 'js-yaml';
import moment from 'moment';
import CronJobExecutions from '@/components/CronJobExecutions';
import CronJobForm from '@/components/CronJobForm';
import { formatDate } from '@/utils/date';

const { Option } = Select;
const { TabPane } = Tabs;

// 固定的集群选项
const clusterList = [
  { name: 'ali-test', displayName: 'ali-test' },
  { name: 'ali-prod', displayName: 'ali-prod' },
  { name: 'ali-guotai', displayName: 'ali-guotai' },
  { name: 'hwyx-prod', displayName: 'hwyx-prod' },
  { name: 'ali-v3', displayName: 'ali-v3' },
];

const CronJobManagement: React.FC = () => {
  // 状态变量
  const [selectedCluster, setSelectedCluster] = useState<string>('');
  const [selectedNamespace, setSelectedNamespace] = useState<string>('default');
  const [cronJobs, setCronJobs] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [detailVisible, setDetailVisible] = useState<boolean>(false);
  const [createVisible, setCreateVisible] = useState<boolean>(false);
  const [editVisible, setEditVisible] = useState<boolean>(false);
  const [currentCronJob, setCurrentCronJob] = useState<any>(null);
  const [yamlContent, setYamlContent] = useState<string>('');
  const [searchText, setSearchText] = useState<string>('');
  const [executionsVisible, setExecutionsVisible] = useState<boolean>(false);
  const [selectedCronJob, setSelectedCronJob] = useState<any>(null);
  const [formMode, setFormMode] = useState<'form' | 'yaml'>('form');
  const [initialActiveTab, setInitialActiveTab] = useState<string>('jobs');

  // 处理集群变更
  const handleClusterChange = (value: string) => {
    setSelectedCluster(value);
    // 直接设置命名空间为 default
    setSelectedNamespace('default');
    fetchCronJobs(value, 'default');
  };

  // 获取CronJob列表
  const fetchCronJobs = async (clusterName: string, namespace: string) => {
    if (!clusterName || !namespace) return;
    try {
      setLoading(true);
      const res = await cronjobService.list_cronjobs({ cluster: clusterName, namespace });
      if (res.result === 'SUCCESS') {
        // 处理API返回的数据
        const processedData = (res.data || []).map((item: any) => {
          if (typeof item === 'string') {
            try {
              return JSON.parse(item);
            } catch (e) {
              console.error('解析CronJob数据失败:', e);
              return item;
            }
          }
          return item;
        });
        
        // 按创建时间排序，最新的在前面
        processedData.sort((a: Record<string, any>, b: Record<string, any>) => {
          const timeA = a.creationTimestamp || '';
          const timeB = b.creationTimestamp || '';
          return timeB.localeCompare(timeA); // 降序排列
        });
        
        setCronJobs(processedData);
      } else {
        message.error(res.message || '获取CronJob列表失败');
      }
    } catch (error) {
      console.error('获取CronJob列表失败:', error);
      message.error('获取CronJob列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 查看CronJob详情
  const viewCronJobDetail = async (record: any) => {
    try {
      setLoading(true);
      const res = await cronjobService.get_cronjob({
        cluster: selectedCluster,
        namespace: selectedNamespace,
        name: record.name,
      });
      
      if (res.result === 'SUCCESS') {
        setCurrentCronJob(res.data);
        // 将对象转换为YAML格式
        const yamlStr = yaml.dump(res.data);
        setYamlContent(yamlStr);
        setDetailVisible(true);
      } else {
        message.error(res.message || '获取CronJob详情失败');
      }
    } catch (error) {
      console.error('获取CronJob详情失败:', error);
      message.error('获取CronJob详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 创建CronJob
  const showCreateModal = () => {
    // 设置默认的YAML模板，固定命名空间为 default
    const defaultYaml = `apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: example-cronjob
  namespace: default
spec:
  schedule: "*/5 * * * *"
  jobTemplate:
    spec:
      backoffLimit: 0
      template:
        spec:
          containers:
          - name: hello
            image: nginx
            command:
            - /bin/bash
            - -c
            args:
            - echo "Hello World"
          restartPolicy: Never`;
    
    setYamlContent(defaultYaml);
    setFormMode('form'); // 默认使用表单模式
    setCreateVisible(true);
  };

  // 提交创建CronJob
  const handleCreateSubmit = async () => {
    try {
      setLoading(true);
      // 解析YAML获取name
      const yamlObj = yaml.load(yamlContent) as any;
      const name = yamlObj?.metadata?.name;
      
      if (!name) {
        message.error('YAML中未找到有效的name字段');
        setLoading(false);
        return;
      }
      
      const res = await cronjobService.create_cronjob({
        cluster: selectedCluster,
        namespace: selectedNamespace,
        name: name, // 添加name参数
        yamlData: yamlContent,
      });
      
      if (res.result === 'SUCCESS') {
        message.success('创建CronJob成功');
        setCreateVisible(false);
        fetchCronJobs(selectedCluster, selectedNamespace);
      } else {
        message.error(res.message || '创建CronJob失败');
      }
    } catch (error) {
      console.error('创建CronJob失败:', error);
      message.error('创建CronJob失败');
    } finally {
      setLoading(false);
    }
  };

  // 编辑CronJob
  const showEditModal = async (record: any) => {
    try {
      setLoading(true);
      const res = await cronjobService.get_cronjob({
        cluster: selectedCluster,
        namespace: selectedNamespace,
        name: record.name,
      });
      
      if (res.result === 'SUCCESS') {
        setCurrentCronJob(res.data);
        // 将对象转换为YAML格式
        const yamlStr = yaml.dump(res.data);
        setYamlContent(yamlStr);
        setEditVisible(true);
      } else {
        message.error(res.message || '获取CronJob详情失败');
      }
    } catch (error) {
      console.error('获取CronJob详情失败:', error);
      message.error('获取CronJob详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 提交编辑CronJob
  const handleEditSubmit = async () => {
    if (!currentCronJob) return;
    
    try {
      setLoading(true);
      console.log('提交更新 YAML:', yamlContent); // 添加日志，检查 YAML 内容
      
      const res = await cronjobService.update_cronjob({
        cluster: selectedCluster,
        namespace: selectedNamespace,
        name: currentCronJob.name,
        yamlData: yamlContent,
      });
      
      if (res.result === 'SUCCESS') {
        message.success('更新CronJob成功');
        setEditVisible(false);
        fetchCronJobs(selectedCluster, selectedNamespace);
      } else {
        message.error(res.message || '更新CronJob失败');
        console.error('更新失败响应:', res); // 添加错误日志
      }
    } catch (error) {
      console.error('更新CronJob失败:', error);
      message.error('更新CronJob失败: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      setLoading(false);
    }
  };

  // 删除CronJob
  const handleDelete = async (record: any) => {
    try {
      setLoading(true);
      
      // 先删除关联的所有Job
      const deleteJobsRes = await cronjobService.delete_cronjob_jobs({
        cluster: selectedCluster,
        namespace: selectedNamespace,
        cronjobName: record.name,
      });
      
      if (deleteJobsRes.result !== 'SUCCESS' && deleteJobsRes.code !== 200 && deleteJobsRes.code !== 0) {
        message.warn('删除关联的Job失败，将继续删除CronJob');
      }
      
      // 删除CronJob本身
      const res = await cronjobService.delete_cronjob({
        cluster: selectedCluster,
        namespace: selectedNamespace,
        name: record.name,
      });
      
      if (res.result === 'SUCCESS') {
        message.success('删除CronJob及其关联Job成功');
        fetchCronJobs(selectedCluster, selectedNamespace);
      } else {
        message.error(res.message || '删除CronJob失败');
      }
    } catch (error) {
      console.error('删除CronJob失败:', error);
      message.error('删除CronJob失败');
    } finally {
      setLoading(false);
    }
  };

  // 导出YAML
  const handleExportYaml = () => {
    if (!yamlContent) return;
    
    const blob = new Blob([yamlContent], { type: 'text/yaml' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${currentCronJob?.name || 'cronjob'}.yaml`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 查看执行结果
  const handleViewExecutions = (record: any) => {
    setSelectedCronJob(record);
    setInitialActiveTab('jobs'); // 默认显示执行记录标签页
    setExecutionsVisible(true);
  };

  // 添加 YAML 生成处理函数
  const handleGenerateYaml = (yamlContent: string) => {
    console.log('表单生成的 YAML:', yamlContent); // 添加日志，用于调试
    setYamlContent(yamlContent);
  };

  // 添加触发执行的处理函数
  const handleTriggerExecution = async (record: any) => {
    try {
      setLoading(true);
      const res = await cronjobService.trigger_cronjob({
        cluster: selectedCluster,
        namespace: selectedNamespace,
        name: record.name,
      });
      
      if (res.code === 0 || res.code === 200) {
        message.success(`${record.name} 触发执行成功`);
        // 自动显示执行结果，并默认显示执行记录标签页
        setInitialActiveTab('jobs');
        setTimeout(() => {
          setSelectedCronJob(record);
          setExecutionsVisible(true);
        }, 1000);
      } else {
        message.error(res.message || '触发执行失败');
      }
    } catch (error) {
      console.error('触发 CronJob 执行失败:', error);
      message.error('触发执行失败');
    } finally {
      setLoading(false);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => <span>{text}</span>,
    },
    {
      title: '调度表达式',
      dataIndex: 'schedule',
      key: 'schedule',
      render: (text: string) => <span>{text}</span>,
    },
    {
      title: '暂停状态',
      dataIndex: 'suspend',
      key: 'suspend',
      render: (suspend: boolean) => (
        <Tag color={suspend ? 'orange' : 'green'}>
          {suspend ? '已暂停' : '运行中'}
        </Tag>
      ),
    },
    {
      title: '活跃任务',
      dataIndex: 'active',
      key: 'active',
      render: (count: number) => <span>{count || 0}</span>,
    },
    {
      title: '最后调度时间',
      dataIndex: 'lastSchedule',
      key: 'lastSchedule',
      render: (text: string) => <span>{text ? formatDate(text) : '-'}</span>,
    },
    {
      title: '创建时间',
      dataIndex: 'creationTimestamp',
      key: 'creationTimestamp',
      render: (text: string) => <span>{text ? formatDate(text) : '-'}</span>,
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: any) => (
        <Space size="middle">
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              onClick={() => viewCronJobDetail(record)} 
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              onClick={() => showEditModal(record)} 
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除这个定时任务吗？删除后将同时删除所有关联的历史任务(Job)"
              onConfirm={() => handleDelete(record)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="text" danger icon={<DeleteOutlined />} />
            </Popconfirm>
          </Tooltip>
          <Tooltip title="执行结果">
            <Button type="link" onClick={() => handleViewExecutions(record)}>
              执行结果
            </Button>
          </Tooltip>
          <Tooltip title="立即执行">
            <Button 
              type="primary" 
              size="small"
              icon={<PlayCircleOutlined />}
              onClick={() => handleTriggerExecution(record)}
            >
              执行
            </Button>
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 初始加载
  useEffect(() => {
    // 如果有默认集群，可以在这里设置
    if (clusterList.length > 0) {
      handleClusterChange(clusterList[0].name);
    }
  }, []);

  // 过滤CronJob列表
  const filteredCronJobs = cronJobs.filter((job) => {
    if (!searchText) return true;
    return job.name.toLowerCase().includes(searchText.toLowerCase());
  });

  return (
    <PageContainer>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space size="large">
            <div>
              <span style={{ marginRight: 8 }}>集群：</span>
              <Select
                style={{ width: 200 }}
                value={selectedCluster}
                onChange={handleClusterChange}
                placeholder="请选择集群"
              >
                {clusterList.map((cluster) => (
                  <Option key={cluster.name} value={cluster.name}>
                    {cluster.displayName}
                  </Option>
                ))}
              </Select>
            </div>
            
            <div>
              <span style={{ marginRight: 8 }}>命名空间：</span>
              <Input
                style={{ width: 200 }}
                value="default"
                disabled
                placeholder="命名空间固定为 default"
              />
            </div>
            
            <Input.Search
              placeholder="搜索定时任务名称"
              style={{ width: 250 }}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              allowClear
            />
            
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={showCreateModal}
              disabled={!selectedNamespace}
            >
              添加定时任务
            </Button>
            
            <Button
              icon={<ReloadOutlined />}
              onClick={() => fetchCronJobs(selectedCluster, selectedNamespace)}
              disabled={!selectedNamespace}
            >
              刷新
            </Button>
          </Space>
        </div>
        
        <Table
          columns={columns}
          dataSource={filteredCronJobs}
          rowKey="name"
          loading={loading}
          pagination={{ pageSize: 10 }}
        />
      </Card>
      
      {/* 查看详情模态框 */}
      <Modal
        title={`定时任务详情: ${currentCronJob?.name}`}
        visible={detailVisible}
        onCancel={() => setDetailVisible(false)}
        width={800}
        footer={[
          <Button key="export" icon={<ExportOutlined />} onClick={handleExportYaml}>
            导出YAML
          </Button>,
          <Button key="close" onClick={() => setDetailVisible(false)}>
            关闭
          </Button>,
        ]}
      >
        <Spin spinning={loading}>
          <CodeEditor
            value={yamlContent}
            language="yaml"
            height="500px"
            readOnly
          />
        </Spin>
      </Modal>
      
      {/* 创建CronJob模态框 */}
      <Modal
        title="创建定时任务"
        visible={createVisible}
        onCancel={() => setCreateVisible(false)}
        width={800}
        footer={[
          <Button key="cancel" onClick={() => setCreateVisible(false)}>
            取消
          </Button>,
          <Button key="submit" type="primary" loading={loading} onClick={handleCreateSubmit}>
            创建
          </Button>,
        ]}
      >
        <Spin spinning={loading}>
          <Tabs 
            activeKey={formMode} 
            onChange={(key) => setFormMode(key as 'form' | 'yaml')}
            style={{ marginBottom: 16 }}
          >
            <TabPane tab="窗口配置" key="form">
              <CronJobForm 
                initialValues={yamlContent}
                onGenerateYaml={handleGenerateYaml}
              />
            </TabPane>
            <TabPane tab="YAML 编辑" key="yaml">
              <CodeEditor
                value={yamlContent}
                language="yaml"
                height="500px"
                onChange={(value) => setYamlContent(value)}
              />
            </TabPane>
          </Tabs>
        </Spin>
      </Modal>
      
      {/* 编辑CronJob模态框 */}
      <Modal
        title={`编辑定时任务: ${currentCronJob?.name}`}
        visible={editVisible}
        onCancel={() => setEditVisible(false)}
        width={800}
        footer={[
          <Button key="cancel" onClick={() => setEditVisible(false)}>
            取消
          </Button>,
          <Button key="submit" type="primary" loading={loading} onClick={handleEditSubmit}>
            更新
          </Button>,
        ]}
      >
        <Spin spinning={loading}>
          <Tabs 
            activeKey={formMode} 
            onChange={(key) => setFormMode(key as 'form' | 'yaml')}
            style={{ marginBottom: 16 }}
          >
            <TabPane tab="窗口配置" key="form">
              <CronJobForm 
                initialValues={yamlContent}
                onGenerateYaml={handleGenerateYaml}
              />
            </TabPane>
            <TabPane tab="YAML 编辑" key="yaml">
              <CodeEditor
                value={yamlContent}
                language="yaml"
                height="500px"
                onChange={(value) => setYamlContent(value)}
              />
            </TabPane>
          </Tabs>
        </Spin>
      </Modal>

      {selectedCronJob && (
        <CronJobExecutions
          cluster={selectedCluster}
          namespace={selectedNamespace}
          name={selectedCronJob.name}
          visible={executionsVisible}
          onClose={() => setExecutionsVisible(false)}
          initialActiveTab={initialActiveTab}
        />
      )}
    </PageContainer>
  );
};

export default CronJobManagement; 