import React, { useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Card, Button, Input, List, Typography, Space, message, Spin } from 'antd';
import { RobotOutlined, SendOutlined } from '@ant-design/icons';
import aiAssistantService from '@/services/aiAssistant';

const { TextArea } = Input;
const { Title, Text } = Typography;

interface TestMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
}

const AIAssistantTest: React.FC = () => {
  const [messages, setMessages] = useState<TestMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [sessionId] = useState(`test_session_${Date.now()}`);

  const sendTestMessage = async () => {
    if (!inputValue.trim()) {
      message.warning('请输入测试消息');
      return;
    }

    const userMessage: TestMessage = {
      role: 'user',
      content: inputValue.trim(),
      timestamp: Date.now(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setLoading(true);

    try {
      const response = await aiAssistantService.sendChatMessage({
        message: inputValue.trim(),
        sessionId,
      });

      if (response.code === 200) {
        const assistantMessage: TestMessage = {
          role: 'assistant',
          content: response.data.reply,
          timestamp: response.data.timestamp * 1000,
        };
        
        setMessages(prev => [...prev, assistantMessage]);
        message.success('AI回复成功');
      } else {
        message.error(response.message || 'AI回复失败');
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      message.error('发送消息失败，请检查网络连接');
    } finally {
      setLoading(false);
    }
  };

  const clearMessages = () => {
    setMessages([]);
    message.success('消息已清空');
  };

  const testQuickTemplates = async () => {
    try {
      const response = await aiAssistantService.getQuickTemplates();
      if (response.code === 200) {
        message.success(`获取到 ${response.data.length} 个快速模板`);
        console.log('快速模板:', response.data);
      } else {
        message.error('获取快速模板失败');
      }
    } catch (error) {
      console.error('获取快速模板失败:', error);
      message.error('获取快速模板失败');
    }
  };

  return (
    <PageContainer
      title="AI助手功能测试"
      subTitle="测试AI助手的各项功能"
      extra={[
        <Button key="templates" onClick={testQuickTemplates}>
          测试快速模板
        </Button>,
        <Button key="clear" onClick={clearMessages}>
          清空消息
        </Button>,
      ]}
    >
      <Card title="AI助手测试界面" extra={<RobotOutlined style={{ fontSize: 20 }} />}>
        <Space direction="vertical" style={{ width: '100%' }} size="large">
          {/* 消息列表 */}
          <div style={{ height: 400, overflowY: 'auto', border: '1px solid #f0f0f0', padding: 16 }}>
            {messages.length === 0 ? (
              <div style={{ textAlign: 'center', color: '#999', paddingTop: 100 }}>
                <RobotOutlined style={{ fontSize: 48, marginBottom: 16 }} />
                <div>开始与AI助手对话吧！</div>
              </div>
            ) : (
              <List
                dataSource={messages}
                renderItem={(message) => (
                  <List.Item style={{ border: 'none', padding: '8px 0' }}>
                    <div style={{ width: '100%' }}>
                      <div style={{ 
                        display: 'flex', 
                        justifyContent: message.role === 'user' ? 'flex-end' : 'flex-start',
                        marginBottom: 8 
                      }}>
                        <div style={{
                          maxWidth: '80%',
                          padding: '8px 12px',
                          borderRadius: 12,
                          backgroundColor: message.role === 'user' ? '#1890ff' : '#f5f5f5',
                          color: message.role === 'user' ? 'white' : '#333',
                        }}>
                          <div style={{ marginBottom: 4 }}>
                            <Text strong style={{ 
                              color: message.role === 'user' ? 'white' : '#1890ff',
                              fontSize: 12 
                            }}>
                              {message.role === 'user' ? '用户' : 'AI助手'}
                            </Text>
                          </div>
                          <div 
                            style={{ lineHeight: 1.5 }}
                            dangerouslySetInnerHTML={{ __html: message.content }}
                          />
                          <div style={{ 
                            fontSize: 11, 
                            opacity: 0.7, 
                            marginTop: 4,
                            textAlign: 'right'
                          }}>
                            {new Date(message.timestamp).toLocaleTimeString()}
                          </div>
                        </div>
                      </div>
                    </div>
                  </List.Item>
                )}
              />
            )}
            {loading && (
              <div style={{ textAlign: 'center', padding: 16 }}>
                <Spin size="small" />
                <span style={{ marginLeft: 8 }}>AI正在思考中...</span>
              </div>
            )}
          </div>

          {/* 输入区域 */}
          <div>
            <TextArea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="输入您的问题，测试AI助手功能..."
              autoSize={{ minRows: 2, maxRows: 4 }}
              disabled={loading}
              onPressEnter={(e) => {
                if (!e.shiftKey) {
                  e.preventDefault();
                  sendTestMessage();
                }
              }}
            />
            <div style={{ marginTop: 8, textAlign: 'right' }}>
              <Button
                type="primary"
                icon={<SendOutlined />}
                onClick={sendTestMessage}
                loading={loading}
                disabled={!inputValue.trim()}
              >
                发送测试消息
              </Button>
            </div>
          </div>

          {/* 测试信息 */}
          <Card size="small" title="测试信息">
            <Space direction="vertical">
              <Text>会话ID: <Text code>{sessionId}</Text></Text>
              <Text>消息数量: <Text strong>{messages.length}</Text></Text>
              <Text>状态: <Text type={loading ? 'warning' : 'success'}>
                {loading ? '正在处理' : '就绪'}
              </Text></Text>
            </Space>
          </Card>
        </Space>
      </Card>
    </PageContainer>
  );
};

export default AIAssistantTest;
