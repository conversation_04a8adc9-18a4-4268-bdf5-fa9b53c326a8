import * as React from 'react';
import {Button, Input, Form, Icon, Spin} from 'antd';
import LocalStorage from '@/utils/localStorage';
import {connect} from 'dva';
import styles from './index.less';

const {Item: FormItem} = Form;

@window.connectModel('init', '@@initialState')
@connect(({user, loading}) => ({
  user,
  loading: loading.effects['user/login'],
}))
class Login extends React.Component {
  // componentDidUpdate(prevProps, prevState, snapshot) {
  //   const auth = LocalStorage.get();
  //   const {
  //     history: {push},
  //   } = this.props;
  //   if (auth) push('/welcome');
  // }

  renderLoginTitle = () => (
    <div
      className={styles.loginTitle}
      onClick={() => window.open('https://www.blacklake.cn', '_blank')}
    >
      <p>黑湖制造</p>
    </div>
  );

  handleSubmit = values => {
    const {username, password} = values;

    const data = {
      username: username.replace(/\s/g, ''),
      password,
    };

    const {dispatch, init = {}} = this.props;
    const { initialState, setInitialState } = init;
    dispatch({
      type: 'user/login',
      payload: {
        data,
      },
      callback: resp => {
        setInitialState({
          ...initialState,
          currentUser: resp?.data,
        })
      }
    });
  };

  renderLoginContent = () => {
    // const { loading } = this.props;

    const loading = false;

    return (
      <Form onFinish={this.handleSubmit} className={styles.loginForm}>
        <FormItem name="username" rules={[
          {
            required: true,
            message: '请输入您的用户名。',
          },
        ]}>
          <Input prefix={<Icon type="user"/>} placeholder="用户名"/>
        </FormItem>
        <FormItem name={"password"} rules={[
          {
            required: true,
            message: '请输入你的密码。',
          },
        ]}>
          <Input prefix={<Icon type="lock"/>} type="password" placeholder="密码"/>
        </FormItem>
        <FormItem>
          <Spin spinning={loading}>
            <Button style={{width: '100%', height: 32}} type="primary" htmlType="submit">
              {loading ? '登 录 中...' : '登 录'}
            </Button>
          </Spin>
        </FormItem>
      </Form>
    );
  };

  render() {
    return (
      <div className={styles.loginWrapper}>
        <div className={styles.blLogo}/>
        <div className={styles.loginFormContainer}>{this.renderLoginContent()}</div>
      </div>
    );
  }
}

export default Login;
