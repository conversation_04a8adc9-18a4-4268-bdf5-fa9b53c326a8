.loginWrapper {
  width: 100%;
  height: 100vh;
  background: url(https://ob2clxctf.qnssl.com/web/login/181101/BG2.png) no-repeat fixed;
  background-size: cover;
  display: flex;
  justify-content: center;
  flex-direction: column;

  .blLogo {
    width: 280px;
    height: 70px;
    position: absolute;
    top: 5px;
    left: -40px;
    transform: scale(0.55);
    background: url(https://ob2clxctf.qnssl.com/web/login/181031/LOGO.png) no-repeat;
  }

  .loginTitle p {
    cursor: pointer;
    text-align: center;
    font-size: 21px;
    font-weight: bold;
    color: #02b980;
    border-radius: 15px;
    width: 105px;
    line-height: 30px;
    border: 2px solid #02b980;
  }

  .loginFormContainer {
    background-color: #fff;
    border-radius: 30px;
    margin: 0 auto;
    width: 380px;
    min-height: 240px;
    padding: 20px 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;

    .loginContent {
      width: 100%;
    }

    .loginForm {
      width: 100%;
      :global {
        .ant-form-item {
          margin-bottom: 5px;
          display: block;
        }
        .ant-input-group-wrapper {
          display: inline-block;
        }
      }
    }
  }
}
