import * as React from 'react';
import { connect } from 'dva';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {Card, Button} from 'antd';

@connect(({ user, loading }) => ({
  user,
  loading: loading.effects['user/login'],
}))
class Profile extends React.Component {
  // onIntroduceClick = () => {
  //   const w = window.open('about:blank');
  //   w.location.href = 'https://blacklake.feishu.cn/docs/doccn7KHIw93v5sDTWDwwZycATf';
  // };

  render() {
    // const { currentUser = {} } = this.props.user;
    return (
      <PageHeaderWrapper title="Profile">
        <Card>
          欢迎使用blops
          {/* <span>Hello {currentUser.displayName || 'world'}</span> */}
          {/* &nbsp;&nbsp;&nbsp;&nbsp; */}
          {/*<Button size="large" type="link" onClick={() => this.onIntroduceClick()}>*/}
          {/*  barren-admin介绍*/}
          {/*</Button>*/}
        </Card>
      </PageHeaderWrapper>
    );
  }
}

export default Profile;
