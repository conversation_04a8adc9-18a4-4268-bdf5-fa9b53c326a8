import React, {PureComponent} from 'react';
import {timeFormatToLocal} from "@/utils/time";
import {PageHeaderWrapper} from "@ant-design/pro-layout";
import {Card, Pagination, Table, Tag} from "antd";
import {connect} from "dva";
import moment from 'moment';
import InputForm from './InputForm';
import ReactMarkdown from 'react-markdown';

const queryKeys = ["cluster", "traceId"];

@connect(({alert, loading}) => ({
  alert,
  loading: loading.effects['alert/listAlert'],
}))


class AlertList extends PureComponent {

  componentDidMount() {
    const {dispatch} = this.props;
    dispatch({
      type: 'alert/listAlert',
      payload: {
        page: this.state.page,
        page_size: this.state.size
      },
    });
  }

  state = {
    hostname: "vm-pro-k8s019",
    page: 1,
    size: 20,
    date: moment().format('YYYY-MM-DD'),
    start: moment('00:00:00', 'HH:mm:ss').format('HH:mm:ss'),
    end: moment('23:59:59', 'HH:mm:ss').format('HH:mm:ss'),
    selectCluster: '',
    selectKeysMap: new Map(),
    inputValuesMap: new Map(),
    prek: null,
    uriList: [],
    loading: false,
  };

  onInputFormRef = ref => {
    this.childInputForm = ref;
  };

  // onAccessLogDetailRef = ref => {
  //   this.childDetailModal = ref;
  // };

  resOnInputChange = key => value => {
    if (value) {
      this.state.inputValuesMap.set(key, value);
    } else {
      this.state.inputValuesMap.delete(key);
    }
  };

  onSearch = () => {
    this.getByPage();
  };

  onShowSizeChange = (current, pageSize) => {
    this.state.page = 1;
    this.state.size = pageSize;
    this.getByPage();
  };


  onPageChange = (page, size) => {
    this.state.page = page;
    this.getByPage();
  };



  getByPage = () => {

    const { dispatch } = this.props;

    dispatch({
      type: 'alert/listAlert',
      payload: {
        page: this.state.page,
        page_size: this.state.size,
        hostname: this.state.inputValuesMap.get("hostname"),
        status: this.state.inputValuesMap.get("status"),
        level: this.state.inputValuesMap.get("level"),
        alert_name: this.state.inputValuesMap.get("alertname"),
        message: this.state.inputValuesMap.get("message_query"), // For fuzzy message search
        cluster: this.state.inputValuesMap.get("cluster")       // Added for cluster filtering
      },
    });
  };


  render() {

    const {alert, loading} = this.props;
    const alertList = alert.alertList|| [];
    const pageTotal = alert.total || 0;
    
    // 在控制台输出调试信息，帮助确认数据
    console.log('Total records:', pageTotal);
    console.log('Current page:', this.state.page);
    console.log('Page size:', this.state.size);

    const columns = [
      {
        title: '主机名',
        dataIndex: 'hostname',
        key: 'hostname',
        width: 150,
        render: text => <div style={{ maxWidth: '100%', whiteSpace: 'normal', overflowWrap: 'break-word', wordBreak: 'break-word' }}>{text}</div>,
      },
      {
        title: '实例',
        dataIndex: 'instance',
        key: 'instance',
        width: 150,
        render: text => <div style={{ maxWidth: '100%', whiteSpace: 'normal', overflowWrap: 'break-word', wordBreak: 'break-word' }}>{text}</div>,
      },
      {
        title: '集群',
        dataIndex: 'cluster',
        key: 'cluster',
        width: 100,
      },
      {
        title: '告警名称',
        dataIndex: 'alert_name',
        key: 'alert_name',
      },
      {
        title: '状态',
        key: 'status',
        dataIndex: 'status',
        render: status => {
          let color = 'geekblue'; // Default color
          if (status === 'firing') {
            color = 'volcano';
          } else if (status === 'resolved') {
            color = 'green';
          }
          return (
            <Tag color={color} key={status}>
              {status ? status.toUpperCase() : 'N/A'}
            </Tag>
          );
        },
        width: 100,

      },
      {
        title: '级别',
        dataIndex: 'level',
        key: 'level',
        width: 50,
      },
      {
        title: '告警信息',
        dataIndex: 'message',
        key: 'message',
        width: 500,
        ellipsis: false,
        render: text => (
          <div style={{ maxWidth: '100%', whiteSpace: 'normal', overflowWrap: 'break-word', wordBreak: 'break-word' }}>
            <ReactMarkdown>{text || ''}</ReactMarkdown>
          </div>
        ),
      },
      {
        title: '接收者',
        dataIndex: 'receiver',
        key: 'receiver',
        width: 100,
      },
      {
        title: '开始时间',
        dataIndex: 'started_at',
        key: 'started_at',
        sorter: (a, b) => moment(a.started_at).diff(moment(b.started_at)),
        render: text => timeFormatToLocal(text),
      },
    ];
    const rowSelection = {
      renderCell: (checked, index, node) => ({
        props: { rowSpan: index % 2 === 0 ? 2 : 0 },
        children: (
          <>
            {String(checked)}: {node}
          </>
        ),
      }),
    };

    const cardHeadStyle = { fontWeight: 'bold', fontSize: '16px' };

    return (
      <PageHeaderWrapper>
        {/* <Pagination
          defaultCurrent={1}
          defaultPageSize={3}
          onChange={this.handleChange}
          total={data.length}
        /> */}
        {/* <Card title="搜索：" headStyle={cardHeadStyle} size="small"> */}
        <Card title="搜索">
          <InputForm
            formId="inputForm"
            onRef={this.onInputFormRef}
            onSearch={() => this.onSearch()}
            resOnInputChange={val => this.resOnInputChange(val)}
          />
        </Card>

        <Card title="列表">
          {/* <Table
            rowKey="id"
            bordered
            // rowSelection={rowSelection}
            dataSource={alertList}
            columns={columns}
            pagination={{
              defaultPageSize: 20,
            }}
          /> */}
          <Table
            rowKey="id"
            bordered
            loading={loading}
            pagination={{
              showSizeChanger: true,
              onShowSizeChange: this.onShowSizeChange,
              pageSizeOptions: ['10', '20', '30', '50', '100'],
              pageSize: this.state.size,
              defaultCurrent: 1,
              defaultPageSize: 20,
              current: this.state.page,
              total: pageTotal,
              onChange: (page, size) => this.onPageChange(page, size),
              showTotal: (total) => `共 ${total} 条记录`
            }}
            columns={columns}
            dataSource={alertList}
            style={{ overflow: 'scroll', whiteSpace: 'nowrap' }}
            scroll={{ x: 'max-content' }}
          />

        </Card>

      </PageHeaderWrapper>
    );
  }
}

export default AlertList;
