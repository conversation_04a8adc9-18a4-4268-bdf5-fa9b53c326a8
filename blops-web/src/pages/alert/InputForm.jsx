import React, {Component} from 'react';
import {Button, Col, Form, Row, Select} from 'antd';
import CommonAutoComplete from '@/components/CommonAutoComplete';
import SearchSelect from '@/components/SearchSelect';

const StatusEnum = {
  FIRING: "FIRING",
  RESOLVED: "RESOLVED"
}

const LevelEnum = {
  P1: "P1",
  P2: "P2",
  P3: "P3"
}

const ClusterEnum = {
  "ali-test": "ali-test",
  "ali-prod": "ali-prod",
  "hwyx-prod": "hwyx-prod"
}

const SelectOption = {
  "status": StatusEnum,
  "level": LevelEnum,
  "cluster": ClusterEnum,
}

const InputKeys = [
  'hostname',
  'status',
  'level',
  'alertname',
  'cluster',
  'message_query',
];

const inputKeysMap = {
  hostname: 'hostname',
  status: 'status',
  level: 'level',
  alertname: 'alertname',
  cluster: 'cluster',
  message_query: 'message_query',
};

const inputKeysDescEnum = {
  hostname: '主机名称',
  status: '告警状态',
  level: '告警级别',
  alertname: '告警名称',
  cluster: '集群',
  message_query: '告警信息',
};

class InputForm extends Component {
  state = {
    cluster: '',
    clear: false,
  };

  formRef = React.createRef();

  componentDidMount = () => {
    this.props.onRef(this);
  };

  handleSubmit = values => {
    this.props.submit(values);
    console.log(values)
  };


  render() {
    const {formId, resOnInputChange, onSearch} = this.props;
    const inputElements = [];
    let currentRowOfCols = [];

    for (const key of InputKeys) {
      const reqKey = inputKeysMap[key];
      currentRowOfCols.push(
        <Col key={key} span={12}>
          <Form.Item
            name={reqKey}
            label={inputKeysDescEnum[key]}
          >
            {reqKey === "status" || reqKey === "level" || reqKey === "cluster" ?
              <SearchSelect
                placeholder={`select ${reqKey}`}
                optionList={Object.keys(SelectOption[reqKey]).map(it => {
                  return {
                    key: it,
                    value: SelectOption[reqKey][it],
                  }
                })}
                onChange={resOnInputChange(reqKey)}
              />
              :
              <CommonAutoComplete
                style={{width: '100%'}}
                placeholder={`input ${reqKey}`}
                onChange={resOnInputChange(reqKey)}
                clear={this.state.clear}
              />
            }
          </Form.Item>
        </Col>,
      );

      if (currentRowOfCols.length === 2) {
        inputElements.push(<Row key={`input-row-${inputElements.length}`} gutter={16}>{currentRowOfCols}</Row>);
        currentRowOfCols = [];
      }
    }

    if (currentRowOfCols.length === 1) {
      currentRowOfCols.push(
        <Col key="search-button-col" span={12}>
          <Form.Item>
            <Button style={{float: 'right'}} type={'primary'} onClick={onSearch}>
              搜 索
            </Button>
          </Form.Item>
        </Col>
      );
      inputElements.push(<Row key={`input-row-${inputElements.length}`} gutter={16}>{currentRowOfCols}</Row>);
    } else {
      inputElements.push(
        <Row key="search-button-row" justify="end" style={{ marginTop: (InputKeys.length > 0 ? '16px' : '0') }}>
          <Col>
            <Form.Item>
              <Button type={'primary'} onClick={onSearch}>
                搜 索
              </Button>
            </Form.Item>
          </Col>
        </Row>
      );
    }

    const formItemlayout = {
      labelCol: {span: 4},
      wrapperCol: {span: 18},
    };
    return (
      <Form
        {...formItemlayout}
        id={formId}
        onFinish={this.handleSubmit}
      >
        {inputElements}
      </Form>
    );
  }
}

export default InputForm;
