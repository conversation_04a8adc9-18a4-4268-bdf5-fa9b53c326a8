import React, {PureComponent} from 'react';
import {timeFormatToLocal} from "@/utils/time";
import {PageHeaderWrapper} from "@ant-design/pro-layout";
import {Card, Table, Tag, Col, Row, Tree, Modal, Form, Select, Input, Button, Space, message} from "antd";
import {connect} from "dva";
import moment from 'moment';
import {MinusCircleOutlined, PlusOutlined} from "@ant-design/icons";

import "./index.css"

const {DirectoryTree} = Tree;
const {TextArea} = Input;

@connect(({alert, loading}) => ({
  alert,
}))


class ExprList extends PureComponent {

  componentDidMount() {
    const {dispatch} = this.props;

    dispatch({
      type: 'alert/listTmpl',
      payload: {},
    });

    dispatch({
      type: 'alert/getTree',
      payload: {},
    });

  }

  state = {
    visible: false,
    hostname: "vm-pro-k8s019",
    page: 1,
    size: 20,
    date: moment().format('YYYY-MM-DD'),
    start: moment('00:00:00', 'HH:mm:ss').format('HH:mm:ss'),
    end: moment('23:59:59', 'HH:mm:ss').format('HH:mm:ss'),
    selectCluster: '',
    selectKeysMap: new Map(),
    inputValuesMap: new Map(),
    prek: null,
    uriList: [],
    loading: false,
    visible_add: false,
    expr: [],
    treeData: [],
    curEditDetail: {},
    alertCode: null
  };

  showModal = (item) => {
    this.setState({visible: true, curEditDetail: {...item}});
  };

  handleCancel = () => {
    this.setState({visible: false, curEditDetail: {}});
  };

  onFinishExpr = values => {
    const {dispatch} = this.props;
    dispatch({
      type: "alert/updateTmpl",
      payload: {
        id: this.state.curEditDetail?.id,
        alert_code: values.alert_code,
        alert_name: values.alert_name,
        message: values.message,
        perchList: values.perchList,
        expr: values.expr,
      },
      callback: resp => {
        if (resp.message === "success") {
          message.info("修改成功！");
          this.setState({visible: false});
        }
      }
    });
  }

  onFinish = (values) => {
    const {dispatch} = this.props;

    console.log(values)

    dispatch({
      type: "alert/addTmpl",
      payload: {
        alert_name: values.name,
        alert_code: values.code,
        message: values.message,
        perchList: values.perchList,
        expr: values.expr,
      }
    });

    this.setState({visible_add: false});

    setTimeout(() =>
        dispatch({
          type: 'alert/listTmpl',
          payload: {
            alert_code: values.code.split(".")[0]
          },
        }), 200)

    location.reload();
  }

  showInput = () => {
    this.setState({visible_add: true,});
  };

  inputCancel = () => {
    this.setState({visible_add: false,});
  };

  onSearch = () => {
    this.getByPage();
  };

  onShowSizeChange = (current, pageSize) => {
    this.state.page = 1;
    this.state.size = pageSize;
    this.getByPage();
  };


  onPageChange = (page, size) => {
    this.state.page = page;
    this.getByPage();
  };



  getByPage = () => {

    const { dispatch } = this.props;
    const { inputValuesMap } = this.state;
    const accessLogFilter = {};

    inputValuesMap.forEach((value, key) => {
      accessLogFilter[key] = value;
    });

    if (this.state.traceId) {
      accessLogFilter.traceId = this.state.traceId;
    }

    const values = {
      page: this.state.page,
      page_size: this.state.size,
    }

    if (this.state.alertCode) {
      values['alert_code'] = this.state.alertCode;
    }

    dispatch({
      type: 'alert/listTmpl',
      payload: {
        ...values,
      },
      // callback: resp => {
      //   console.log("&&&&&&&&&&&&&&&&&", resp)
      //   if (resp && resp.list) {
      //     this.setState({
      //       loading: true,
      // //     });
      //   }
      // },
    });
  };

  render() {

    const {alert, total} = this.props;
    const exprList = alert.exprList
    this.state.treeData = alert.treeList || [];

    const okBtProps = {
      htmlType: 'submit',
      form: "edit_expr",
    };

    const okBtInput = {
      htmlType: 'submit',
      form: "inputForm",
    };

    const onSelect = (keys, info) => {
      this.setState({
        alertCode: keys[0],
      });
      const {dispatch} = this.props;
      dispatch({
        type: 'alert/listTmpl',
        payload: {
          alert_code: keys[0]
        },
      });
    };

    const onExpand = () => {
      console.log('Trigger Expand');
    };

    const columns = [
      {
        title: '操作',
        key: 'option',
        width: 120,
        valueType: 'option',
        render: item =>
          <Button type="primary" onClick={() => this.showModal(item)}>
            编 辑
          </Button>

      },
      {
        title: '告警名称',
        dataIndex: 'alert_name',
      },
      {
        title: '告警标识',
        dataIndex: 'alert_code',
      },
      {
        title: '告警信息',
        dataIndex: 'message',
      },
      {
        title: '表达式',
        dataIndex: 'expr',
      },
      {
        title: '更新时间',
        defaultSortOrder: 'ascend',
        sorter: (a, b) => b.updated_at - a.updated_at,
        render: item => timeFormatToLocal(item.updated_at),
      },
    ];

    return (
      <PageHeaderWrapper>

        <Button type="primary" onClick={this.showInput}>
          新增规则模板
        </Button>
        <Button type="link" href="https://blacklake.feishu.cn/wiki/wikcnsXfIVuMJO1BKLEdCfxTURb"
                target="_blank">帮助手册</Button>

        <br/>
        <br/>

        <Modal
          destroyOnClose
          okButtonProps={{
            ...okBtInput,
          }}
          open={this.state.visible_add}
          onCancel={this.inputCancel}
          title="添加"
          width={800}
        >
          <Form
            id="inputForm"
            ref={this.form}
            name="basic"
            labelCol={{
              span: 3,
            }}
            wrapperCol={{
              span: 21,
            }}
            initialValues={{
              remember: true,
            }}
            onFinish={this.onFinish}
            autoComplete="off"
          >

            <Form.Item
              label="告警名称"
              name="name"
              rules={[
                {
                  required: true,
                  message: '告警名不能为空!',
                },
              ]}
            >
              <Input/>
            </Form.Item>

            <Form.Item
              label="告警标识"
              name="code"
              rules={[
                {
                  required: true,
                  message: '告警标识不能为空，注意层级结构!',
                },
                // 内容校验
                ({getFieldValue}) => ({
                  validator(_, value) {
                    if (value.indexOf('.') >= 0) {
                      return Promise.resolve();
                    } else {
                      return Promise.reject(new Error('请使用类似os.disk或k8s.apiserver.error这样的层级结构命名!'));
                    }
                  },
                }),
              ]}
            >
              <Input/>
            </Form.Item>

            <Form.Item
              label="告警信息"
              name="message"
              rules={[
                {
                  required: true,
                  message: '告警信息不能为空!',
                },
                // 内容校验
                ({getFieldValue}) => ({
                  validator(_, value) {
                    if ((value.startsWith('"') && value.endsWith('"')) || (value.startsWith("'") && value.endsWith("'"))) {
                      return Promise.resolve();
                    } else {
                      return Promise.reject(new Error('内容前后请带双引号或单引号!'));
                    }
                  },
                }),
              ]}
            >
              <TextArea
                placeholder="Please input message"
                autoSize={{minRows: 3, maxRows: 5}}
              />
            </Form.Item>
            <Form.Item
              label="表达式"
              name="expr"
              rules={[
                {
                  required: true,
                  message: '表达式不能为空!',
                },
              ]}
            >
              <TextArea
                placeholder="Please input expr"
                autoSize={{minRows: 3, maxRows: 5}}
              />
            </Form.Item>

            <Form.Item
              label="占位符"
              rules={[
                {
                  required: false,
                  message: '没有可不填',
                },
              ]}
              tooltip={"占位符说明：除k8s本身labels或已有标识外，自定义的占位符，示例如：{{ $MaxValue1 }} 填写 MaxValue1，然后填写MaxValue1对应中文说明：最大值"}
            >
              <Form.List name="perchList">
                {(fields, {add, remove}) => (
                  <>
                    {fields.map(field => (
                        <Space key={field.key} align="right">
                          <Row>
                            <Col>
                              <Form.Item
                                {...field}
                                label={"Key"}
                                name={[field.name, 'key']}
                                rules={[{required: true, message: '不能为空!'}]}
                              >
                                <Input placeholder="请输入占位标识"/>
                              </Form.Item>
                            </Col>
                            &nbsp;&nbsp;
                            <Col>
                              <Form.Item
                                {...field}
                                label={"描述"}
                                name={[field.name, 'value']}
                                rules={[{required: true, message: '不能为空!'}]}
                              >
                                <Input placeholder="请输入占位符描述"/>
                              </Form.Item>
                            </Col>
                            <Col>
                              <MinusCircleOutlined
                                className="dynamic-delete-button"
                                onClick={() => remove(field.name)}
                              />
                            </Col>
                          </Row>
                        </Space>
                      )
                    )}
                    <Form.Item>
                      <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined/>}>
                        新增
                      </Button>
                    </Form.Item>
                  </>
                )}
              </Form.List>
            </Form.Item>
          </Form>
        </Modal>

        <Modal
          title="告警模板修改"
          destroyOnClose
          okButtonProps={{
            ...okBtProps,
          }}
          open={this.state.visible}
          onCancel={this.handleCancel}
          width={800}
        >
          <Form
            id='edit_expr'
            ref={this.form}
            onFinish={this.onFinishExpr}
            name="basic"
            labelCol={{
              span: 3,
            }}
            wrapperCol={{
              span: 21,
            }}
            initialValues={{
              alert_name: this.state.curEditDetail?.alert_name,
              message: this.state.curEditDetail?.message,
              alert_code: this.state.curEditDetail?.alert_code,
              expr: this.state.curEditDetail?.expr,
              perchList: this.state.curEditDetail?.perchList,
            }}
            autoComplete="off"
          >

            <Form.Item
              label="告警名称"
              name="alert_name"
              rules={[
                {
                  required: true,
                  message: '告警名不能为空!',
                },
              ]}
            >
              <Input/>
            </Form.Item>

            <Form.Item
              label="告警标识"
              name="alert_code"
              rules={[
                {
                  required: true,
                  message: '告警标识不能为空，请注意层级结构!',
                },
                // 内容校验
                ({getFieldValue}) => ({
                  validator(_, value) {
                    if (value.indexOf('.') >= 0) {
                      return Promise.resolve();
                    } else {
                      return Promise.reject(new Error('请使用类似os.disk或k8s.apiserver.error这样的层级结构命名!'));
                    }
                  },
                }),
              ]}
            >
              <Input/>
            </Form.Item>

            <Form.Item
              label="告警信息"
              name="message"
              rules={[
                {
                  required: true,
                  message: '告警信息不能为空!',
                },
                // 内容校验
                ({getFieldValue}) => ({
                  validator(_, value) {
                    if ((value.startsWith('"') && value.endsWith('"')) || (value.startsWith("'") && value.endsWith("'"))) {
                      return Promise.resolve();
                    } else {
                      return Promise.reject(new Error('内容前后请带双引号!'));
                    }
                  },
                }),
              ]}
            >
              <TextArea
                placeholder="Please input message"
                autoSize={{minRows: 3, maxRows: 5}}
              />
            </Form.Item>

            <Form.Item
              label="表达式"
              name="expr"
              rules={[
                {
                  required: true,
                  message: '表达式不能为空!',
                },
              ]}
            >
              <TextArea
                placeholder="Please input expr"
                autoSize={{minRows: 3, maxRows: 5}}
              />
            </Form.Item>
            <Form.Item
              label="占位符"
              rules={[
                {
                  required: false,
                  message: '没有可不填',
                },
              ]}
              tooltip={"占位符说明：除k8s本身labels或已有标识外，自定义的占位符，示例如：{{ $MaxValue1 }} 填写 MaxValue1，然后填写MaxValue1对应中文说明：最大值"}
            >
              <Form.List name="perchList">
                {(fields, {add, remove}) => (
                  <>
                    {fields.map(field => (
                        <Space key={field.key} align="right">
                          <Row>
                            <Col>
                              <Form.Item
                                {...field}
                                label={"Key"}
                                name={[field.name, 'key']}
                                rules={[{required: true, message: '不能为空!'}]}
                              >
                                <Input placeholder="请输入占位标识"/>
                              </Form.Item>
                            </Col>
                            &nbsp;&nbsp;
                            <Col>
                              <Form.Item
                                {...field}
                                label={"描述"}
                                name={[field.name, 'value']}
                                rules={[{required: true, message: '不能为空!'}]}
                              >
                                <Input placeholder="请输入占位符描述"/>
                              </Form.Item>
                            </Col>
                            <Col>
                              <MinusCircleOutlined
                                className="dynamic-delete-button"
                                onClick={() => remove(field.name)}
                              />
                            </Col>
                          </Row>
                        </Space>
                      )
                    )}
                    <Form.Item>
                      <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined/>}>
                        新增
                      </Button>
                    </Form.Item>
                  </>
                )}
              </Form.List>
            </Form.Item>
          </Form>
        </Modal>

        <Row gutter={[8, 8]}>
          <Col span={5}>
            <DirectoryTree
              multiple
              onSelect={onSelect}
              onExpand={onExpand}
              treeData={this.state.treeData}
            />
          </Col>

          <Col span={19}>
            <Table
              rowKey="id"
              bordered
              // size="middle"
              loading={this.state.loading}
              pagination={{
                showSizeChanger: true,
                onShowSizeChange: this.onShowSizeChange,
                pageSizeOptions: ['10', '20', '30', '50', '100'],
                pageSize: this.state.pageSize,
                defaultCurrent: 1,
                defaultPageSize: 20,
                current: this.state.page,
                onChange: (page, size) => this.onPageChange(page, size),
              }}
              columns={columns}
              dataSource={exprList}
              style={{overflow: 'scroll', whiteSpace: 'nowrap'}}
            />

          </Col>
        </Row>

      </PageHeaderWrapper>
    );
  }
}

export default ExprList
