import React, { PureComponent } from 'react';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { Card, Spin, Tag, Row, Col, Statistic, Empty, Tooltip, Space, Button, Modal, Table, Divider, Select } from 'antd';
import { connect } from 'dva';
import ReactMarkdown from 'react-markdown';
import moment from 'moment';
import { timeFormatToLocal } from '@/utils/time';
import { Column } from '@ant-design/charts';

const { Option } = Select;

@connect(({ alert, loading }) => ({
  alert,
  loading: loading.effects['alert/getDashboardAlerts'],
}))
class AlertDashboard extends PureComponent {
  state = {
    alertGroups: {},
    filters: {
      level: null,
      cluster: null,
      status: null,
    },
    lastUpdatedAt: null,
    detailModalVisible: false,
    currentAlertGroup: [],
    currentAlertName: '',
    timeRange: 3,
  };

  componentDidMount() {
    this.fetchDashboardData();
    // 设置3分钟自动刷新定时器
    this.timer = setInterval(() => {
      this.fetchDashboardData();
    }, 180000); // 180000毫秒 = 3分钟
  }

  componentWillUnmount() {
    // 组件卸载时清除定时器
    if (this.timer) {
      clearInterval(this.timer);
    }
  }

  fetchDashboardData = () => {
    const { dispatch } = this.props;
    const { timeRange } = this.state;
    dispatch({
      type: 'alert/getDashboardAlerts',
      payload: {
        page: 1,
        page_size: 5000, // 获取最近5000条告警
        h: timeRange,
      },
    });
    // 更新最后刷新时间
    this.setState({ lastUpdatedAt: new Date() });
  };

  getStatusColor = (status) => {
    if (status === 'firing') return 'volcano';
    if (status === 'resolved') return 'green';
    return 'geekblue';
  };

  getLevelColor = (level) => {
    if (level === 'P1') return 'red';
    if (level === 'P2') return 'orange';
    if (level === 'P3') return 'blue';
    return 'default';
  };

  handleFilterClick = (filterType, value) => {
    this.setState(prevState => {
      const newFilters = { ...prevState.filters };
      // 如果点击了当前已选中的值，则取消筛选
      if (newFilters[filterType] === value) {
        newFilters[filterType] = null;
      } else {
        newFilters[filterType] = value;
      }
      return { filters: newFilters };
    });
  };

  showDetailModal = (alertName, alertGroup) => {
    this.setState({
      detailModalVisible: true,
      currentAlertGroup: alertGroup,
      currentAlertName: alertName,
    });
  };

  closeDetailModal = () => {
    this.setState({
      detailModalVisible: false,
    });
  };

  // 处理时间范围选择
  handleTimeRangeChange = (value) => {
    this.setState({ timeRange: value }, this.fetchDashboardData);
  };

  renderFilterTags = (dataList) => {
    // 获取所有可能的值并过滤掉空值
    const levels = [...new Set(dataList.map(item => item.level).filter(Boolean))];
    const statuses = [...new Set(dataList.map(item => item.status).filter(Boolean))];
    const clusters = [...new Set(dataList.map(item => item.cluster).filter(Boolean))];

    const { filters } = this.state;

    return (
      <Card title="筛选条件" style={{ marginBottom: 24 }}>
        <div style={{ marginBottom: 12 }}>
          <span style={{ fontWeight: 'bold', marginRight: 8 }}>级别: </span>
          <Space>
            {levels.map(level => (
              <Tag
                key={level}
                color={filters.level === level ? this.getLevelColor(level) : 'default'}
                style={{ 
                  cursor: 'pointer',
                  textDecoration: 'none'
                }}
                onClick={() => this.handleFilterClick('level', level)}
              >
                {level}
                {filters.level === level && <span style={{ marginLeft: '4px' }}>✓</span>}
              </Tag>
            ))}
            {filters.level && (
              <Tag
                color="default"
                style={{ cursor: 'pointer' }}
                onClick={() => this.handleFilterClick('level', filters.level)}
              >
                清除
              </Tag>
            )}
          </Space>
        </div>

        <div style={{ marginBottom: 12 }}>
          <span style={{ fontWeight: 'bold', marginRight: 8 }}>状态: </span>
          <Space>
            {statuses.map(status => (
              <Tag
                key={status}
                color={filters.status === status ? this.getStatusColor(status) : 'default'}
                style={{ 
                  cursor: 'pointer',
                  textDecoration: 'none'
                }}
                onClick={() => this.handleFilterClick('status', status)}
              >
                {status}
                {filters.status === status && <span style={{ marginLeft: '4px' }}>✓</span>}
              </Tag>
            ))}
            {filters.status && (
              <Tag
                color="default"
                style={{ cursor: 'pointer' }}
                onClick={() => this.handleFilterClick('status', filters.status)}
              >
                清除
              </Tag>
            )}
          </Space>
        </div>

        <div>
          <span style={{ fontWeight: 'bold', marginRight: 8 }}>集群: </span>
          <Space>
            {clusters.map(cluster => (
              <Tag
                key={cluster}
                color={filters.cluster === cluster ? 'cyan' : 'default'}
                style={{ 
                  cursor: 'pointer',
                  textDecoration: 'none'
                }}
                onClick={() => this.handleFilterClick('cluster', cluster)}
              >
                {cluster}
                {filters.cluster === cluster && <span style={{ marginLeft: '4px' }}>✓</span>}
              </Tag>
            ))}
            {filters.cluster && (
              <Tag
                color="default"
                style={{ cursor: 'pointer' }}
                onClick={() => this.handleFilterClick('cluster', filters.cluster)}
              >
                清除
              </Tag>
            )}
          </Space>
        </div>
      </Card>
    );
  };

  renderAlertCard = (alert) => {
    return (
      <Card 
        key={alert.finger_print}
        size="small" 
        style={{ marginBottom: 16, borderLeft: `3px solid ${alert.status === 'firing' ? '#ff4d4f' : '#52c41a'}` }}
        title={
          <div>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>{alert.instance || alert.hostname || '未知实例'}</span>
              <Tag color={this.getStatusColor(alert.status)}>{alert.status?.toUpperCase() || 'N/A'}</Tag>
            </div>
            <div style={{ fontSize: '12px', color: '#8c8c8c', marginTop: '4px' }}>
              <span>{alert.cluster} | {timeFormatToLocal(alert.started_at)}</span>
            </div>
          </div>
        }
      >
        <div style={{ maxHeight: '200px', overflow: 'auto', marginBottom: '12px' }}>
          <ReactMarkdown>{alert.message || ''}</ReactMarkdown>
        </div>
        <div style={{ display: 'flex', justifyContent: 'space-between', borderTop: '1px solid #f0f0f0', paddingTop: '8px' }}>
          <span>接收者: {alert.receiver}</span>
          <Tag color={this.getLevelColor(alert.level)}>{alert.level}</Tag>
        </div>
      </Card>
    );
  };

  renderDetailModal = () => {
    const { detailModalVisible, currentAlertGroup, currentAlertName } = this.state;

    const columns = [
      {
        title: '实例/主机',
        key: 'instance',
        render: record => record.instance || record.hostname || '未知实例',
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        render: status => (
          <Tag color={this.getStatusColor(status)}>{status?.toUpperCase() || 'N/A'}</Tag>
        ),
      },
      {
        title: '级别',
        dataIndex: 'level',
        key: 'level',
        render: level => (
          <Tag color={this.getLevelColor(level)}>{level}</Tag>
        ),
      },
      {
        title: '集群',
        dataIndex: 'cluster',
        key: 'cluster',
      },
      {
        title: '开始时间',
        dataIndex: 'started_at',
        key: 'started_at',
        render: text => timeFormatToLocal(text),
        sorter: (a, b) => moment(a.started_at).diff(moment(b.started_at)),
      },
      {
        title: '消息',
        dataIndex: 'message',
        key: 'message',
        render: text => (
          <div style={{ maxWidth: 400 }}>
            <ReactMarkdown>{text || ''}</ReactMarkdown>
          </div>
        ),
      },
    ];

    return (
      <Modal
        title={`告警详情: ${currentAlertName}`}
        visible={detailModalVisible}
        onCancel={this.closeDetailModal}
        width={1200}
        footer={[
          <Button key="close" onClick={this.closeDetailModal}>
            关闭
          </Button>
        ]}
      >
        <Table
          dataSource={currentAlertGroup}
          columns={columns}
          rowKey="finger_print"
          pagination={{ pageSize: 10 }}
          scroll={{ x: 1100, y: 500 }}
        />
      </Modal>
    );
  };

  renderAlertChart = (alertList) => {
    // 按告警名称分组并计算数量
    const alertNameCount = {};
    alertList.forEach(item => {
      if (!item.alert_name) return;
      
      if (!alertNameCount[item.alert_name]) {
        alertNameCount[item.alert_name] = 0;
      }
      alertNameCount[item.alert_name] += 1;
    });

    // 转换为图表数据格式并排序
    const chartData = Object.keys(alertNameCount)
      .map(name => ({
        name: name,
        count: alertNameCount[name]
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5); // 只取前5名
    
    const config = {
      data: chartData,
      xField: 'name',
      yField: 'count',
      label: {
        position: 'top',
      },
      meta: {
        name: { alias: '告警名称' },
        count: { alias: '数量' },
      },
      xAxis: {
        label: {
          autoRotate: false,
          formatter: (text) => {
            if (text.length > 10) {
              return `${text.substring(0, 10)}...`;
            }
            return text;
          }
        },
      },
      tooltip: {
        formatter: (datum) => {
          return { name: datum.name, value: datum.count };
        }
      },
      height: 300,
      color: ({ name }) => {
        const colors = ['#5B8FF9', '#5AD8A6', '#5D7092', '#F6BD16', '#E86452'];
        const idx = chartData.findIndex(item => item.name === name);
        return colors[idx % colors.length];
      },
    };
    
    return <Column {...config} />;
  };

  render() {
    const { alert, loading } = this.props;
    const { filters, lastUpdatedAt, timeRange } = this.state;
    const allAlerts = alert.dashboardAlerts || [];
    
    // 处理相同finger_print的告警，只保留最新的一条
    const latestAlertsMap = new Map();
    allAlerts.forEach(item => {
      if (!item.finger_print) return;
      
      const existingAlert = latestAlertsMap.get(item.finger_print);
      // 如果Map中不存在该finger_print或当前记录比已存在的更新，则更新Map
      if (!existingAlert || 
          (new Date(item.updatedAt || item.createdAt) > new Date(existingAlert.updatedAt || existingAlert.createdAt))) {
        latestAlertsMap.set(item.finger_print, item);
      }
    });
    
    // 将Map转换回数组
    const uniqueAlerts = Array.from(latestAlertsMap.values());
    
    // 应用筛选条件
    const alertList = uniqueAlerts.filter(item => {
      // 过滤掉包含"慢接口"的告警
      if (item.alert_name && item.alert_name.includes('慢接口')) return false;
      if (item.message && item.message.includes('慢接口')) return false;
      
      if (filters.level && item.level !== filters.level) return false;
      if (filters.status && item.status !== filters.status) return false;
      if (filters.cluster && item.cluster !== filters.cluster) return false;
      return true;
    });

    // 按告警名称分组
    const alertGroups = {};
    alertList.forEach(item => {
      if (!alertGroups[item.alert_name]) {
        alertGroups[item.alert_name] = [];
      }
      alertGroups[item.alert_name].push(item);
    });

    // 计算各组的firing和resolved数量
    const alertStats = {};
    Object.keys(alertGroups).forEach(name => {
      const group = alertGroups[name];
      const firingCount = group.filter(item => item.status === 'firing').length;
      alertStats[name] = {
        total: group.length,
        firing: firingCount,
        resolved: group.length - firingCount,
      };
    });

    return (
      <PageHeaderWrapper>
        <Card 
          title="告警统计" 
          style={{ marginBottom: 24 }}
          extra={
            <div style={{ display: 'flex', alignItems: 'center', fontSize: '12px', color: '#8c8c8c' }}>
              <Select value={timeRange} style={{ width: 80 }} onChange={this.handleTimeRangeChange}>
                <Option value={3}>3h</Option>
                <Option value={6}>6h</Option>
                <Option value={12}>12h</Option>
                <Option value={24}>24h</Option>
              </Select>
              <Tag color="blue" style={{ marginLeft: '8px', cursor: 'pointer' }} onClick={this.fetchDashboardData}>
                刷新
              </Tag>
              <span style={{ marginLeft: '16px' }}>
                最后更新: {lastUpdatedAt ? moment(lastUpdatedAt).format('YYYY-MM-DD HH:mm:ss') : '加载中...'}
              </span>
            </div>
          }
        >
          <Row gutter={16} style={{ marginBottom: 20 }}>
            <Col span={8}>
              <Statistic 
                title="总告警数" 
                value={alertList.length} 
              />
            </Col>
            <Col span={8}>
              <Statistic 
                title="firing告警数" 
                value={alertList.filter(item => item.status === 'firing').length}
                valueStyle={{ color: '#ff4d4f' }}
              />
            </Col>
            <Col span={8}>
              <Statistic 
                title="resolved告警数" 
                value={alertList.filter(item => item.status === 'resolved').length}
                valueStyle={{ color: '#52c41a' }}
              />
            </Col>
          </Row>
          
          <Divider orientation="left">告警名称 Top 5</Divider>
          {this.renderAlertChart(alertList)}
        </Card>

        {/* 筛选标签 */}
        {this.renderFilterTags(allAlerts)}

        <Spin spinning={loading}>
          {Object.keys(alertGroups).length > 0 ? (
            Object.keys(alertGroups).map(alertName => (
              <Card 
                key={alertName} 
                title={
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <span>{alertName}</span>
                    <div>
                      <Tag color="volcano">{`Firing: ${alertStats[alertName].firing}`}</Tag>
                      <Tag color="green">{`Resolved: ${alertStats[alertName].resolved}`}</Tag>
                      {alertGroups[alertName].length > 5 && (
                        <Tag color="blue">{`共 ${alertGroups[alertName].length} 条`}</Tag>
                      )}
                    </div>
                  </div>
                }
                style={{ marginBottom: 24 }}
                extra={
                  alertGroups[alertName].length > 5 && (
                    <Button 
                      type="primary" 
                      size="small" 
                      onClick={() => this.showDetailModal(alertName, alertGroups[alertName])}
                    >
                      查看全部 {alertGroups[alertName].length} 条
                    </Button>
                  )
                }
              >
                <Row gutter={16}>
                  {alertGroups[alertName].slice(0, 5).map(alert => (
                    <Col xs={24} sm={24} md={12} lg={8} xl={6} xxl={4} key={alert.finger_print}>
                      {this.renderAlertCard(alert)}
                    </Col>
                  ))}
                </Row>
              </Card>
            ))
          ) : (
            <Empty description="没有告警数据" />
          )}
        </Spin>

        {/* 详情弹窗 */}
        {this.renderDetailModal()}
      </PageHeaderWrapper>
    );
  }
}

export default AlertDashboard; 