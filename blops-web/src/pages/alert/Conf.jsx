import React, {PureComponent} from 'react';
import {timeFormatToLocal} from "@/utils/time";
import {PageHeaderWrapper} from "@ant-design/pro-layout";
import {
  Card,
  Table,
  Tabs,
  Select,
  Input,
  Form,
  Modal,
  Button,
  TreeSelect,
  Alert,
  Row,
  Col,
  Space,
  message,
  Upload,
  Progress
} from "antd";
import {connect} from "dva";
import moment from 'moment';
import SearchSelect from "@/components/SearchSelect";
import { UploadOutlined } from '@ant-design/icons';

const {TextArea} = Input;
const {TabPane} = Tabs;
const {Option} = Select;

@connect(({alert, loading}) => ({
  alert,
  loading: loading.effects['alert/listConf'],
}))


class ConfList extends PureComponent {


  componentDidMount() {
    const {dispatch} = this.props;
    dispatch({
      type: 'alert/listTmpl',
      payload: {},
    });

    dispatch({
      type: 'alert/getTree',
      payload: {},
    });

    dispatch({
      type: 'alert/listConf',
      payload: {
        cluster: "ali-test",
        page: this.state.page,
        page_size: this.state.size
      },
    });
  }

  form = React.createRef();

  state = {
    visible: false,
    visible_rule: false,
    visible_add: false,
    cluster: "ali-test",
    treeData: [],
    receiver: undefined,
    expr: undefined,
    detail: undefined,
    page: 1,
    size: 20,
    importingRules: false,
    importProgress: 0,
    totalRulesToImport: 0,
    importedCount: 0,
    skippedCount: 0,
  };

  resOnInputChange = key => value => {
    if (value) {
      this.state.inputValuesMap.set(key, value);
    } else {
      this.state.inputValuesMap.delete(key);
    }
  };


  onSearch = () => {
    this.getByPage();
  };

  onShowSizeChange = (current, pageSize) => {
    this.state.page = 1;
    this.state.size = pageSize;
    this.getByPage();
  };


  onPageChange = (page, size) => {
    this.state.page = page;
    this.getByPage();
  };


  getByPage = () => {

    const {dispatch} = this.props;

    dispatch({
      type: 'alert/listConf',
      payload: {
        cluster: this.state.cluster,
        types: this.state.value,
        page: this.state.page,
        page_size: this.state.size
      },
    });
  };

  onSelectAlert = (values) => {
    const {alert} = this.props;
    const exprList = alert.exprList;
    const expr = exprList.filter(it => it.alert_code === values?.value)?.[0];
    this.setState({
      detail: null,
      expr,
    });
    this.form.current.setFieldsValue({
      alert_code: expr?.alert_code,
      alert_name: expr?.alert_name,
      message: expr?.message,
      expr: expr?.expr,
      perchList: expr?.perchList.map(it => {
        return {
          key: it.key,
          label: it.value,
          value: null,
        }
      }),
    });
  }

  onSelectReceiver = (values) => {

    if (values === "custom") {
      this.setState({receiver: true});
    } else {
      this.setState({receiver: undefined});
    }
  }

  onFinishInput = values => {
    console.log(values);
    const {dispatch} = this.props;
    let type = "alert/addConf";
    let types = values.alert_code;
    if (this.state.detail) {
      type = "alert/updateConf";
      types = this.state.detail.types;
    }
    dispatch({
      type: type,
      payload: {
        template_id: this.state.expr?.id,
        types: types,
        cluster: this.state.cluster,
        level: values.level,
        period: values.period,
        webhook: values.webhook,
        alert_name: values.alert_name,
        message: this.state.expr?.message,
        expr: this.state.expr?.expr,
        roles: "admin",
        receiver: values.receiver,
        perchList: values.perchList,
        labels: values.labels
      },
      callback: resp => {
        if (resp.code === 200 && resp.message === "success") {
          message.info("执行成功");
          this.setState({
            visible_add: false,
            keys: [],
            receiver: false,
            detail: null,
          });

          setTimeout(() =>
              this.getByPage()
            , 500);
        }
      }
    });
  }

  onFinishGet = value => {
    const {dispatch} = this.props;

    dispatch({
      type: "alert/listConf",
      payload: {
        cluster: this.state.cluster,
        types: value.types ? value.types.trim() : "",
        page: this.state.page,
        page_size: this.state.size
      }
    });
  }

  onChangeCls = value => {

    const {dispatch} = this.props;

    this.setState({cluster: value,});

    dispatch({
      type: "alert/listConf",
      payload: {
        cluster: value,
      }
    });

    this.setState({value: "",});
    this.setState({page: "1",});

  }


  showInput = item => {
    const {alert} = this.props;
    const exprList = alert.exprList;
    this.setState({
      detail: item ? {...item} : null,
      receiver: item?.receiver === 'custom',
      visible_add: true,
      expr: exprList.filter(it => it.id === item?.template_id)?.[0],
    });
  };

  afterModalClose = () => {
    this.setState({
      expr: undefined,
      detail: undefined,
    });
  }

  showRuleModal = (item) => {
    const content = (
      <>
        <div>
          <br/>
          <Row gutter={16}>
            {/*<Col span={6}>*/}
            {/*  阈值：*/}
            {/*  <Alert showIcon message={item?.value} type="success"/>*/}
            {/*</Col>*/}
            <Col span={6}>
              检测周期：<Alert showIcon message={item?.period} type="info"/>
            </Col>
            <Col span={6}>
              接收者：<Alert showIcon message={item?.receiver} type="warning"/>
            </Col>
            <Col span={6}>
              分级：<Alert showIcon message={item?.level} type="error"/>
            </Col>
          </Row>
          {/* <Tag color="green">{item?.expr}</Tag> */}
          <br/>
          表达式：
          <Alert 
            showIcon 
            message={item?.expr} 
            type="success"
            style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}
          />
          <br/>
          消息：
          <Alert 
            showIcon 
            message={item?.message} 
            type="info"
            style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}
          />
        </div>
      </>

    );

    Modal.confirm({
      title: `策略详情 （告警名称：${item?.alert_name} ，告警ID：${item?.types}）`,
      width: '60%',
      content,
    });
  };

  onChange = value => {
    const {dispatch} = this.props;
    this.setState({value});
    dispatch({
      type: 'alert/listConf',
      payload: {
        cluster: this.state.cluster,
        types: value || "",
        page: this.state.page,
        page_size: this.state.size
      },
    });
  };

  onCancel = () => {
    this.setState({
      visible_add: false,
      keys: [],
      receiver: false,
      detail: undefined,
      expr: undefined,
    });
  }

  // 处理文件选择
  handleFileSelect = (file) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const rules = JSON.parse(e.target.result);
        if (!Array.isArray(rules)) {
          message.error('导入失败！文件内容必须是告警规则数组');
          return;
        }
        
        this.setState({ 
          importingRules: true,
          importProgress: 0,
          totalRulesToImport: rules.length,
          importedCount: 0,
          skippedCount: 0
        }, () => {
          this.processRulesImport(rules);
        });
      } catch (error) {
        message.error('导入失败！JSON解析错误：' + error.message);
      }
    };
    reader.readAsText(file);
    return false; // 阻止自动上传
  };

  // 逐条处理规则导入
  processRulesImport = (rules) => {
    const { alert } = this.props;
    const confList = alert.confList || [];
    const currentAlertNames = new Set(confList.map(item => item.alert_name));
    
    let processed = 0;
    let imported = 0;
    let skipped = 0;
    const totalRules = rules.length;
    
    const importNextRule = (index) => {
      if (index >= totalRules) {
        // 所有规则已处理完毕
        this.setState({ 
          importingRules: false,
          importProgress: 100,
        });
        message.success(`导入完成！成功导入${imported}条规则，跳过${skipped}条规则`);
        // 刷新列表
        this.getByPage();
        return;
      }
      
      const rule = rules[index];
      
      // 检查是否存在同名告警
      if (currentAlertNames.has(rule.alert_name)) {
        processed++;
        skipped++;
        this.setState({ 
          importProgress: Math.floor((processed / totalRules) * 100),
          importedCount: imported,
          skippedCount: skipped
        });
        
        // 延迟处理下一条
        setTimeout(() => importNextRule(index + 1), 100);
        return;
      }
      
      // 处理规则字段
      // 提取types前缀，去掉末尾的时间戳
      const typesPrefix = rule.types ? rule.types.replace(/-\d+$/, '') : 'custom';
      
      // 处理labels字段，确保JSON格式正确
      let labels = '{"user":"prometheus"}';
      if (rule.labels) {
        try {
          // 尝试解析现有的labels
          const labelsObj = JSON.parse(rule.labels);
          
          // 确保没有重复的key
          const uniqueLabels = {};
          for (const key in labelsObj) {
            if (labelsObj.hasOwnProperty(key)) {
              uniqueLabels[key] = labelsObj[key];
            }
          }
          
          // 重新序列化为字符串
          labels = JSON.stringify(uniqueLabels);
        } catch (error) {
          console.error('解析labels失败:', error);
          // 使用默认值
          labels = '{"user":"prometheus"}';
        }
      }
      
      const processedRule = {
        template_id: rule.template_id,
        types: typesPrefix,
        cluster: this.state.cluster,
        level: rule.level,
        period: rule.period,
        webhook: rule.webhook,
        alert_name: rule.alert_name,
        message: rule.message,
        expr: rule.expr,
        roles: rule.roles || "admin",
        receiver: rule.receiver,
        perchList: rule.perchList || [],
        labels: labels,
      };
      
      // 调用API创建规则
      const { dispatch } = this.props;
      dispatch({
        type: 'alert/addConf',
        payload: processedRule,
        callback: resp => {
          processed++;
          
          if (resp.code === 200 && resp.message === "success") {
            imported++;
            // 添加到当前名称集合防止重复导入
            currentAlertNames.add(rule.alert_name);
          } else {
            skipped++;
            console.error('导入失败:', rule.alert_name, resp);
          }
          
          this.setState({ 
            importProgress: Math.floor((processed / totalRules) * 100),
            importedCount: imported,
            skippedCount: skipped
          });
          
          // 延迟处理下一条
          setTimeout(() => importNextRule(index + 1), 1000);
        }
      });
    };
    
    // 开始处理第一条规则
    importNextRule(0);
  };

  // 导出当前页面数据为JSON文件
  exportData = () => {
    const { alert } = this.props;
    const confList = alert.confList || [];
    
    if (confList.length === 0) {
      message.warning('没有可导出的数据');
      return;
    }
    
    // 创建Blob对象
    const jsonData = JSON.stringify(confList, null, 2);
    const blob = new Blob([jsonData], { type: 'application/json' });
    
    // 创建下载链接并触发下载
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `告警配置_${this.state.cluster}_${moment().format('YYYY-MM-DD')}.json`;
    document.body.appendChild(link);
    link.click();
    
    // 清理
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    
    message.success('导出成功');
  };


  render() {

    const {alert, total} = this.props;
    const exprList = alert?.exprList || [];

    const confList = alert.confList || [];
    this.state.treeData = alert.treeList || [];

    const perchValueList = this.state.expr?.perchList.map((expr) => {
      const perchValue = this.state.detail ? this.state.detail?.perchList?.filter(it => it.key === expr.key)?.[0]?.value : null;
      return {
        key: expr.key,
        label: expr.value,
        value: perchValue,
      }
    });

    const okBtRules = {
      htmlType: 'submit',
      form: "get_alert",
    };

    const okBtProps = {
      htmlType: 'submit',
      form: "edit_alert",
    };

    const okBtInput = {
      htmlType: 'submit',
      form: "inputForm",
    };

    const columns = [
      {
        title: '告警名称',
        dataIndex: 'alert_name',
        fixed: 'left',
      },
      // {
      //   title: 'alertmanager关联名称',
      //   dataIndex: 'types',
      // },
      {
        title: '告警定级',
        dataIndex: 'level',
      },
      // {
      //   title: '告警阈值',
      //   dataIndex: 'value',
      //   editable: true,
      // },
      {
        title: '接收者',
        dataIndex: 'receiver',
      },
      {
        title: '告警信息',
        dataIndex: 'message',
        editable: true,
      },
      {
        title: '更新时间',
        sorter: (a, b) => moment(b.updatedAt).diff(moment(a.updatedAt)),
        defaultSortOrder: 'ascend',
        render: item => timeFormatToLocal(item.updatedAt),
      },
      {
        title: '操作',
        key: 'option',
        width: 120,
        valueType: 'option',
        fixed: 'right',
        render: item =>
          <>
            <Button type="primary" onClick={() => this.showRuleModal({...item})}>
              查 看
            </Button>
            &nbsp;&nbsp;
            <Button onClick={() => this.showInput({...item})}>
              编 辑
            </Button>
          </>
      },
    ];

    let initValues = {};
    if (this.state.detail) {
      initValues = {
        ...this.state.detail,
        message: this.state.expr?.message,
        expr: this.state.expr?.expr,
        alert_code: this.state.expr?.alert_code,
      }
      initValues["perchList"] = [];
      if (perchValueList) {
        initValues["perchList"] = perchValueList;
      }
    }

    const uploadProps = {
      beforeUpload: this.handleFileSelect,
      accept: '.json',
      showUploadList: false,
    };

    return (
      <PageHeaderWrapper>
        <Tabs defaultActiveKey="ali-test" onChange={this.onChangeCls}>
          <TabPane tab="ali-test集群" key="ali-test">
            {/* Content of Tab Pane 1 */}
          </TabPane>
          <TabPane tab="ali-prod集群" key="ali-prod">
            {/* Content of Tab Pane 3 */}
          </TabPane>
          <TabPane tab="hwyx-prod集群" key="hwyx-prod">
            {/* Content of Tab Pane 3 */}
          </TabPane>
          <TabPane tab="ali-v3集群" key="ali-v3">
            {/* Content of Tab Pane 3 */}
          </TabPane>
        </Tabs>

        <Modal
          destroyOnClose
          okButtonProps={{
            ...okBtInput,
          }}
          open={this.state.visible_add}
          title={this.state.detail ? "编辑告警" : "新增告警"}
          width={800}
          afterClose={this.afterModalClose}
          onCancel={this.onCancel}
        >
          <Form
            id="inputForm"
            ref={this.form}
            name="basic"
            labelCol={{
              span: 3,
            }}
            wrapperCol={{
              span: 21,
            }}
            initialValues={{
              ...initValues
            }}
            onFinish={this.onFinishInput}
            autoComplete="off"
          >
            <Form.Item
              label="告警模版"
              name="alert_code"
              rules={[
                {
                  required: true,
                  message: '请选择模版!',
                },
              ]}
            >
              <SearchSelect
                disabled={!!this.state.detail}
                initValue={this.state.detail ? {
                  value: this.state.detail?.alert_code,
                  label: this.state?.expr?.alert_name,
                } : (this.state.expr ? {
                  value: this.state.expr?.alert_code,
                  label: this.state?.expr?.alert_name,
                } : undefined)}
                placeholder={"选择告警模版"}
                optionList={exprList.map(it => {
                  return {
                    key: it.alert_code,
                    value: it.alert_name,
                  }
                })}
                onSelected={this.onSelectAlert}
              />
            </Form.Item>
            <Form.Item
              name="alert_name"
              label="告警名称"
              rules={[
                {
                  required: true,
                  message: '请输入告警名称!',
                },
              ]}
            >
              <Input placeholder={"请输入告警名称"} value={this.state?.detail?.alert_name || this.state?.expr?.alert_name}/>
            </Form.Item>
            {
              this.state.expr
                ?
                <>
                  <Form.Item
                    label="告警信息"
                    name="message"
                    rules={[
                      {
                        required: true,
                        message: 'Please input alert message!',
                      }]
                    }
                  >
                    <Input.TextArea disabled allowClear={false}/>
                  </Form.Item>
                  <Form.Item
                    label="表达式"
                    name="expr"
                    rules={[
                      {
                        required: true,
                        message: 'Please input alert expr!',
                      }]
                    }
                  >
                    <Input.TextArea disabled/>
                  </Form.Item>
                  {
                    perchValueList.length > 0 ?
                      <Form.Item
                        label={"占位值"}
                      >
                        <Form.List
                          name="perchList"
                        >
                          {(fields, {add, remove}) => (
                            <>
                              {fields.map(field => {
                                  const perch = perchValueList[field.name];
                                  if (!perch) return;
                                  return <Space key={field.key}>
                                    <Row gutter={[2, 1]}>
                                      <Col span={20}>
                                        <Form.Item
                                          hidden
                                          {...field}
                                          label={"Key"}
                                          name={[field.name, 'key']}
                                          rules={[{required: true, message: '不能为空!'}]}
                                        >
                                          <Input width={6} disabled placeholder="请输入占位标识"/>
                                        </Form.Item>
                                      </Col>
                                      <Col span={100}>
                                        <Form.Item
                                          {...field}
                                          label={perch.key}
                                          name={[field.name, 'value']}
                                          rules={[{required: true, message: '不能为空!'}]}
                                          tooltip={perch.label}
                                        >
                                          <Input placeholder={"请输入" + perch.label}/>
                                        </Form.Item>
                                      </Col>
                                    </Row>
                                  </Space>
                                }
                              )}
                            </>
                          )}
                        </Form.List>
                      </Form.Item>
                    : ''
                  }
                </>
                : ''
            }
            <Form.Item
              label="告警定级"
              name="level"
              rules={[
                {
                  required: true,
                  message: 'Please input level!',
                },
              ]}
            >
              <Select
                showSearch
                placeholder="选择告警定级"
                optionFilterProp="children"
                filterOption={(input, option) =>
                  option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              >
                <Option value="P1">P1</Option>
                <Option value="P2">P2</Option>
                <Option value="P3">P3</Option>

              </Select>

            </Form.Item>

            <Form.Item
              label="接收者"
              name="receiver"
              rules={[
                {
                  required: true,
                  message: 'Please input receiver',
                },
              ]}
            >
              <Select
                showSearch
                onChange={this.onSelectReceiver}
                placeholder="选择告警消息接收人"
                optionFilterProp="children"
                filterOption={(input, option) =>
                  option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              >
                <Option value="ops">ops</Option>
                <Option value="ops-svc">ops-svc</Option>
                <Option value="ops-vm">ops-vm</Option>
                <Option value="ops-k8s">ops-k8s</Option>
                <Option value="ops-db">ops-db</Option>
                <Option value="log">log</Option>
                <Option value="appid">appid</Option>
                <Option value="custom">自定义</Option>

              </Select>

            </Form.Item>

            {
              this.state.receiver ?
                <>
                  <Form.Item
                    label="webhook"
                    name="webhook"
                    rules={[
                      {
                        required: true,
                        message: 'Please input webhook address',
                      },
                    ]}
                  >
                    <TextArea
                      placeholder="输入webhook链接"
                      autoSize={{minRows: 3, maxRows: 5}}
                    />
                  </Form.Item>
                </>
                : ''
            }

            <Form.Item
              label="检测周期"
              name="period"
              rules={[
                {
                  required: true,
                  message: 'Please input period',
                },
              ]}
            >
              <Select
                showSearch
                // onChange={this.onSelectPeriod}
                placeholder="选择检测周期"
                optionFilterProp="children"
                filterOption={(input, option) =>
                  option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              >
                <Option value="1m">1m</Option>
                <Option value="2m">2m</Option>
                <Option value="3m">3m</Option>
                <Option value="5m">5m</Option>
                <Option value="10m">10m</Option>
                <Option value="30m">30m</Option>
                <Option value="1h">1h</Option>

              </Select>

            </Form.Item>

            <Form.Item
              label="标签"
              name="labels"
              initialValue={this.state.detail ? undefined : '{"user":"prometheus"}'}
              rules={[
                {
                  required: false,
                  message: '请输入标签',
                },
              ]}
            >
              <TextArea
                placeholder='输入自定义标签，JSON格式，例如: {"user":"prometheus","team":"sre"}'
                autoSize={{minRows: 3, maxRows: 5}}
              />
            </Form.Item>

            <Form.Item
              wrapperCol={{
                offset: 4,
                span: 20,
              }}
            >
              {/* <Button type="primary" htmlType="submit">
                添 加
              </Button> */}
            </Form.Item>
          </Form>
        </Modal>

        <Card style={{ marginBottom: 16, boxShadow: '0 1px 3px rgba(0,0,0,0.1)', border: 'none', padding: '12px 12px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12, paddingLeft: 0 }}>
            <h2 style={{ margin: 0, color: '#1890ff', fontWeight: 600, fontSize: '18px' }}>
              {this.state.cluster}集群
            </h2>
          </div>
          
          <Row gutter={6} align="middle" style={{ marginLeft: -12 }}>
            <Col span={6}>
              <TreeSelect
                style={{ width: '100%' }}
                value={this.state.value}
                dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                treeData={this.state.treeData}
                placeholder="请选择告警类型"
                onChange={this.onChange}
                allowClear
                size="large"
              />
            </Col>
            <Col span={18}>
              <Form
                name="basic"
                onFinish={this.onFinishGet}
                autoComplete="off"
                layout="inline"
                style={{ display: 'flex', alignItems: 'center' }}
              >
                <Form.Item
                  name="types"
                  style={{ marginBottom: 0, flex: 1 }}
                  rules={[
                    {
                      required: false,
                      message: '请输入关键字',
                    },
                  ]}
                >
                  <Input
                    placeholder="请输入告警名称关键字"
                    size="large"
                    style={{ width: '80%' }}
                    suffix={<span role="img" aria-label="search" className="anticon anticon-search"><svg viewBox="64 64 896 896" focusable="false" data-icon="search" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"></path></svg></span>}
                  />
                </Form.Item>
                <Form.Item style={{ marginBottom: 0, marginLeft: 8 }}>
                  <Button 
                    type="primary" 
                    htmlType="submit" 
                    size="large"
                    style={{ 
                      backgroundColor: '#1890ff',
                      borderColor: '#1890ff',
                      boxShadow: '0 2px 0 rgba(0, 0, 0, 0.045)'
                    }}
                  >
                    模糊搜索
                  </Button>
                </Form.Item>
                <Form.Item style={{ marginBottom: 0, marginLeft: 8 }}>
                  <Button 
                    type="primary" 
                    onClick={() => this.showInput(null)}
                    size="large"
                    style={{ 
                      backgroundColor: '#52c41a', 
                      borderColor: '#52c41a',
                      boxShadow: '0 2px 0 rgba(0, 0, 0, 0.045)' 
                    }}
                    icon={<span role="img" aria-label="plus" className="anticon anticon-plus"><svg viewBox="64 64 896 896" focusable="false" data-icon="plus" width="1em" height="1em" fill="currentColor" aria-hidden="true"><defs><style></style></defs><path d="M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"></path><path d="M176 474h672q8 0 8 8v60q0 8-8 8H176q-8 0-8-8v-60q0-8 8-8z"></path></svg></span>}
                  >
                    新增告警
                  </Button>
                </Form.Item>
                <Form.Item style={{ marginBottom: 0, marginLeft: 8 }}>
                  <Upload {...uploadProps}>
                    <Button
                      type="primary" 
                      size="large"
                      style={{ 
                        backgroundColor: '#1890ff', 
                        borderColor: '#1890ff',
                        boxShadow: '0 2px 0 rgba(0, 0, 0, 0.045)' 
                      }}
                      icon={<UploadOutlined />}
                    >
                      导入
                    </Button>
                  </Upload>
                </Form.Item>
              </Form>
            </Col>
          </Row>
        </Card>

        {this.state.importingRules && (
          <Card style={{ marginBottom: 16 }}>
            <div>
              <div style={{ marginBottom: 8 }}>
                正在导入告警规则 ({this.state.importedCount}/{this.state.totalRulesToImport})
                {this.state.skippedCount > 0 && `, 已跳过: ${this.state.skippedCount}`}
              </div>
              <Progress percent={this.state.importProgress} status="active" />
            </div>
          </Card>
        )}

        <div>
          {confList.filter(it => it.grafana_link !== "" && it.grafana_name !== "").map((item) => {
            return (
              <Button width={100} key={item.id} type="link" href={item.grafana_link}
                      target="_blank">{item.grafana_name}</Button>
            );
          })}
        </div>

        <Modal
          title="策略详情"
          destroyOnClose
          okButtonProps={{
            ...okBtRules,
          }}
          open={this.state.visible_rule}
        />
        <Table
          rowKey={"id"}
          bordered
          size="middle"
          loading={this.state.loading}
          pagination={{
            showSizeChanger: true,
            onShowSizeChange: this.onShowSizeChange,
            pageSizeOptions: ['10', '20', '30', '50', '100'],
            pageSize: this.state.pageSize,
            defaultCurrent: 1,
            defaultPageSize: 20,
            current: this.state.page,
            onChange: (page, size) => this.onPageChange(page, size),
          }}
          columns={columns}
          dataSource={confList}
          style={{overflow: 'scroll', whiteSpace: 'nowrap'}}
        />

        <div style={{ marginTop: 16, textAlign: 'center' }}>
          <Button
            type="primary"
            onClick={this.exportData}
            style={{
              backgroundColor: '#f5222d',
              borderColor: '#f5222d',
              boxShadow: '0 2px 0 rgba(0, 0, 0, 0.045)'
            }}
          >
            导出
          </Button>
        </div>
      </PageHeaderWrapper>
    );
  }
}

export default ConfList
