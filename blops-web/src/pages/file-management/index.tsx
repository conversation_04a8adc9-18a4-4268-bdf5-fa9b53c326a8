import React, { useState } from 'react';
import { Tree, Input, Button, message, Spin, Alert } from 'antd';
import type { DataNode } from 'antd/lib/tree';
import { ReloadOutlined, DeleteOutlined, FolderOutlined, FileOutlined } from '@ant-design/icons';

// 删除父节点的函数
const FileManagement: React.FC = () => {
  // 删除节点函数
  const removeNode = (ip: string) => {
    console.log(`[removeNode] 删除节点: ${ip}`);
    setNodeTree(nodes => nodes.filter(node => node.key !== ip));
    console.log(`[removeNode] 节点删除成功: ${ip}`);
    message.success('节点删除成功');
  };

  // 为父节点生成带删除按钮的标题
  const getParentTitle = (ip: string) => (
    <span>
      <FolderOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
      {ip}
      <Button
        type="link"
        icon={<DeleteOutlined />}
        onClick={(e) => {
          e.stopPropagation();
          removeNode(ip);
        }}
        size="small"
      />
    </span>
  );

  // 初始预置节点
  const [nodeTree, setNodeTree] = useState<DataNode[]>([
    { title: getParentTitle('***********'), key: '***********:8888', isLeaf: false },
    { title: getParentTitle('***********'), key: '***********:8888', isLeaf: false },
    { title: getParentTitle('***********'), key: '***********:8888', isLeaf: false },
    
    // 新增的10.81.1.x节点
    { title: getParentTitle('**********'), key: '**********:8888', isLeaf: false },
    { title: getParentTitle('**********'), key: '**********:8888', isLeaf: false },
    { title: getParentTitle('**********'), key: '**********:8888', isLeaf: false },
    { title: getParentTitle('***********'), key: '***********:8888', isLeaf: false },
    { title: getParentTitle('***********'), key: '***********:8888', isLeaf: false },
    { title: getParentTitle('***********'), key: '***********:8888', isLeaf: false },
    { title: getParentTitle('***********'), key: '***********:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.168'), key: '10.81.1.168:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.17'), key: '10.81.1.17:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.170'), key: '10.81.1.170:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.171'), key: '10.81.1.171:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.177'), key: '10.81.1.177:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.18'), key: '10.81.1.18:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.20'), key: '10.81.1.20:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.200'), key: '10.81.1.200:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.201'), key: '10.81.1.201:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.202'), key: '10.81.1.202:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.203'), key: '10.81.1.203:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.204'), key: '10.81.1.204:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.21'), key: '10.81.1.21:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.214'), key: '10.81.1.214:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.215'), key: '10.81.1.215:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.216'), key: '10.81.1.216:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.217'), key: '10.81.1.217:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.218'), key: '10.81.1.218:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.22'), key: '10.81.1.22:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.223'), key: '10.81.1.223:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.224'), key: '10.81.1.224:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.225'), key: '10.81.1.225:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.226'), key: '10.81.1.226:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.227'), key: '10.81.1.227:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.23'), key: '10.81.1.23:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.234'), key: '10.81.1.234:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.236'), key: '10.81.1.236:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.237'), key: '10.81.1.237:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.238'), key: '10.81.1.238:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.239'), key: '10.81.1.239:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.24'), key: '10.81.1.24:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.240'), key: '10.81.1.240:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.25'), key: '10.81.1.25:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.26'), key: '10.81.1.26:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.27'), key: '10.81.1.27:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.28'), key: '10.81.1.28:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.29'), key: '10.81.1.29:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.34'), key: '10.81.1.34:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.35'), key: '10.81.1.35:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.37'), key: '10.81.1.37:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.38'), key: '10.81.1.38:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.39'), key: '10.81.1.39:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.41'), key: '10.81.1.41:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.42'), key: '10.81.1.42:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.43'), key: '10.81.1.43:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.49'), key: '10.81.1.49:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.60'), key: '10.81.1.60:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.74'), key: '10.81.1.74:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.75'), key: '10.81.1.75:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.76'), key: '10.81.1.76:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.77'), key: '10.81.1.77:8888', isLeaf: false },
    { title: getParentTitle('10.81.1.78'), key: '10.81.1.78:8888', isLeaf: false },
    
    // 新增的10.81.10.x节点
    { title: getParentTitle('10.81.10.12'), key: '10.81.10.12:8888', isLeaf: false },
    { title: getParentTitle('10.81.10.27'), key: '10.81.10.27:8888', isLeaf: false },
    { title: getParentTitle('10.81.10.28'), key: '10.81.10.28:8888', isLeaf: false },
    { title: getParentTitle('10.81.10.29'), key: '10.81.10.29:8888', isLeaf: false },
    { title: getParentTitle('10.81.10.30'), key: '10.81.10.30:8888', isLeaf: false },
    { title: getParentTitle('10.81.10.33'), key: '10.81.10.33:8888', isLeaf: false },
    { title: getParentTitle('10.81.10.40'), key: '10.81.10.40:8888', isLeaf: false },
    { title: getParentTitle('10.81.10.41'), key: '10.81.10.41:8888', isLeaf: false },
    { title: getParentTitle('10.81.10.42'), key: '10.81.10.42:8888', isLeaf: false },
    { title: getParentTitle('10.81.10.47'), key: '10.81.10.47:8888', isLeaf: false },
    { title: getParentTitle('10.81.10.56'), key: '10.81.10.56:8888', isLeaf: false },
    { title: getParentTitle('10.81.10.57'), key: '10.81.10.57:8888', isLeaf: false },
    { title: getParentTitle('10.81.10.58'), key: '10.81.10.58:8888', isLeaf: false },
    { title: getParentTitle('10.81.10.59'), key: '10.81.10.59:8888', isLeaf: false },
    { title: getParentTitle('10.81.10.60'), key: '10.81.10.60:8888', isLeaf: false },
    { title: getParentTitle('10.81.10.61'), key: '10.81.10.61:8888', isLeaf: false },
    { title: getParentTitle('10.81.10.69'), key: '10.81.10.69:8888', isLeaf: false },
    { title: getParentTitle('10.81.10.7'), key: '10.81.10.7:8888', isLeaf: false },
    { title: getParentTitle('10.81.10.71'), key: '10.81.10.71:8888', isLeaf: false },
  ]);

  // 输入框内容，用于添加新的节点
  const [nodeInput, setNodeInput] = useState('');
  
  // 加载状态
  const [loadingNodes, setLoadingNodes] = useState<Record<string, boolean>>({});
  
  // 错误信息
  const [errorMessage, setErrorMessage] = useState('');

  // 辅助函数：更新树节点数据（用于设置某个节点的子节点）
  const updateTreeData = (list: DataNode[], key: React.Key, children: DataNode[]): DataNode[] =>
    list.map(node => {
      if (node.key === key) {
        return { ...node, children };
      }
      if (node.children) {
        return { ...node, children: updateTreeData(node.children, key, children) };
      }
      return node;
    });

  // 模拟子目录数据 - 用于测试，当实际API不可用时
  const mockSubdirectories = (ip: string): string[] => {
    // 为每个IP返回一些模拟的子目录
    const mockData: Record<string, string[]> = {
      '***********:8888': ['logs', 'config', 'data', 'backup'],
      '***********:8888': ['www', 'html', 'images', 'static'],
      '***********:8888': ['docs', 'reports', 'archive'],
    };
    
    return mockData[ip] || ['folder1', 'folder2', 'folder3'];
  };

  // 修改从HTML内容中提取目录和文件的函数，保留原始链接
  const extractItemsFromHtml = (htmlContent: string): Array<{name: string, isDirectory: boolean, href: string}> => {
    console.log(`[extractItemsFromHtml] 开始从HTML内容中提取项目`);
    try {
      const parser = new DOMParser();
      const doc = parser.parseFromString(htmlContent, 'text/html');
      
      const items: Array<{name: string, isDirectory: boolean, href: string}> = [];
      
      // 尝试从链接中提取
      console.log(`[extractItemsFromHtml] 尝试从链接中提取`);
      const links = doc.querySelectorAll('a');
      links.forEach(link => {
        const href = link.getAttribute('href');
        const text = link.textContent?.trim();
        
        if (href && text && 
            href !== '../' && 
            !href.startsWith('?') && 
            !href.startsWith('http')) {
          
          // 判断是否为目录（通常以 / 结尾）
          const isDirectory = href.endsWith('/');
          
          // 移除尾部的斜杠（如果有）
          let name = href.endsWith('/') ? href.slice(0, -1) : href;
          
          // 对URL编码的字符进行解码
          try {
            name = decodeURIComponent(name);
          } catch (e) {
            console.warn('[extractItemsFromHtml] 解码URL失败:', name);
          }
          
          console.log(`[extractItemsFromHtml] 提取项目: ${name}, 是否为目录: ${isDirectory}, 链接: ${href}`);
          items.push({ name, isDirectory, href });
        }
      });
      
      // 如果没有找到任何项目，尝试从表格中提取
      if (items.length === 0) {
        console.log(`[extractItemsFromHtml] 未从链接中找到项目，尝试从表格中提取`);
        const rows = doc.querySelectorAll('tr');
        rows.forEach(row => {
          // 跳过表头行
          if (row.querySelector('th')) return;
          
          const linkCell = row.querySelector('a');
          if (linkCell) {
            const href = linkCell.getAttribute('href');
            if (href && href !== '../' && !href.startsWith('?')) {
              // 判断是否为目录
              const isDirectory = href.endsWith('/');
              
              let name = href.endsWith('/') ? href.slice(0, -1) : href;
              try {
                name = decodeURIComponent(name);
              } catch (e) {
                console.warn('[extractItemsFromHtml] 解码URL失败:', name);
              }
              
              console.log(`[extractItemsFromHtml] 从表格中提取项目: ${name}, 是否为目录: ${isDirectory}, 链接: ${href}`);
              items.push({ name, isDirectory, href });
            }
          }
        });
      }
      
      console.log(`[extractItemsFromHtml] 提取完成，共 ${items.length} 个项目`);
      return items;
    } catch (error) {
      console.error('[extractItemsFromHtml] 解析HTML失败:', error);
      return [];
    }
  };

  // 修改加载节点子目录数据函数
  const onLoadData = (treeNode: DataNode): Promise<void> => {
    return new Promise((resolve) => {
      if (treeNode.children) {
        console.log(`[onLoadData] 节点 ${treeNode.key} 已有子节点，跳过加载`);
        resolve();
        return;
      }
      
      // 解析节点路径
      const keyParts = String(treeNode.key).split('/');
      const ip = keyParts[0];
      let path = '';
      
      // 如果不是根节点，构建完整路径
      if (keyParts.length > 1) {
        // 使用斜杠作为分隔符
        path = keyParts.slice(1).join('/');
      }
      
      // console.log(`[onLoadData] 正在加载节点 ${treeNode.key}，IP: ${ip}, 路径: ${path || '/'}`);
      
      // 构建请求URL
      const requestUrl = path ? `http://${ip}/${path}/` : `http://${ip}/`;
      // console.log(`[onLoadData] 请求URL: ${requestUrl}`);
      
      // 设置加载状态
      setLoadingNodes(prev => ({ ...prev, [treeNode.key]: true }));
      setErrorMessage('');
      
      // 尝试从API获取数据
      fetch(requestUrl)
        .then(response => {
          console.log(`[onLoadData] 服务器响应状态: ${response.status} ${response.statusText}`);
          if (!response.ok) {
            throw new Error(`服务器返回错误: ${response.status} ${response.statusText}`);
          }
          return response.text();
        })
        .then((content) => {
          // console.log(`[onLoadData] 获取到响应内容，长度: ${content.length} 字节`);
          let directories: Array<{name: string, isDirectory: boolean, href?: string}> = [];
          
          // 尝试解析为JSON
          try {
            const jsonData = JSON.parse(content);
            console.log(`[onLoadData] 成功解析为JSON数据`);
            if (Array.isArray(jsonData)) {
              directories = jsonData.map(item => {
                // 假设JSON数据中有标识是否为目录的字段
                const isDir = typeof item === 'object' && item.isDirectory;
                return {
                  name: typeof item === 'string' ? item : item.name,
                  isDirectory: !!isDir,
                  href: typeof item === 'object' && item.href ? item.href : undefined
                };
              });
              console.log(`[onLoadData] 从JSON中提取了 ${directories.length} 个项目`);
            }
          } catch (e) {
            console.log(`[onLoadData] 不是JSON格式，尝试从HTML中提取`);
            // 如果不是JSON，尝试从HTML中提取目录和文件
            const extractedItems = extractItemsFromHtml(content);
            directories = extractedItems;
            
            console.log(`[onLoadData] 从HTML中提取了 ${directories.length} 个项目`);
            
            // 如果没有提取到任何项目，显示警告
            if (directories.length === 0) {
              // console.warn('[onLoadData] 无法从响应中提取目录列表');
              throw new Error('无法从服务器响应中提取目录列表');
            }
          }
          
          // 处理提取的目录数据
          const children: DataNode[] = directories.map(item => {
            const childKey = `${treeNode.key}/${item.name}`;
            // console.log(`[onLoadData] 创建子节点: ${childKey}, 是否为目录: ${item.isDirectory}, 原始链接: ${item.href || '无'}`);
            return {
              title: (
                <span>
                  {item.isDirectory ? (
                    <FolderOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
                  ) : (
                    <FileOutlined style={{ marginRight: '8px', color: '#52c41a' }} />
                  )}
                  {item.name}
                </span>
              ),
              key: childKey,
              isLeaf: !item.isDirectory, // 如果不是目录，则为叶子节点
              href: item.href, // 保存原始链接，用于后续构建URL
            };
          });
          
          // console.log(`[onLoadData] 更新节点 ${treeNode.key} 的子节点，共 ${children.length} 个`);
          setNodeTree(old => updateTreeData(old, treeNode.key, children));
          setLoadingNodes(prev => ({ ...prev, [treeNode.key]: false }));
          resolve();
        })
        .catch(error => {
          // console.error(`[onLoadData] 获取节点 ${treeNode.key} 子目录失败:`, error);
          
          // 使用模拟数据作为后备
          const mockData = mockSubdirectories(ip);
          // console.log(`[onLoadData] 使用模拟数据，共 ${mockData.length} 个项目`);
          const children: DataNode[] = mockData.map(sub => ({
            title: (
              <span>
                <FileOutlined style={{ marginRight: '8px', color: '#52c41a' }} />
                {sub} (模拟数据)
              </span>
            ),
            key: `${treeNode.key}/${sub}`,
            isLeaf: true,
          }));
          
          setNodeTree(old => updateTreeData(old, treeNode.key, children));
          setLoadingNodes(prev => ({ ...prev, [treeNode.key]: false }));
          setErrorMessage(`无法连接到 ${requestUrl}，已显示模拟数据。错误: ${error.message}`);
          resolve();
        });
    });
  };

  // 修改节点选中处理函数
  const onSelect = (selectedKeys: React.Key[], info: any) => {
    const treeNode = info.node;
    // console.log(`[onSelect] 选中节点: ${treeNode.key}, 是否为叶子节点: ${treeNode.isLeaf}`);
    
    if (treeNode.isLeaf) {
      // 获取完整的节点键值
      const fullKey = String(treeNode.key);
      // console.log(`[onSelect] 节点完整路径: ${fullKey}`);
      
      // 直接使用节点键值作为URL路径
      const url = `http://${fullKey}`;
      // console.log(`[onSelect] 构建URL: ${url}`);
      
      // 如果包含"模拟数据"，则提示用户
      if (String(treeNode.title).includes('模拟数据')) {
        // console.log(`[onSelect] 检测到模拟数据，显示警告`);
        message.warning('这是模拟数据，实际链接可能不可用');
        return;
      }
      
      // 获取文件名（路径的最后一部分）
      const fileName = fullKey.split('/').pop() || 'file';
      // console.log(`[onSelect] 提取文件名: ${fileName}`);
      
      // 下载文件
      downloadFile(url, fileName, treeNode);
    } else {
      console.log(`[onSelect] 选中的是目录节点，不执行下载操作`);
    }
  };

  // 提取下载文件的逻辑为单独的函数
  const downloadFile = (url: string, fileName: string, treeNode: any) => {
    // console.log(`[downloadFile] 开始下载文件: ${fileName}, URL: ${url}`);
    
    // 显示下载中消息
    message.loading({ content: '正在下载文件...', key: 'download' });
    
    // 创建一个隐藏的a标签
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    a.target = '_blank';
    
    // 添加到文档并触发点击
    document.body.appendChild(a);
    a.click();
    
    // 清理
    setTimeout(() => {
      document.body.removeChild(a);
      message.success({ content: '文件下载成功', key: 'download' });
    }, 100);
  };

  // 添加节点函数
  const addNode = () => {
    if (!nodeInput.trim()) {
      message.warning('请输入有效的节点IP地址');
      return;
    }
    
    // 检查IP地址格式
    const ip = nodeInput.trim();
    const ipPattern = /^(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}(:\d+)?$/;
    if (!ipPattern.test(ip)) {
      message.error('请输入有效的IP地址，可选带端口号（例如：***********:8888）');
      return;
    }
    
    // 检查是否已存在
    const nodeKey = ip.includes(':') ? ip : `${ip}:8888`;
    if (nodeTree.some(node => node.key === nodeKey)) {
      message.warning('该节点已存在');
      return;
    }
    
    // 添加新节点
    const newNode = { 
      title: getParentTitle(ip), 
      key: nodeKey, 
      isLeaf: false 
    };
    
    setNodeTree(prev => [...prev, newNode]);
    setNodeInput('');
    message.success('节点添加成功');
  };

  // 刷新节点函数
  const handleRefreshNodes = () => {
    console.log('[handleRefreshNodes] 刷新所有节点');
    message.loading({ content: '正在刷新节点...', key: 'refresh' });
    
    // 重置所有节点的子节点
    const refreshedNodes = nodeTree.map(node => ({
      ...node,
      children: undefined
    }));
    
    setNodeTree(refreshedNodes);
    message.success({ content: '节点刷新成功', key: 'refresh' });
  };

  return (
    <div style={{ padding: '24px' }}>
      <h2>内存dump文件 - 节点IP</h2>
      
      <div style={{ marginBottom: '16px', display: 'flex', alignItems: 'center' }}>
        <Input
          placeholder="请输入节点IP地址"
          value={nodeInput}
          onChange={(e) => setNodeInput(e.target.value)}
          style={{ width: '300px', marginRight: '8px' }}
        />
        <Button type="primary" onClick={addNode}>
          添加节点
        </Button>
      </div>
      
      <div style={{ marginBottom: '16px' }}>
        <Button type="primary" icon={<ReloadOutlined />} onClick={handleRefreshNodes}>
          刷新节点
        </Button>
      </div>
      
      {errorMessage && (
        <Alert
          message="连接错误"
          description={errorMessage}
          type="warning"
          showIcon
          closable
          style={{ marginBottom: '16px' }}
          onClose={() => setErrorMessage('')}
        />
      )}
      
      <Spin spinning={Object.values(loadingNodes).some(Boolean)}>
        <Tree
          treeData={nodeTree}
          loadData={onLoadData}
          onSelect={onSelect}
          defaultExpandedKeys={[]}
        />
      </Spin>
      
      <div style={{ marginTop: '24px', padding: '16px', background: '#f5f5f5', borderRadius: '4px' }}>
        <h3>使用说明</h3>
        <p>1. 点击节点前的展开图标加载子目录</p>
        <p>2. 点击文件项将在新窗口打开或下载该文件</p>
        <p>3. 文件夹可以继续展开浏览其内容</p>
      </div>
    </div>
  );
};

export default FileManagement;