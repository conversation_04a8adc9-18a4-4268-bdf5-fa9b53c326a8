import React, { useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Card, Select, Table, Tabs, Button, Modal, Form, Input, message, Space, Spin, Tooltip, Switch, Tag, Layout, Menu, Breadcrumb, Divider, Row, Col, Checkbox } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, ReloadOutlined, BugOutlined, FormatPainterOutlined, CodeOutlined, DownloadOutlined, SearchOutlined } from '@ant-design/icons';
import cluster from '@/services/cluster';
import CodeEditor from '@/components/CodeEditor';
import PodTerminal from '@/components/Terminal';
// @ts-ignore
import yaml from 'js-yaml'; // 引入js-yaml库用于JSON和YAML转换
import request from '@/utils/request';

const { Option } = Select;
const { TabPane } = Tabs;
const { TextArea } = Input;

// 自定义YAML显示组件
const PrettyYaml: React.FC<{ content: string; isYaml: boolean }> = ({ content, isYaml }) => {
  try {
    let displayContent = content;
    let jsonObj: any;
    
    // 如果输入是JSON格式，转换为YAML
    if (!isYaml) {
      jsonObj = JSON.parse(content);
      
      // 移除managedFields字段
      if (jsonObj && typeof jsonObj === 'object' && 'metadata' in jsonObj) {
        delete jsonObj.metadata.managedFields;
        
        // 移除last-applied-configuration注解
        if (jsonObj.metadata.annotations && jsonObj.metadata.annotations["kubectl.kubernetes.io/last-applied-configuration"]) {
          delete jsonObj.metadata.annotations["kubectl.kubernetes.io/last-applied-configuration"];
        }
      }
      
      // 转换为YAML
      displayContent = yaml.dump(jsonObj, { indent: 2 });
    } else {
      // 如果已经是YAML，解析后处理再转回YAML
      jsonObj = yaml.load(content);
      
      // 移除managedFields字段
      if (jsonObj && typeof jsonObj === 'object' && 'metadata' in jsonObj) {
        delete jsonObj.metadata.managedFields;
        
        // 移除last-applied-configuration注解
        if (jsonObj.metadata.annotations && jsonObj.metadata.annotations["kubectl.kubernetes.io/last-applied-configuration"]) {
          delete jsonObj.metadata.annotations["kubectl.kubernetes.io/last-applied-configuration"];
        }
      }
      
      // 转换回YAML
      displayContent = yaml.dump(jsonObj, { indent: 2 });
    }
    
    // 将YAML字符串拆分为行
    const lines = displayContent.split('\n');
    
    return (
      <pre style={{ 
        height: '100%', 
        overflow: 'auto', 
        padding: '12px',
        backgroundColor: '#f5f5f5',
        border: '1px solid #e8e8e8',
        borderRadius: '4px',
        fontSize: '14px',
        lineHeight: '1.5',
        fontFamily: 'monospace'
      }}>
        {lines.map((line, index) => (
          <div key={index}>{line}</div>
        ))}
      </pre>
    );
  } catch (error) {
    console.error('YAML处理失败:', error);
    return <pre>{content}</pre>;
  }
};

// 创建一个可复用的日期格式化函数
const formatDate = (timestamp: string | undefined): string => {
  if (!timestamp) return '-';
  
  try {
    // 处理不同格式的时间戳
    let date;
    if (timestamp.includes('+0800 CST')) {
      // 处理 "2024-04-15 14:27:52 +0800 CST" 格式
      const parsedDate = timestamp.replace(' +0800 CST', '');
      date = new Date(parsedDate);
    } else {
      // 标准ISO格式
      date = new Date(timestamp);
    }
    
    if (isNaN(date.getTime())) {
      return timestamp; // 如果解析失败，直接返回原始字符串
    }
    
    // 使用更简洁的格式：YYYY-MM-DD HH:MM
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  } catch (e) {
    console.error('解析时间戳失败:', e);
    return timestamp;
  }
};

const ClusterManagement: React.FC = () => {
  // 固定的集群选项
  const fixedClusterOptions = [
    { name: 'ali-test', displayName: 'ali-test' },
    { name: 'ali-prod', displayName: 'ali-prod' },
    { name: 'ali-guotai', displayName: 'ali-guotai' },
    { name: 'hwyx-prod', displayName: 'hwyx-prod' },
    { name: 'ali-v3', displayName: 'ali-v3' },
  ];
  
  const [clusterList, setClusterList] = useState<any[]>(fixedClusterOptions);
  const [selectedCluster, setSelectedCluster] = useState<string>('ali-test'); // 默认选择 ali-test
  const [namespaceList, setNamespaceList] = useState<any[]>([]);
  const [selectedNamespace, setSelectedNamespace] = useState<string>('');
  const [deployments, setDeployments] = useState<any[]>([]);
  const [services, setServices] = useState<any[]>([]);
  const [ingresses, setIngresses] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState<string>('deployments');
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [modalType, setModalType] = useState<string>('');
  const [modalTitle, setModalTitle] = useState<string>('');
  const [resourceType, setResourceType] = useState<string>('');
  const [resourceName, setResourceName] = useState<string>('');
  const [yamlContent, setYamlContent] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [useYamlFormat, setUseYamlFormat] = useState<boolean>(true); // 默认使用YAML格式
  const [form] = Form.useForm();
  const [podModalVisible, setPodModalVisible] = useState(false);
  const [currentDeployment, setCurrentDeployment] = useState<string>('');
  const [podList, setPodList] = useState<any[]>([]);
  const [podLoading, setPodLoading] = useState(false);
  const [showPodDetails, setShowPodDetails] = useState(false);
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
  const [podDataMap, setPodDataMap] = useState<Record<string, any[]>>({});
  const [loadingPodMap, setLoadingPodMap] = useState<Record<string, boolean>>({});
  const [terminalVisible, setTerminalVisible] = useState(false);
  const [terminalPod, setTerminalPod] = useState<{ cluster: string; namespace: string; pod: string }>({
    cluster: '',
    namespace: '',
    pod: '',
  });

  // 添加搜索状态
  const [deploymentSearchText, setDeploymentSearchText] = useState<string>('');
  const [serviceSearchText, setServiceSearchText] = useState<string>('');
  const [ingressSearchText, setIngressSearchText] = useState<string>('');

  // 在 ClusterManagement 组件的状态变量部分添加新的状态变量
  const [ingressFormVisible, setIngressFormVisible] = useState<boolean>(false);
  const [ingressFormData, setIngressFormData] = useState<any>({
    name: '',
    host: '',
    paths: [{ path: '/', serviceName: '', servicePort: '' }],
    tls: {
      enabled: false,
      secretName: '',
    }
  });
  const [availableServices, setAvailableServices] = useState<any[]>([]);

  // 修改命名空间搜索状态变量
  const [namespaceSearchText, setNamespaceSearchText] = useState<string>('');
  const [namespaceDropdownOpen, setNamespaceDropdownOpen] = useState<boolean>(false);

  // 获取集群列表 - 不再需要从后端获取，使用固定选项
  const fetchClusters = async () => {
    // 不再需要从后端获取，直接使用固定选项
    // 但仍然需要获取命名空间
    if (selectedCluster) {
      fetchNamespaces(selectedCluster);
    }
  };

  // 获取命名空间列表
  const fetchNamespaces = async (clusterName: string) => {
    if (!clusterName) return;
    try {
      setLoading(true);
      const res = await cluster.list_namespaces({ cluster: clusterName });
      if (res.result === 'SUCCESS') {
        setNamespaceList(res.data || []);
        
        // 如果是 ali-test 集群，默认选择 barren-dev 命名空间
        if (clusterName === 'ali-test') {
          const barrenDevExists = (res.data || []).some((ns: any) => ns.name === 'barren-dev');
          if (barrenDevExists) {
            setSelectedNamespace('barren-dev');
          } else if (res.data && res.data.length > 0) {
            setSelectedNamespace(res.data[0].name);
          } else {
            setSelectedNamespace('');
          }
        } else if (res.data && res.data.length > 0) {
          setSelectedNamespace(res.data[0].name);
        } else {
          setSelectedNamespace('');
        }
      } else {
        message.error(res.message || '获取命名空间列表失败');
      }
    } catch (error) {
      console.error('获取命名空间列表失败:', error);
      message.error('获取命名空间列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取Deployment列表
  const fetchDeployments = async (clusterName: string, namespace: string) => {
    if (!clusterName || !namespace) return;
    try {
      setLoading(true);
      const res = await cluster.list_deployments({ cluster: clusterName, namespace });
      if (res.result === 'SUCCESS') {
        // 确保数据格式正确，处理API返回的数据
        const processedData = (res.data || []).map((item: any) => {
          // 如果API返回的是字符串形式的JSON，则解析它
          if (typeof item === 'string') {
            try {
              return JSON.parse(item);
            } catch (e) {
              console.error('解析Deployment数据失败:', e);
              return item;
            }
          }
          return item;
        });
        
        // 按创建时间排序，最新的在前面
        processedData.sort((a: Record<string, any>, b: Record<string, any>) => {
          const timeA = a.metadata?.creationTimestamp || '';
          const timeB = b.metadata?.creationTimestamp || '';
          return timeB.localeCompare(timeA); // 降序排列
        });
        
        setDeployments(processedData);
      } else {
        message.error(res.message || '获取Deployment列表失败');
      }
    } catch (error) {
      console.error('获取Deployment列表失败:', error);
      message.error('获取Deployment列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取Service列表
  const fetchServices = async (clusterName: string, namespace: string) => {
    if (!clusterName || !namespace) return;
    try {
      setLoading(true);
      const res = await cluster.list_services({ cluster: clusterName, namespace });
      if (res.result === 'SUCCESS') {
        const processedData = res.data || [];
        
        // 按创建时间排序，最新的在前面
        processedData.sort((a: Record<string, any>, b: Record<string, any>) => {
          const timeA = a.metadata?.creationTimestamp || '';
          const timeB = b.metadata?.creationTimestamp || '';
          return timeB.localeCompare(timeA); // 降序排列
        });
        
        setServices(processedData);
      } else {
        message.error(res.message || '获取Service列表失败');
      }
    } catch (error) {
      console.error('获取Service列表失败:', error);
      message.error('获取Service列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取Ingress列表
  const fetchIngresses = async (clusterName: string, namespace: string) => {
    if (!clusterName || !namespace) return;
    try {
      setLoading(true);
      const res = await cluster.list_ingresses({ cluster: clusterName, namespace });
      if (res.result === 'SUCCESS') {
        // 确保数据格式正确，处理API返回的数据
        const processedData = (res.data || []).map((item: any) => {
          // 如果API返回的是字符串形式的JSON，则解析它
          if (typeof item === 'string') {
            try {
              return JSON.parse(item);
            } catch (e) {
              console.error('解析Ingress数据失败:', e);
              return item;
            }
          }
          
          // 处理简化的数据格式（直接包含rules而不是在spec下）
          if (item.rules && !item.spec) {
            // 从名称中提取可能的ingress class
            let ingressClass = '';
            if (item.name) {
              if (item.name.includes('-internal-')) {
                ingressClass = 'ingress-internal';
              } else if (item.name.includes('-external-')) {
                ingressClass = 'ingress-external';
              }
            }
            
            // 构建annotations对象
            const annotations = item.annotations || {};
            if (ingressClass && !annotations['kubernetes.io/ingress.class']) {
              annotations['kubernetes.io/ingress.class'] = ingressClass;
            }
            
            return {
              ...item,
              spec: {
                rules: item.rules,
                tls: item.tls || []
              },
              metadata: {
                name: item.name,
                creationTimestamp: item.creationTimestamp,
                namespace: item.namespace || selectedNamespace,
                annotations: annotations,
                labels: item.labels || {}
              },
              status: item.status || {},
              // 保留原始annotations以防metadata中的被覆盖
              annotations: annotations
            };
          }
          
          // 确保标准格式也能访问到annotations
          if (item.metadata?.annotations) {
            return {
              ...item,
              // 添加一个直接的annotations属性，方便访问
              annotations: item.metadata.annotations
            };
          }
          
          return item;
        });
        
        // 按创建时间排序，最新的在前面
        processedData.sort((a: Record<string, any>, b: Record<string, any>) => {
          const timeA = a.metadata?.creationTimestamp || a.creationTimestamp || '';
          const timeB = b.metadata?.creationTimestamp || b.creationTimestamp || '';
          return timeB.localeCompare(timeA); // 降序排列
        });
        
        // 添加调试日志
        console.log('Processed Ingress data sample:', 
          processedData.length > 0 ? 
          { 
            name: processedData[0].metadata?.name || processedData[0].name,
            annotations: processedData[0].metadata?.annotations || processedData[0].annotations || {}
          } : 'No ingress data');
        
        setIngresses(processedData);
      } else {
        message.error(res.message || '获取Ingress列表失败');
      }
    } catch (error) {
      console.error('获取Ingress列表失败:', error);
      message.error('获取Ingress列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 格式化JSON数据，确保一致的美化格式，移除managedFields字段和转义字符
  const formatJsonData = (data: any): string => {
    try {
      // 确保数据是对象格式
      let jsonData = typeof data === 'string' ? JSON.parse(data) : JSON.parse(JSON.stringify(data));
      
      // 移除managedFields字段
      if (jsonData && typeof jsonData === 'object' && 'metadata' in jsonData && jsonData.metadata && typeof jsonData.metadata === 'object') {
        delete jsonData.metadata.managedFields;
        
        // 移除last-applied-configuration注解
        if (jsonData.metadata.annotations && 
            typeof jsonData.metadata.annotations === 'object' && 
            "kubectl.kubernetes.io/last-applied-configuration" in jsonData.metadata.annotations) {
          delete jsonData.metadata.annotations["kubectl.kubernetes.io/last-applied-configuration"];
        }
      }
      
      // 使用2个空格缩进美化JSON
      const formattedJson = JSON.stringify(jsonData, null, 2);
      
      return formattedJson;
    } catch (error) {
      console.error('JSON格式化失败:', error);
      // 如果解析失败，返回原始数据
      return typeof data === 'string' ? data : JSON.stringify(data, null, 2);
    }
  };

  // 保存前处理JSON数据 - 移除managedFields字段
  const prepareJsonForSave = (jsonString: string): string => {
    try {
      // 解析JSON
      const jsonData = JSON.parse(jsonString);
      
      // 移除managedFields字段
      if (jsonData && typeof jsonData === 'object' && 'metadata' in jsonData && jsonData.metadata && typeof jsonData.metadata === 'object') {
        delete jsonData.metadata.managedFields;
        
        // 移除last-applied-configuration注解
        if (jsonData.metadata.annotations && 
            typeof jsonData.metadata.annotations === 'object' && 
            "kubectl.kubernetes.io/last-applied-configuration" in jsonData.metadata.annotations) {
          delete jsonData.metadata.annotations["kubectl.kubernetes.io/last-applied-configuration"];
        }
      }
      
      // 重新格式化
      return JSON.stringify(jsonData, null, 2);
    } catch (error) {
      console.error('JSON处理失败:', error);
      return jsonString;
    }
  };

  // 格式化JSON数据用于显示 - 移除转义字符和managedFields字段
  const formatJsonForDisplay = (jsonString: string): string => {
    try {
      // 解析JSON
      const jsonData = JSON.parse(jsonString);
      
      // 移除managedFields字段
      if (jsonData && typeof jsonData === 'object' && 'metadata' in jsonData && jsonData.metadata && typeof jsonData.metadata === 'object') {
        delete jsonData.metadata.managedFields;
        
        // 移除last-applied-configuration注解
        if (jsonData.metadata.annotations && 
            typeof jsonData.metadata.annotations === 'object' && 
            "kubectl.kubernetes.io/last-applied-configuration" in jsonData.metadata.annotations) {
          delete jsonData.metadata.annotations["kubectl.kubernetes.io/last-applied-configuration"];
        }
      }
      
      // 使用JSON5格式化，减少转义字符
      return JSON.stringify(jsonData, null, 2)
        // 移除字符串中的转义字符，使其更易读
        .replace(/\\"/g, '"')  // 替换 \" 为 "
        .replace(/\\\\/g, '\\') // 替换 \\ 为 \
        .replace(/\\n/g, '\n') // 替换 \n 为实际的换行符
        .replace(/\\t/g, '\t') // 替换 \t 为实际的制表符
        .replace(/\\r/g, '\r'); // 替换 \r 为实际的回车符
    } catch (error) {
      console.error('JSON格式化失败:', error);
      return jsonString;
    }
  };

  // JSON转YAML
  const jsonToYaml = (jsonData: any): string => {
    try {
      // 确保数据是对象格式
      const data = typeof jsonData === 'string' ? JSON.parse(jsonData) : jsonData;
      
      // 移除managedFields字段
      if (data && typeof data === 'object' && 'metadata' in data && data.metadata && typeof data.metadata === 'object') {
        delete data.metadata.managedFields;
        
        // 移除last-applied-configuration注解
        if (data.metadata.annotations && 
            typeof data.metadata.annotations === 'object' && 
            "kubectl.kubernetes.io/last-applied-configuration" in data.metadata.annotations) {
          delete data.metadata.annotations["kubectl.kubernetes.io/last-applied-configuration"];
        }
      }
      
      // 转换为YAML
      return yaml.dump(data, { indent: 2 });
    } catch (error) {
      console.error('JSON转YAML失败:', error);
      return typeof jsonData === 'string' ? jsonData : JSON.stringify(jsonData, null, 2);
    }
  };

  // YAML转JSON
  const yamlToJson = (yamlData: string): string => {
    try {
      // 解析YAML
      const data = yaml.load(yamlData);
      
      // 移除managedFields字段
      if (data && typeof data === 'object' && 'metadata' in data) {
        delete data.metadata.managedFields;
        
        // 移除last-applied-configuration注解
        if (data.metadata.annotations && data.metadata.annotations["kubectl.kubernetes.io/last-applied-configuration"]) {
          delete data.metadata.annotations["kubectl.kubernetes.io/last-applied-configuration"];
        }
      }
      
      // 转换为JSON
      return JSON.stringify(data, null, 2);
    } catch (error) {
      console.error('YAML转JSON失败:', error);
      return yamlData;
    }
  };

  // 格式化数据用于显示
  const formatDataForDisplay = (data: any): string => {
    try {
      // 如果使用YAML格式
      if (useYamlFormat) {
        return jsonToYaml(data);
      } 
      // 如果使用JSON格式
      else {
        return formatJsonData(data);
      }
    } catch (error) {
      console.error('数据格式化失败:', error);
      return typeof data === 'string' ? data : JSON.stringify(data, null, 2);
    }
  };

  // 保存前处理数据
  const prepareDataForSave = (content: string): string => {
    try {
      // 如果使用YAML格式，先转换为JSON
      if (useYamlFormat) {
        return yamlToJson(content);
      } 
      // 如果使用JSON格式
      else {
        return prepareJsonForSave(content);
      }
    } catch (error) {
      console.error('数据处理失败:', error);
      return content;
    }
  };

  // 获取资源详情
  const fetchResourceDetail = async (type: string, name: string) => {
    if (!selectedCluster || !selectedNamespace || !type || !name) return;
    try {
      setLoading(true);
      const res = await cluster.get_resource({
        cluster: selectedCluster,
        namespace: selectedNamespace,
        resourceType: type,
        name,
      });
      if (res.result === 'SUCCESS') {
        // 使用统一的格式化函数处理数据
        setYamlContent(formatDataForDisplay(res.data));
        setModalVisible(true);
        setModalType('view');
        setModalTitle(`查看 ${type} - ${name}`);
        setResourceType(type);
        setResourceName(name);
      } else {
        message.error(res.message || `获取${type}详情失败`);
      }
    } catch (error) {
      console.error(`获取${type}详情失败:`, error);
      message.error(`获取${type}详情失败`);
    } finally {
      setLoading(false);
    }
  };

  // 创建资源
  const createResource = async (type: string) => {
    if (type === 'ingress') {
      // 获取可用的服务列表
      await fetchServices(selectedCluster, selectedNamespace);
      setIngressFormVisible(true);
      setIngressFormData({
        name: '',
        host: '',
        paths: [{ path: '/', serviceName: '', servicePort: '' }],
        tls: {
          enabled: false,
          secretName: '',
        }
      });
    } else {
      setModalVisible(true);
      setModalType('create');
      setModalTitle(`创建 ${type}`);
      setResourceType(type);
      setResourceName('');
      setYamlContent('');
    }
  };

  // 编辑资源
  const editResource = async (type: string, name: string) => {
    await fetchResourceDetail(type, name);
    setModalType('edit');
    setModalTitle(`编辑 ${type} - ${name}`);
  };

  // 删除资源
  const deleteResource = async (type: string, name: string) => {
    if (!selectedCluster || !selectedNamespace) return;
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除 ${type} ${name} 吗？`,
      onOk: async () => {
        try {
          setLoading(true);
          const res = await cluster.delete_resource({
            cluster: selectedCluster,
            namespace: selectedNamespace,
            resourceType: type,
            name,
          });
          if (res.result === 'SUCCESS') {
            message.success(`删除 ${type} 成功`);
            // 刷新列表
            refreshResourceList();
          } else {
            message.error(res.message || `删除 ${type} 失败`);
          }
        } catch (error) {
          console.error(`删除 ${type} 失败:`, error);
          message.error(`删除 ${type} 失败`);
        } finally {
          setLoading(false);
        }
      },
    });
  };

  // 保存资源
  const saveResource = async () => {
    if (!selectedCluster || !selectedNamespace || !resourceType || !yamlContent) {
      message.error('参数不完整');
      return;
    }

    try {
      // 尝试解析和格式化数据，确保格式正确
      let formattedContent;
      try {
        // 处理数据 - 转换格式并移除不需要的字段
        formattedContent = prepareDataForSave(yamlContent);
        
        // 验证JSON格式
        JSON.parse(formattedContent);
      } catch (error) {
        console.error('数据解析失败:', error);
        message.error(useYamlFormat ? 'YAML格式不正确，请检查输入' : 'JSON格式不正确，请检查输入');
        return;
      }

      setLoading(true);
      let res;
      if (modalType === 'create') {
        res = await cluster.create_resource({
          cluster: selectedCluster,
          namespace: selectedNamespace,
          resourceType,
          yamlData: formattedContent,
        });
      } else if (modalType === 'edit') {
        res = await cluster.update_resource({
          cluster: selectedCluster,
          namespace: selectedNamespace,
          resourceType,
          name: resourceName,
          yamlData: formattedContent,
        });
      }

      if (res && res.result === 'SUCCESS') {
        message.success(`${modalType === 'create' ? '创建' : '更新'} ${resourceType} 成功`);
        setModalVisible(false);
        // 刷新列表
        refreshResourceList();
      } else {
        message.error(res?.message || `${modalType === 'create' ? '创建' : '更新'} ${resourceType} 失败`);
      }
    } catch (error) {
      console.error(`${modalType === 'create' ? '创建' : '更新'} ${resourceType} 失败:`, error);
      message.error(`${modalType === 'create' ? '创建' : '更新'} ${resourceType} 失败`);
    } finally {
      setLoading(false);
    }
  };

  // 刷新资源列表
  const refreshResourceList = () => {
    if (activeTab === 'deployments') {
      fetchDeployments(selectedCluster, selectedNamespace);
    } else if (activeTab === 'services') {
      fetchServices(selectedCluster, selectedNamespace);
    } else if (activeTab === 'ingresses') {
      fetchIngresses(selectedCluster, selectedNamespace);
    }
  };

  // 集群变更
  const handleClusterChange = (value: string) => {
    setSelectedCluster(value);
    setSelectedNamespace('');
    setDeployments([]);
    setServices([]);
    setIngresses([]);
  };

  // 命名空间变更
  const handleNamespaceChange = (value: string) => {
    setSelectedNamespace(value);
    setDeployments([]);
    setServices([]);
    setIngresses([]);
  };

  // 标签页变更
  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  // 格式化当前编辑器中的内容
  const formatCurrentContent = () => {
    try {
      let formattedContent;
      
      // 根据当前格式选择处理方式
      if (useYamlFormat) {
        // 解析YAML，然后重新格式化
        const jsonObj = yaml.load(yamlContent);
        
        // 移除不需要的字段
        if (jsonObj && typeof jsonObj === 'object' && 'metadata' in jsonObj) {
          delete jsonObj.metadata.managedFields;
          
          if (jsonObj.metadata.annotations && jsonObj.metadata.annotations["kubectl.kubernetes.io/last-applied-configuration"]) {
            delete jsonObj.metadata.annotations["kubectl.kubernetes.io/last-applied-configuration"];
          }
        }
        
        // 转回YAML
        formattedContent = yaml.dump(jsonObj, { indent: 2 });
      } else {
        // 使用JSON格式化
        formattedContent = prepareJsonForSave(yamlContent);
      }
      
      setYamlContent(formattedContent);
      message.success(useYamlFormat ? 'YAML格式化成功' : 'JSON格式化成功');
    } catch (error) {
      console.error('格式化失败:', error);
      message.error(useYamlFormat ? 'YAML格式不正确，无法格式化' : 'JSON格式不正确，无法格式化');
    }
  };

  // 切换格式（JSON/YAML）
  const toggleFormat = () => {
    try {
      let newContent;
      
      // 从当前格式转换到另一种格式
      if (useYamlFormat) {
        // 从YAML转为JSON
        const jsonObj = yaml.load(yamlContent);
        newContent = JSON.stringify(jsonObj, null, 2);
      } else {
        // 从JSON转为YAML
        const jsonObj = JSON.parse(yamlContent);
        newContent = yaml.dump(jsonObj, { indent: 2 });
      }
      
      // 更新状态
      setYamlContent(newContent);
      setUseYamlFormat(!useYamlFormat);
    } catch (error) {
      console.error('格式转换失败:', error);
      message.error('格式转换失败，请检查内容格式');
    }
  };

  // 获取指定 Deployment 下的所有 Pod
  const fetchPodsByDeployment = async (deploymentName: string) => {
    if (!selectedCluster || !selectedNamespace) return;
    
    try {
      // 设置对应 Deployment 的 Pod 加载状态
      setLoadingPodMap(prev => ({ ...prev, [deploymentName]: true }));
      
      const res = await cluster.list_deployment_pods({
        cluster: selectedCluster,
        namespace: selectedNamespace,
        deployment: deploymentName
      });
      
      if (res.result === 'SUCCESS') {
        // 更新 Pod 数据映射
        setPodDataMap(prev => ({ 
          ...prev, 
          [deploymentName]: res.data || [] 
        }));
        
        // 展开对应的行
        setExpandedRowKeys(prev => 
          prev.includes(deploymentName) ? prev : [...prev, deploymentName]
        );
      } else {
        message.error(res.message || '获取Pod列表失败');
      }
    } catch (error) {
      console.error('获取Pod列表失败:', error);
      message.error('获取Pod列表失败');
    } finally {
      // 清除加载状态
      setLoadingPodMap(prev => ({ ...prev, [deploymentName]: false }));
    }
  };

  // 查看 Pod 详情
  const handleViewPods = (deploymentName: string) => {
    // 如果已经展开且有数据，则收起；否则获取数据并展开
    if (expandedRowKeys.includes(deploymentName) && podDataMap[deploymentName]) {
      setExpandedRowKeys(prev => prev.filter(key => key !== deploymentName));
    } else {
      fetchPodsByDeployment(deploymentName);
    }
  };

  // 打开终端
  const openTerminal = (podName: string) => {
    setTerminalPod({
      cluster: selectedCluster,
      namespace: selectedNamespace,
      pod: podName,
    });
    setTerminalVisible(true);
  };

  // 关闭终端
  const closeTerminal = () => {
    setTerminalVisible(false);
  };

  // 处理搜索函数
  const handleDeploymentSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setDeploymentSearchText(e.target.value);
  };
  
  const handleServiceSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setServiceSearchText(e.target.value);
  };
  
  const handleIngressSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setIngressSearchText(e.target.value);
  };
  
  // 过滤数据函数
  const getFilteredDeployments = () => {
    if (!deploymentSearchText) return deployments;
    return deployments.filter(item => 
      item.name.toLowerCase().includes(deploymentSearchText.toLowerCase())
    );
  };
  
  const getFilteredServices = () => {
    if (!serviceSearchText) return services;
    return services.filter(item => 
      item.name.toLowerCase().includes(serviceSearchText.toLowerCase())
    );
  };
  
  const getFilteredIngresses = () => {
    if (!ingressSearchText) return ingresses;
    return ingresses.filter(item => 
      item.name.toLowerCase().includes(ingressSearchText.toLowerCase())
    );
  };

  // 修改命名空间搜索处理函数
  const handleNamespaceSearch = (value: string) => {
    if (namespaceDropdownOpen) {
      setNamespaceSearchText(value);
    }
  };

  // 添加下拉框打开/关闭处理函数
  const handleNamespaceDropdownVisibleChange = (open: boolean) => {
    setNamespaceDropdownOpen(open);
    if (open) {
      // 当下拉框打开时，重置搜索文本，显示所有命名空间
      setNamespaceSearchText('');
    }
  };

  // 添加过滤命名空间的函数
  const getFilteredNamespaces = () => {
    if (!namespaceSearchText) return namespaceList;
    return namespaceList.filter(item => 
      item.name.toLowerCase().includes(namespaceSearchText.toLowerCase())
    );
  };

  // 初始化
  useEffect(() => {
    // 不再需要获取集群列表，直接获取命名空间
    fetchNamespaces(selectedCluster);
  }, []);

  // 集群变更时获取命名空间
  useEffect(() => {
    if (selectedCluster) {
      fetchNamespaces(selectedCluster);
    }
  }, [selectedCluster]);

  // 命名空间变更时获取资源列表
  useEffect(() => {
    if (selectedCluster && selectedNamespace) {
      if (activeTab === 'deployments') {
        fetchDeployments(selectedCluster, selectedNamespace);
      } else if (activeTab === 'services') {
        fetchServices(selectedCluster, selectedNamespace);
      } else if (activeTab === 'ingresses') {
        fetchIngresses(selectedCluster, selectedNamespace);
      }
    }
  }, [selectedCluster, selectedNamespace, activeTab]);

  // 添加创建 Ingress 的函数
  const handleIngressFormSubmit = async () => {
    if (!selectedCluster || !selectedNamespace) {
      message.error('请选择集群和命名空间');
      return;
    }

    if (!ingressFormData.name) {
      message.error('请输入 Ingress 名称');
      return;
    }

    if (!ingressFormData.host) {
      message.error('请输入主机名');
      return;
    }

    // 验证 TLS 配置
    if (ingressFormData.tls.enabled && !ingressFormData.tls.secretName) {
      message.error('请输入 TLS Secret 名称');
      return;
    }

    // 验证每个路径都有服务和端口
    const invalidPath = ingressFormData.paths.find(
      (p: any) => !p.serviceName || !p.servicePort
    );
    if (invalidPath) {
      message.error('请为每个路径选择服务和端口');
      return;
    }

    try {
      setLoading(true);
      
      // 提取主机名末尾两段，用于确定 ingress class
      const hostParts = ingressFormData.host.split('.');
      const domainSuffix = hostParts.length >= 2 ? 
        `${hostParts[hostParts.length - 2]}.${hostParts[hostParts.length - 1]}` : 
        ingressFormData.host;
      
      // 根据域名后缀确定 ingress class
      let ingressClass = '';
      if (domainSuffix === 'blacklake.cn') {
        ingressClass = 'ingress-external';
      } else if (domainSuffix === 'blacklake.tech') {
        ingressClass = 'ingress-internal';
      }
      
      // 构建 Ingress YAML
      const ingressYaml: {
        apiVersion: string;
        kind: string;
        metadata: { 
          name: string; 
          namespace: string;
          annotations?: Record<string, string>;
        };
        spec: {
          rules: Array<{
            host: string;
            http: {
              paths: Array<{
                path: string;
                pathType: string;
                backend: {
                  service: {
                    name: string;
                    port: {
                      number: number;
                    };
                  };
                };
              }>;
            };
          }>;
          tls?: Array<{
            hosts: string[];
            secretName: string;
          }>;
        };
      } = {
        apiVersion: 'networking.k8s.io/v1',
        kind: 'Ingress',
        metadata: {
          name: ingressFormData.name,
          namespace: selectedNamespace,
          annotations: {}
        },
        spec: {
          rules: [
            {
              host: ingressFormData.host,
              http: {
                paths: ingressFormData.paths.map((p: any) => ({
                  path: p.path,
                  pathType: 'Prefix',
                  backend: {
                    service: {
                      name: p.serviceName,
                      port: {
                        number: parseInt(p.servicePort, 10)
                      }
                    }
                  }
                })),
              }
            }
          ]
        }
      };

      // 如果有确定的 ingress class，添加到 annotations
      if (ingressClass) {
        ingressYaml.metadata.annotations = {
          'kubernetes.io/ingress.class': ingressClass
        };
      }

      // 如果启用了 TLS，添加 TLS 配置
      if (ingressFormData.tls.enabled && ingressFormData.tls.secretName) {
        ingressYaml.spec.tls = [
          {
            hosts: [ingressFormData.host],
            secretName: ingressFormData.tls.secretName
          }
        ];
      }

      // 转换为 YAML 字符串
      const yamlString = jsonToYaml(ingressYaml);
      
      // 调用创建资源的 API
      const res = await cluster.create_resource({
        cluster: selectedCluster,
        namespace: selectedNamespace,
        resourceType: 'ingress',
        yamlData: yamlString,
      });

      if (res.result === 'SUCCESS') {
        message.success('创建 Ingress 成功');
        setIngressFormVisible(false);
        // 刷新列表
        if (activeTab === 'ingresses') {
          fetchIngresses(selectedCluster, selectedNamespace);
        }
      } else {
        message.error(res.message || '创建 Ingress 失败');
      }
    } catch (error) {
      console.error('创建 Ingress 失败:', error);
      message.error('创建 Ingress 失败');
    } finally {
      setLoading(false);
    }
  };

  // 添加路径
  const addIngressPath = () => {
    setIngressFormData({
      ...ingressFormData,
      paths: [...ingressFormData.paths, { path: '/', serviceName: '', servicePort: '' }]
    });
  };

  // 删除路径
  const removeIngressPath = (index: number) => {
    const newPaths = [...ingressFormData.paths];
    newPaths.splice(index, 1);
    setIngressFormData({
      ...ingressFormData,
      paths: newPaths
    });
  };

  // 更新路径信息
  const updateIngressPath = (index: number, field: string, value: string) => {
    const newPaths = [...ingressFormData.paths];
    newPaths[index] = { ...newPaths[index], [field]: value };
    setIngressFormData({
      ...ingressFormData,
      paths: newPaths
    });
  };

  // 获取服务的端口列表
  const getServicePorts = (serviceName: string) => {
    const service = services.find((s: any) => s.name === serviceName);
    if (!service) return [];
    
    // 检查端口信息的位置
    let ports = [];
    if (service.ports) {
      ports = service.ports;
    } else if (service.spec && service.spec.ports) {
      ports = service.spec.ports;
    }
    
    if (!ports || ports.length === 0) {
      console.log('未找到服务端口信息:', service);
      return [];
    }
    
    return ports.map((port: any) => ({
      label: `${port.port} (${port.name || 'default'})`,
      value: port.port
    }));
  };

  // Deployment表格列
  const deploymentColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 200, // 设置固定宽度
      ellipsis: true, // 超出宽度时显示省略号
      render: (text: string, record: any) => {
        const labels = record.metadata?.labels || {};
        const appName = labels['app.kubernetes.io/name'] || labels['app'] || '';
        return (
          <Tooltip title={text} placement="topLeft">
            <div style={{ 
              wordBreak: 'break-all',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}>
              <div>{text}</div>
              {appName && appName !== text && <div style={{ fontSize: '12px', color: '#888' }}>{appName}</div>}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: '副本数',
      key: 'replicas',
      render: (text: string, record: any) => {
        const ready = record.status?.readyReplicas || 0;
        const desired = record.spec?.replicas || 0;
        const replicaStatus = `${ready}/${desired}`;
        let color = '#52c41a'; // 绿色
        if (ready < desired) {
          color = '#faad14'; // 黄色
        }
        if (ready === 0 && desired > 0) {
          color = '#f5222d'; // 红色
        }
        return <span style={{ color }}>{replicaStatus}</span>;
      },
    },
    {
      title: '状态',
      key: 'status',
      render: (text: string, record: any) => {
        const conditions = record.status?.conditions || [];
        const availableCondition = conditions.find((c: any) => c.type === 'Available');
        const progressingCondition = conditions.find((c: any) => c.type === 'Progressing');
        
        let status = '未知';
        let color = '';
        
        if (availableCondition && availableCondition.status === 'True') {
          status = '可用';
          color = '#52c41a'; // 绿色
        } else if (progressingCondition && progressingCondition.status === 'True') {
          status = '部署中';
          color = '#1890ff'; // 蓝色
        } else {
          status = '不可用';
          color = '#f5222d'; // 红色
        }
        
        return <span style={{ color }}>{status}</span>;
      },
    },
    {
      title: '创建时间',
      dataIndex: ['metadata', 'creationTimestamp'],
      key: 'creationTimestamp',
      width: 140,
      render: (text: string, record: any) => {
        // 直接从record获取，以支持简化格式
        const timestamp = text || record.creationTimestamp || record.metadata?.creationTimestamp;
        return formatDate(timestamp);
      },
    },
    {
      title: '标签',
      key: 'labels',
      render: (text: string, record: any) => {
        const labels = record.metadata?.labels || {};
        const importantLabels = ['app', 'version', 'env', 'app.kubernetes.io/name'];
        
        return (
          <div style={{ maxWidth: 200 }}>
            {Object.entries(labels)
              .filter(([key]) => importantLabels.includes(key))
              .map(([key, value]) => (
                <div key={key} style={{ fontSize: '12px' }}>
                  <span style={{ color: '#1890ff' }}>{key}</span>: {value}
                </div>
              ))}
          </div>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_: any, record: any) => (
        <Space size="middle">
          <Button 
            type="link" 
            icon={<EyeOutlined />} 
            onClick={() => handleViewPods(record.name)}
          >
            查看详情
          </Button>
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            onClick={() => editResource('deployments', record.name)}
          >
            编辑
          </Button>
          { false && <Button 
            type="link" 
            icon={<DeleteOutlined />} 
            danger
            onClick={() => deleteResource('deployments', record.name)}
          >
            删除
          </Button> }
        </Space>
      ),
    },
  ];

  // Service表格列
  const serviceColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 200, // 设置固定宽度
      ellipsis: true, // 超出宽度时显示省略号
      render: (text: string) => (
        <Tooltip title={text} placement="topLeft">
          <span style={{ 
            display: 'inline-block',
            width: '100%',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}>
            {text}
          </span>
        </Tooltip>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 140,
    },
    {
      title: 'Cluster IP',
      dataIndex: 'clusterIP',
      key: 'clusterIP',
      width: 140,
    },
    {
      title: '创建时间',
      dataIndex: 'creationTimestamp',
      key: 'creationTimestamp',
      width: 140,
      sorter: (a: any, b: any) => {
        const timeA = a.metadata?.creationTimestamp || a.creationTimestamp || '';
        const timeB = b.metadata?.creationTimestamp || b.creationTimestamp || '';
        return timeA.localeCompare(timeB);
      },
      defaultSortOrder: 'descend' as const,
      render: (text: string, record: any) => {
        // 直接从record获取，以支持简化格式
        const timestamp = text || record.creationTimestamp || record.metadata?.creationTimestamp;
        return formatDate(timestamp);
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (text: string, record: any) => (
        <Space size="middle">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => fetchResourceDetail('service', record.name)}
          >
            查看
          </Button>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => editResource('service', record.name)}
          >
            编辑
          </Button>
          { false && <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => deleteResource('service', record.name)}
          >
            删除
          </Button> }
        </Space>
      ),
    },
  ];

  // Ingress表格列
  const ingressColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 180, // 设置固定宽度
      ellipsis: true, // 超出宽度时显示省略号
      render: (text: string, record: any) => {
        const labels = record.metadata?.labels || {};
        const appId = labels.appid || '';
        
        return (
          <Tooltip title={text} placement="topLeft">
            <div style={{ 
              wordBreak: 'break-all',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}>
              <div>{text}</div>
              <div style={{ fontSize: '12px', color: '#888' }}>
                {record.metadata?.namespace}
              </div>
              {appId && (
                <div style={{ fontSize: '12px', color: '#1890ff' }}>
                  appid: {appId}
                </div>
              )}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: 'Host',
      key: 'host',
      width: 250,
      render: (text: string, record: any) => {
        // 支持两种数据格式：标准Kubernetes格式和简化格式
        const rules = record.spec?.rules || record.rules || [];
        const tls = record.spec?.tls || record.tls || [];
        
        if (rules.length === 0) return '-';
        
        return (
          <div style={{ wordBreak: 'break-all' }}>
            {rules.map((rule: any, index: number) => {
              const host = rule.host || '-';
              const hasHttps = tls && tls.length > 0 && tls.some((t: any) => t.hosts && t.hosts.includes(rule.host));
              
              return (
                <div key={index} style={{ 
                  padding: '4px 8px', 
                  margin: '2px 0', 
                  background: '#f5f5f5', 
                  borderRadius: '4px',
                  borderLeft: hasHttps ? '3px solid #52c41a' : '3px solid #1890ff'
                }}>
                  {host}
                  {hasHttps && (
                    <span style={{ 
                      marginLeft: 8, 
                      color: '#52c41a', 
                      fontSize: '12px',
                      background: 'rgba(82, 196, 26, 0.1)',
                      padding: '1px 4px',
                      borderRadius: '2px'
                    }}>
                      HTTPS
                    </span>
                  )}
                </div>
              );
            })}
            {tls && tls.length > 0 && tls[0].secretName && (
              <div style={{ fontSize: '12px', color: '#888', marginTop: 4 }}>
                Secret: <span style={{ color: '#1890ff' }}>{tls[0].secretName}</span>
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: '路径映射',
      key: 'paths',
      width: 300,
      render: (text: string, record: any) => {
        // 支持两种数据格式：标准Kubernetes格式和简化格式
        const rules = record.spec?.rules || record.rules || [];
        if (rules.length === 0) return '-';
        
        return (
          <div style={{ wordBreak: 'break-all' }}>
            {rules.map((rule: any, ruleIndex: number) => {
              const paths = rule.http?.paths || [];
              if (paths.length === 0) return null;
              
              return (
                <div key={ruleIndex} style={{ marginBottom: ruleIndex < rules.length - 1 ? 12 : 0 }}>
                  {rule.host && paths.length > 0 && (
                    <div style={{ 
                      fontSize: '12px', 
                      color: '#888', 
                      marginBottom: '4px',
                      fontWeight: 'bold'
                    }}>
                      {rule.host}:
                    </div>
                  )}
                  {paths.map((path: any, pathIndex: number) => {
                    const pathType = path.pathType || '';
                    const pathValue = path.path || '/';
                    const serviceName = path.backend?.service?.name || path.backend?.serviceName || '';
                    const servicePort = path.backend?.service?.port?.number || path.backend?.servicePort || '';
                    
                    // 为不同类型的路径设置不同的颜色
                    let pathColor = '#1890ff';
                    let borderColor = '#1890ff';
                    
                    if (pathValue === '/') {
                      pathColor = '#52c41a';
                      borderColor = '#52c41a';
                    } else if (pathValue.includes('*')) {
                      pathColor = '#faad14';
                      borderColor = '#faad14';
                    }
                    
                    return (
                      <div 
                        key={`${ruleIndex}-${pathIndex}`} 
                        style={{ 
                          marginBottom: 8,
                          padding: '4px 8px',
                          background: '#f9f9f9',
                          borderRadius: '4px',
                          borderLeft: `3px solid ${borderColor}`
                        }}
                      >
                        <div>
                          <span style={{ 
                            color: pathColor,
                            fontWeight: 'bold'
                          }}>
                            {pathValue || '/'}
                          </span> 
                          <span style={{ margin: '0 4px' }}>→</span> 
                          <span style={{ color: '#1890ff' }}>{serviceName}</span>
                          <span>:{servicePort}</span>
                        </div>
                        {pathType && pathType !== 'ImplementationSpecific' && (
                          <div style={{ fontSize: '12px', color: '#888' }}>
                            类型: {pathType}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              );
            })}
          </div>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: ['metadata', 'creationTimestamp'],
      key: 'creationTimestamp',
      width: 140,
      render: (text: string, record: any) => {
        // 直接从record获取，以支持简化格式
        const timestamp = text || record.creationTimestamp || record.metadata?.creationTimestamp;
        return formatDate(timestamp);
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      render: (text: string, record: any) => (
        <Space size="middle">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => fetchResourceDetail('ingress', record.name)}
          >
            查看
          </Button>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => editResource('ingress', record.name)}
          >
            编辑
          </Button>
          { false && <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => deleteResource('ingress', record.name)}
          >
            删除
          </Button> }
          {process.env.NODE_ENV === 'development' && (
            <Tooltip title="调试数据">
              <Button
                type="text"
                icon={<BugOutlined />}
                onClick={() => {
                  console.log('Ingress Debug Data:', record);
                  Modal.info({
                    title: '调试信息',
                    width: 800,
                    content: (
                      <div>
                        <h3>Annotations:</h3>
                        <pre style={{ maxHeight: '200px', overflow: 'auto', marginBottom: '20px' }}>
                          {JSON.stringify(record.metadata?.annotations || record.annotations || {}, null, 2)}
                        </pre>
                        <h3>完整数据:</h3>
                        <pre style={{ maxHeight: '300px', overflow: 'auto' }}>
                          {JSON.stringify(record, null, 2)}
                        </pre>
                      </div>
                    ),
                  });
                }}
              />
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  // Pod表格列
  const podColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: any) => {
        let color = 'green';
        if (status !== 'Running') {
          color = 'red';
        }
        return <Tag color={color}>{status}</Tag>;
      },
    },
    {
      title: 'Pod IP',
      dataIndex: 'podIP',
      key: 'podIP',
    },
    {
      title: '节点',
      dataIndex: 'hostIP',
      key: 'hostIP',
    },
    {
      title: '重启次数',
      dataIndex: 'restarts',
      key: 'restarts',
    },
    {
      title: '创建时间',
      dataIndex: 'creationTimestamp',
      key: 'creationTimestamp',
      render: (time: any) => formatDate(time),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: any) => (
        <Space>
          <Button 
            type="link" 
            icon={<CodeOutlined />} 
            onClick={() => openTerminal(record.name)}
          >
            终端
          </Button>
          <Button
            type="link"
            onClick={() => handlePodCommand(record.name, 'jstack 1', '线程详情')}
          >
            线程详情
          </Button>
          <Button
            type="link"
            onClick={() => handlePodCommand(record.name, 'top -H -b -n 2', '线程top')}
          >
            线程top
          </Button>
        </Space>
      ),
    },
  ];

  // 添加下载功能的辅助函数
  const downloadThreadDump = (content: string, podName: string, title: string = '线程详情') => {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${title}-${podName}-${timestamp}.txt`;
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // Create a component for the thread analysis modal
  const ThreadAnalysisModal: React.FC<{
    output: string;
    podName: string;
    title: string;
    onClose: () => void;
  }> = ({ output, podName, title, onClose }) => {
    const [analysisResult, setAnalysisResult] = useState<string>('');
    const [analyzing, setAnalyzing] = useState<boolean>(false);

    const analyzeThreadStack = async () => {
      if (!output) {
        message.warning('没有内容可以分析');
        return;
      }

      try {
        setAnalyzing(true);
        const response = await request('/api/ai/analyze', {
          method: 'POST',
          data: {
            content: output,
            type: 'thread_dump'
          },
          timeout: 120000, // 2 minute timeout
        });
        
        if (response && response.data) {
          setAnalysisResult(response.data);
        } else {
          setAnalysisResult('分析完成，但没有返回结果');
        }
      } catch (error) {
        console.error('线程分析失败:', error);
        message.error('线程分析失败');
        setAnalysisResult('分析失败，请稍后重试');
      } finally {
        setAnalyzing(false);
      }
    };

    return (
      <div>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
          <div>
            <Button 
              type="primary" 
              onClick={analyzeThreadStack}
              loading={analyzing}
              icon={<SearchOutlined />}
            >
              AI分析
            </Button>
          </div>
        </div>

        <pre style={{ maxHeight: '400px', overflow: 'auto', marginBottom: '16px' }}>
          {output}
        </pre>
        
        {analyzing && (
          <div style={{ textAlign: 'center', margin: '16px 0' }}>
            <Spin tip="AI正在分析中..." />
          </div>
        )}
        
        {analysisResult && !analyzing && (
          <Card title="AI分析结果" style={{ marginTop: 16 }}>
            <div style={{ 
              padding: '12px', 
              backgroundColor: '#f5f5f5', 
              borderRadius: '4px',
              whiteSpace: 'pre-line'
            }}>
              <div dangerouslySetInnerHTML={{ __html: analysisResult.replace(/\n/g, '<br/>') }} />
            </div>
          </Card>
        )}
        
        <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: 16 }}>
          <Button 
            style={{ marginRight: 8 }}
            onClick={() => {
              navigator.clipboard.writeText(output)
                .then(() => message.success('已复制到剪贴板'))
                .catch(() => message.error('复制失败'));
            }}
          >
            复制
          </Button>
          <Button onClick={onClose} style={{ marginRight: 8 }}>
            关闭
          </Button>
          <Button 
            type="primary" 
            icon={<DownloadOutlined />}
            onClick={() => downloadThreadDump(output, podName, title)}
          >
            下载
          </Button>
        </div>
      </div>
    );
  };

  // Modify handlePodCommand function to use the new component
  const handlePodCommand = async (podName: string, command: string, title: string = '线程详情') => {
    try {
      const response = await fetch(
        `/api/v1/pod/command?cluster=${selectedCluster}&namespace=${selectedNamespace}&pod=${podName}&command=${encodeURIComponent(command)}`
      );
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to execute command');
      }

      // Use Modal with custom content
      const modal = Modal.info({
        title: title,
        width: 800,
        content: (
          <ThreadAnalysisModal 
            output={data.output} 
            podName={podName} 
            title={title} 
            onClose={() => modal.destroy()} 
          />
        ),
        icon: null,
        maskClosable: true,
        okButtonProps: { style: { display: 'none' } } // Hide the default OK button
      });
    } catch (error) {
      message.error('Failed to execute command: ' + (error as Error).message);
    }
  };

  return (
    <PageContainer>
      <Spin spinning={loading}>
        <Card>
          <div style={{ marginBottom: 16 }}>
            <span style={{ marginRight: 8 }}>集群:</span>
            <Select
              style={{ width: 200, marginRight: 16 }}
              value={selectedCluster}
              onChange={handleClusterChange}
              placeholder="请选择集群"
            >
              {clusterList.map((item) => (
                <Option key={item.name} value={item.name}>
                  {item.displayName}
                </Option>
              ))}
            </Select>
            <span style={{ marginRight: 8 }}>命名空间:</span>
            <Select
              style={{ width: 200 }}
              value={selectedNamespace}
              onChange={handleNamespaceChange}
              placeholder="请选择命名空间"
              disabled={!selectedCluster}
              showSearch
              filterOption={false}
              onSearch={handleNamespaceSearch}
              notFoundContent={null}
              onDropdownVisibleChange={handleNamespaceDropdownVisibleChange}
            >
              {getFilteredNamespaces().map((item) => (
                <Option key={item.name} value={item.name}>
                  {item.name}
                </Option>
              ))}
            </Select>
          </div>

          <Tabs activeKey={activeTab} onChange={handleTabChange}>
            <TabPane tab="Deployments" key="deployments">
              <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Input.Search
                  placeholder="搜索 Deployments"
                  onChange={handleDeploymentSearch}
                  style={{ width: 200 }}
                />
                <div>
                  <Button
                    style={{ marginRight: 8 }}
                    icon={<ReloadOutlined />}
                    onClick={() => fetchDeployments(selectedCluster, selectedNamespace)}
                    disabled={!selectedCluster || !selectedNamespace}
                  >
                    刷新
                  </Button>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => createResource('deployment')}
                    disabled={!selectedCluster || !selectedNamespace}
                  >
                    创建 Deployment
                  </Button>
                </div>
              </div>
              <Table
                columns={deploymentColumns}
                dataSource={getFilteredDeployments()}
                rowKey="name"
                loading={loading}
                pagination={{ pageSize: 10 }}
                expandable={{
                  expandedRowKeys,
                  onExpandedRowsChange: (expandedRows) => {
                    setExpandedRowKeys(expandedRows as string[]);
                  },
                  expandedRowRender: (record) => {
                    const deploymentName = record.name;
                    const isLoading = loadingPodMap[deploymentName];
                    const podData = podDataMap[deploymentName] || [];
                    
                    return (
                      <div style={{ margin: '0 16px' }}>
                        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          {/* <h3>Pod 列表</h3> */}
                          <Button 
                            icon={<ReloadOutlined />} 
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              fetchPodsByDeployment(deploymentName);
                            }}
                          >
                            刷新
                          </Button>
                        </div>
                        <Table
                          columns={podColumns}
                          dataSource={podData}
                          rowKey="name"
                          loading={isLoading}
                          pagination={false}
                          size="small"
                        />
                      </div>
                    );
                  },
                }}
              />
            </TabPane>
            <TabPane tab="Services" key="services">
              <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Input.Search
                  placeholder="搜索 Services"
                  onChange={handleServiceSearch}
                  style={{ width: 200 }}
                />
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => createResource('service')}
                  disabled={!selectedCluster || !selectedNamespace}
                >
                  创建 Service
                </Button>
              </div>
              <Table
                columns={serviceColumns}
                dataSource={getFilteredServices()}
                rowKey="name"
                pagination={{ 
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total) => `共 ${total} 条记录`
                }}
                loading={loading && activeTab === 'services'}
                scroll={{ x: 1100 }}
                size="middle"
                bordered
              />
            </TabPane>
            <TabPane tab="Ingresses" key="ingresses">
              <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Input.Search
                  placeholder="搜索 Ingresses"
                  onChange={handleIngressSearch}
                  style={{ width: 200 }}
                />
                <div>
                  <Button
                    style={{ marginRight: 8 }}
                    icon={<ReloadOutlined />}
                    onClick={() => fetchIngresses(selectedCluster, selectedNamespace)}
                    disabled={!selectedCluster || !selectedNamespace}
                  >
                    刷新
                  </Button>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => {
                      fetchServices(selectedCluster, selectedNamespace);
                      setIngressFormVisible(true);
                    }}
                    disabled={!selectedCluster || !selectedNamespace}
                  >
                    创建 Ingress
                  </Button>
                </div>
              </div>
              <Table
                columns={ingressColumns}
                dataSource={getFilteredIngresses()}
                rowKey="name"
                pagination={{ 
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total) => `共 ${total} 条记录`
                }}
                loading={loading && activeTab === 'ingresses'}
                scroll={{ x: 1300 }}
                size="middle"
                bordered
                summary={() => {
                  const total = getFilteredIngresses().length;
                  return (
                    <Table.Summary fixed>
                      <Table.Summary.Row>
                        <Table.Summary.Cell index={0} colSpan={7}>
                          <div style={{ textAlign: 'right', fontWeight: 'bold' }}>
                            总计: {total} 个 Ingress
                          </div>
                        </Table.Summary.Cell>
                      </Table.Summary.Row>
                    </Table.Summary>
                  );
                }}
              />
            </TabPane>
          </Tabs>
        </Card>

        <Modal
          title={modalTitle}
          visible={modalVisible}
          onCancel={() => setModalVisible(false)}
          width={800}
          footer={
            modalType === 'view'
              ? [
                  <Button key="format" icon={<FormatPainterOutlined />} onClick={formatCurrentContent}>
                    格式化
                  </Button>,
                  <Button key="toggle" onClick={toggleFormat}>
                    切换为{useYamlFormat ? 'JSON' : 'YAML'}
                  </Button>,
                  <Button key="close" onClick={() => setModalVisible(false)}>
                    关闭
                  </Button>,
                ]
              : [
                  <Button key="format" icon={<FormatPainterOutlined />} onClick={formatCurrentContent}>
                    格式化
                  </Button>,
                  <Button key="toggle" onClick={toggleFormat}>
                    切换为{useYamlFormat ? 'JSON' : 'YAML'}
                  </Button>,
                  <Button key="cancel" onClick={() => setModalVisible(false)}>
                    取消
                  </Button>,
                  <Button key="submit" type="primary" onClick={saveResource}>
                    保存
                  </Button>,
                ]
          }
        >
          <div style={{ height: 500 }}>
            {modalType === 'view' ? (
              <PrettyYaml content={yamlContent} isYaml={useYamlFormat} />
            ) : (
              <div>
                <div style={{ marginBottom: '10px' }}>
                  <Switch
                    checked={useYamlFormat}
                    onChange={setUseYamlFormat}
                    checkedChildren="YAML"
                    unCheckedChildren="JSON"
                  />
                  <span style={{ marginLeft: '10px' }}>
                    当前格式: {useYamlFormat ? 'YAML' : 'JSON'}
                  </span>
                </div>
                <TextArea
                  value={yamlContent}
                  onChange={(e) => setYamlContent(e.target.value)}
                  style={{ 
                    height: '450px',
                    fontFamily: 'monospace',
                    fontSize: '14px',
                    lineHeight: '1.5'
                  }}
                  placeholder={`请输入 ${resourceType} 的 ${useYamlFormat ? 'YAML' : 'JSON'} 配置`}
                />
              </div>
            )}
          </div>
        </Modal>

        {/* 终端弹窗 */}
        <Modal
          title={`Pod 终端 - ${terminalPod.pod}`}
          visible={terminalVisible}
          onCancel={closeTerminal}
          footer={null}
          width={800}
          bodyStyle={{ padding: 0 }}
          destroyOnClose
        >
          {terminalVisible && (
            <PodTerminal
              cluster={terminalPod.cluster}
              namespace={terminalPod.namespace}
              pod={terminalPod.pod}
            />
          )}
        </Modal>

        {/* 在 return 语句中添加 Ingress 表单 Modal */}
        <Modal
          title="创建 Ingress"
          visible={ingressFormVisible}
          onCancel={() => setIngressFormVisible(false)}
          destroyOnClose={false}
          width={800}
          footer={[
            <Button key="cancel" onClick={() => setIngressFormVisible(false)}>
              取消
            </Button>,
            <Button key="submit" type="primary" onClick={handleIngressFormSubmit}>
              创建
            </Button>,
          ]}
        >
          <Spin spinning={loading}>
            <Form layout="vertical">
              <Form.Item label="Ingress 名称" required>
                <Input
                  value={ingressFormData.name}
                  onChange={(e) => setIngressFormData({ ...ingressFormData, name: e.target.value })}
                  placeholder="请输入 Ingress 名称"
                />
              </Form.Item>
              
              <Form.Item label="主机名" required>
                <Input
                  value={ingressFormData.host}
                  onChange={(e) => setIngressFormData({ ...ingressFormData, host: e.target.value })}
                  placeholder="请输入主机名，例如: example.com"
                />
              </Form.Item>
              
              <Divider orientation="left">路径配置</Divider>
              
              {ingressFormData.paths.map((path: any, index: number) => (
                <div key={index} style={{ marginBottom: 16, padding: 16, border: '1px dashed #d9d9d9', borderRadius: 4 }}>
                  <Row gutter={16}>
                    <Col span={8}>
                      <Form.Item label="路径" required>
                        <Input
                          value={path.path}
                          onChange={(e) => updateIngressPath(index, 'path', e.target.value)}
                          placeholder="例如: / 或 /api"
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="服务" required>
                        <Select
                          value={path.serviceName}
                          onChange={(value) => updateIngressPath(index, 'serviceName', value)}
                          placeholder="选择服务"
                          style={{ width: '100%' }}
                          showSearch
                          filterOption={(input, option) => 
                            option?.children 
                              ? (option.children as unknown as string).toLowerCase().includes(input.toLowerCase())
                              : false
                          }
                          optionFilterProp="children"
                        >
                          {services.map((service: any) => (
                            <Option key={service.name} value={service.name}>
                              {service.name}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item label="端口" required>
                        <Select
                          value={path.servicePort}
                          onChange={(value) => updateIngressPath(index, 'servicePort', value)}
                          placeholder="选择端口"
                          style={{ width: '100%' }}
                          disabled={!path.serviceName}
                        >
                          {path.serviceName && getServicePorts(path.serviceName).map((port: any) => (
                            <Option key={port.value} value={port.value}>
                              {port.label}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={2} style={{ display: 'flex', alignItems: 'flex-end', justifyContent: 'center' }}>
                      {ingressFormData.paths.length > 1 && (
                        <Button
                          type="text"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => removeIngressPath(index)}
                          style={{ marginBottom: 8 }}
                        />
                      )}
                    </Col>
                  </Row>
                </div>
              ))}
              
              <Button type="dashed" onClick={addIngressPath} block icon={<PlusOutlined />}>
                添加路径
              </Button>
              
              <Divider orientation="left">TLS 配置</Divider>
              
              <Form.Item>
                <Checkbox
                  checked={ingressFormData.tls.enabled}
                  onChange={(e) => setIngressFormData({
                    ...ingressFormData,
                    tls: {
                      ...ingressFormData.tls,
                      enabled: e.target.checked
                    }
                  })}
                >
                  启用 TLS
                </Checkbox>
              </Form.Item>
              
              {ingressFormData.tls.enabled && (
                <Form.Item label="TLS Secret 名称" required={ingressFormData.tls.enabled}>
                  <Select
                    value={ingressFormData.tls.secretName}
                    onChange={(value) => setIngressFormData({
                      ...ingressFormData,
                      tls: {
                        ...ingressFormData.tls,
                        secretName: value
                      }
                    })}
                    placeholder="请选择 Secret 名称"
                  >
                    <Option value="blacklake.cn">blacklake.cn</Option>
                    <Option value="blacklake.tech">blacklake.tech</Option>
                  </Select>
                  <div style={{ marginTop: '8px', color: '#888' }}>
                    Secret 必须包含 tls.crt 和 tls.key 字段，用于存储证书和私钥
                  </div>
                </Form.Item>
              )}
            </Form>
          </Spin>
        </Modal>
      </Spin>
    </PageContainer>
  );
};

export default ClusterManagement; 