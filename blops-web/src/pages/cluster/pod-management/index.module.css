/* Pod管理页面现代化样式 */

.podManagement {
  background: #f5f5f5;
  min-height: 100vh;
}

.controlPanel {
  background: linear-gradient(135deg, #f8f9fa 0%, #fff 50%, #f0f2f5 100%);
  border: 1px solid #e8e8e8;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06),
              0 1px 4px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.controlPanel:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08),
              0 2px 6px rgba(0, 0, 0, 0.06);
  border-color: #d9d9d9;
}

.controlPanel .ant-card {
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
}

/* 控制面板文本颜色优化 */
.controlPanel .ant-typography {
  color: #262626 !important;
}

.controlPanel .ant-select-selection-item {
  color: #262626 !important;
}

.controlPanel .ant-input {
  color: #262626 !important;
}

.statisticsCard {
  border-radius: 8px;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.statisticsCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.tableCard {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.tableCard .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
  border-bottom: 2px solid #f0f0f0;
  padding: 12px 16px;
}

.tableCard .ant-table-tbody > tr > td {
  padding: 12px 16px;
  border-bottom: 1px solid #f5f5f5;
}

.tableCard .ant-table-tbody > tr:hover > td {
  background-color: #f8f9ff;
}

.podStatusBadge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.podStatusBadge:hover {
  transform: scale(1.05);
}

.actionButton {
  border: none;
  box-shadow: none;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
}

.actionButton:hover {
  transform: scale(1.1);
}

.podAvatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: bold;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.readyBadge {
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 12px;
}

.restartIndicator {
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 12px;
}

.ageBadge {
  display: inline-flex;
  align-items: center;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.ageBadge:hover {
  transform: scale(1.05);
}

.ageNew {
  background-color: #fffbe6;
  border: 1px solid #ffe58f;
  color: #faad14;
}

.ageNormal {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #52c41a;
}

.ageLong {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  color: #1890ff;
}

/* 控制面板选择器样式优化 */

.controlPanel .ant-select {
  height: 40px !important;
}

.controlPanel .ant-select-selector {
  border-radius: 6px !important;
  height: 40px !important;
  padding: 8px 12px !important;
  border: 1px solid #d9d9d9 !important;
  transition: all 0.2s ease !important;
}

.controlPanel .ant-select-selector:hover {
  border-color: #40a9ff !important;
}

.controlPanel .ant-select-focused .ant-select-selector {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.controlPanel .ant-input-search {
  height: 40px !important;
}

.controlPanel .ant-input-search .ant-input {
  height: 40px !important;
  border-radius: 6px !important;
  padding: 8px 12px !important;
  border: 1px solid #d9d9d9 !important;
  transition: all 0.2s ease !important;
}

.controlPanel .ant-input-search .ant-input:hover {
  border-color: #40a9ff !important;
}

.controlPanel .ant-input-search .ant-input:focus {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* 刷新按钮专用样式 */
.refreshButton {
  width: 100% !important;
  height: 40px !important;
  border-radius: 10px !important;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 50%, #69c0ff 100%) !important;
  border: none !important;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.25),
              0 2px 4px rgba(24, 144, 255, 0.15) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  letter-spacing: 0.5px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
  color: #fff !important;
}

/* 添加光泽效果 */
.refreshButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.refreshButton:hover::before {
  left: 100%;
}

.refreshButton:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 50%, #096dd9 100%) !important;
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.35),
              0 3px 6px rgba(24, 144, 255, 0.25) !important;
  transform: translateY(-2px) scale(1.02) !important;
}

.refreshButton:active {
  transform: translateY(-1px) scale(0.98) !important;
  box-shadow: 0 3px 8px rgba(24, 144, 255, 0.3) !important;
}

.refreshButton:focus {
  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.3),
              0 4px 12px rgba(24, 144, 255, 0.25) !important;
  outline: none !important;
}

.refreshButton .anticon {
  margin-right: 6px !important;
  font-size: 15px !important;
  transition: transform 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55) !important;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1)) !important;
}

.refreshButton:hover .anticon {
  transform: rotate(360deg) scale(1.1) !important;
}

/* 加载状态样式 */
.refreshButton.ant-btn-loading {
  background: linear-gradient(135deg, #91d5ff 0%, #69c0ff 50%, #40a9ff 100%) !important;
  cursor: not-allowed !important;
}

.refreshButton.ant-btn-loading .anticon {
  animation: refreshSpin 1.2s linear infinite !important;
  transform: none !important;
}

.refreshButton.ant-btn-loading:hover .anticon {
  transform: none !important;
}

@keyframes refreshSpin {
  0% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(1.05);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

/* 禁用状态 */
.refreshButton:disabled {
  background: linear-gradient(135deg, #d9d9d9 0%, #bfbfbf 100%) !important;
  box-shadow: none !important;
  transform: none !important;
  cursor: not-allowed !important;
}

.refreshButton:disabled .anticon {
  color: #8c8c8c !important;
  transform: none !important;
}

.controlPanel .ant-btn:not(.refreshButton) {
  height: 40px !important;
  border-radius: 6px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .controlPanel .ant-select {
    width: 140px !important;
  }
}

@media (max-width: 768px) {
  /* 移动端改为垂直布局 */
  .controlPanel > div {
    flex-direction: column !important;
    gap: 12px !important;
  }

  .controlPanel > div > div {
    min-width: 100% !important;
    width: 100% !important;
  }

  .controlPanel .ant-select {
    width: 100% !important;
    height: 36px !important;
  }

  .controlPanel .ant-select-selector {
    height: 36px !important;
    padding: 6px 10px !important;
  }

  .controlPanel .ant-input-search {
    height: 36px !important;
  }

  .controlPanel .ant-input-search .ant-input {
    height: 36px !important;
    padding: 6px 10px !important;
  }

  .refreshButton {
    width: 100% !important;
    height: 36px !important;
    font-size: 13px !important;
    border-radius: 8px !important;
    letter-spacing: 0.3px !important;
  }

  .refreshButton .anticon {
    font-size: 13px !important;
    margin-right: 4px !important;
  }

  .refreshButton:hover {
    transform: translateY(-1px) scale(1.01) !important;
  }
}

.searchInput {
  border-radius: 6px;
  transition: all 0.2s ease;
}

.searchInput:focus {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}



/* 响应式设计 */
@media (max-width: 768px) {
  .controlPanel {
    padding: 12px;
  }
  
  .statisticsCard {
    margin-bottom: 8px;
  }
  
  .tableCard .ant-table {
    font-size: 12px;
  }
  
  .actionButton {
    width: 24px;
    height: 24px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fadeIn {
  animation: fadeIn 0.3s ease-out;
}

/* 加载状态 */
.loadingOverlay {
  position: relative;
}

.loadingOverlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
  z-index: 10;
  border-radius: 12px;
}

/* 自定义滚动条 */
.tableCard .ant-table-body::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.tableCard .ant-table-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.tableCard .ant-table-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.tableCard .ant-table-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
