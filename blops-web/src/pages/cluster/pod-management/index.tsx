import React, { useState, useEffect, useCallback } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Table, Button, message, Select, Input, Modal, Space, Spin, Card, Tooltip, Row, Col, Statistic, Badge, Avatar, Typography, Progress } from 'antd';
import clusterService from '@/services/cluster'; // 请确保 clusterService 中有 getPodList 和 list_namespaces 方法
import { debounce } from 'lodash'; // 导入 debounce 函数
import {
  DownloadOutlined,
  SearchOutlined,
  CodeOutlined,
  BarChartOutlined,
  ThunderboltOutlined,
  MonitorOutlined,
  FileTextOutlined,
  ReloadOutlined,
  ClusterOutlined,
  AppstoreOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  SyncOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  FireOutlined
} from '@ant-design/icons';
import request from '@/utils/request';
import styles from './index.module.css';

const { Option } = Select;
const { Search } = Input; // 使用 Input.Search
const { Title, Text } = Typography;

// 固定的集群选项
const fixedClusterOptions = [
  { name: 'ali-test', displayName: 'ali-test' },
  { name: 'ali-prod', displayName: 'ali-prod' },
  { name: 'ali-guotai', displayName: 'ali-guotai' },
  { name: 'hwyx-prod', displayName: 'hwyx-prod' },
  { name: 'ali-v3', displayName: 'ali-v3' },
];

// 格式化年龄的函数 - 处理Go Duration格式
const formatAge = (age: string) => {
    if (!age) return '';

    // Go Duration格式: "2h30m45s", "1h30m", "45m30s", "30s" 等
    // 解析并格式化为更简洁的显示
    const parseGoDuration = (duration: string) => {
        const dayMatch = duration.match(/(\d+)h(\d+)m(\d+)s/);
        const hourMinMatch = duration.match(/(\d+)h(\d+)m/);
        const hourMatch = duration.match(/^(\d+)h$/);
        const minSecMatch = duration.match(/(\d+)m(\d+)s/);
        const minMatch = duration.match(/^(\d+)m$/);
        const secMatch = duration.match(/^(\d+)s$/);

        if (dayMatch) {
            const hours = parseInt(dayMatch[1]);
            const minutes = parseInt(dayMatch[2]);
            if (hours >= 24) {
                const days = Math.floor(hours / 24);
                const remainingHours = hours % 24;
                return remainingHours > 0 ? `${days}d${remainingHours}h` : `${days}d`;
            }
            return minutes > 0 ? `${hours}h${minutes}m` : `${hours}h`;
        }

        if (hourMinMatch) {
            const hours = parseInt(hourMinMatch[1]);
            const minutes = parseInt(hourMinMatch[2]);
            if (hours >= 24) {
                const days = Math.floor(hours / 24);
                const remainingHours = hours % 24;
                return remainingHours > 0 ? `${days}d${remainingHours}h` : `${days}d`;
            }
            return minutes > 0 ? `${hours}h${minutes}m` : `${hours}h`;
        }

        if (hourMatch) {
            const hours = parseInt(hourMatch[1]);
            if (hours >= 24) {
                const days = Math.floor(hours / 24);
                const remainingHours = hours % 24;
                return remainingHours > 0 ? `${days}d${remainingHours}h` : `${days}d`;
            }
            return `${hours}h`;
        }

        if (minSecMatch) {
            const minutes = parseInt(minSecMatch[1]);
            return `${minutes}m`;
        }

        if (minMatch) {
            return `${minMatch[1]}m`;
        }

        if (secMatch) {
            return `${secMatch[1]}s`;
        }

        return duration; // 返回原始值
    };

    return parseGoDuration(age);
};

// 添加下载功能的辅助函数
const downloadThreadDump = (content: string, podName: string, title: string = '线程详情') => {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `${title}-${podName}-${timestamp}.txt`;
  const blob = new Blob([content], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

// Create a component for thread analysis
const ThreadAnalysisModal: React.FC<{
  output: string;
  podName: string;
  title: string;
  onClose: () => void;
}> = ({ output, podName, title, onClose }) => {
  const [analysisResult, setAnalysisResult] = useState<string>('');
  const [analyzing, setAnalyzing] = useState<boolean>(false);

  const analyzeThreadStack = async () => {
    if (!output) {
      message.warning('没有内容可以分析');
      return;
    }

    try {
      setAnalyzing(true);
      const response = await request('/api/ai/analyze', {
        method: 'POST',
        data: {
          content: output,
          type: 'thread_dump', // 使用预设的线程分析提示词
        },
        timeout: 120000, // 2 minute timeout
      });
      
      if (response?.code === 200 && response.data) {
        // 后端已经处理了 markdown 转 HTML，可以直接使用
        setAnalysisResult(response.data);
      } else {
        message.error(response?.message || '分析失败');
        setAnalysisResult('分析失败，请稍后重试');
      }
    } catch (error) {
      console.error('线程分析失败:', error);
      message.error('分析失败: ' + (error instanceof Error ? error.message : String(error)));
      setAnalysisResult('分析失败，请稍后重试');
    } finally {
      setAnalyzing(false);
    }
  };

  // 检查是否包含火焰图链接
  const flameGraphMatch = output.match(/🔥 打开火焰图: (http:\/\/[^\s]+)/);
  const hasFlameGraphLink = flameGraphMatch && flameGraphMatch[1];

  // 分离输出内容和火焰图链接
  const outputContent = hasFlameGraphLink ? output.replace(/\n\n🔥 打开火焰图: http:\/\/[^\s]+/, '') : output;

  return (
    <div>
      <pre style={{ maxHeight: '400px', overflow: 'auto', marginBottom: '16px' }}>
        {outputContent}
      </pre>

      {/* 火焰图链接区域 */}
      {hasFlameGraphLink && (
        <Card
          size="small"
          style={{
            marginBottom: '16px',
            background: 'linear-gradient(135deg, #fff2e8 0%, #fff7e6 100%)',
            border: '1px solid #ffb366',
            borderRadius: '8px'
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px', flexWrap: 'wrap' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <FireOutlined style={{ color: '#ff4d4f', fontSize: '18px' }} />
              <span style={{ color: '#262626', fontWeight: 600, fontSize: '14px' }}>
                🎯 火焰图已生成
              </span>
            </div>
            <div style={{ display: 'flex', gap: '8px', flex: 1, justifyContent: 'flex-end' }}>
              <Button
                type="primary"
                size="small"
                icon={<FireOutlined />}
                onClick={() => window.open(hasFlameGraphLink, '_blank')}
                style={{
                  background: 'linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%)',
                  border: 'none',
                  boxShadow: '0 2px 6px rgba(255, 77, 79, 0.3)',
                  borderRadius: '6px',
                  fontWeight: 500
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-1px)';
                  e.currentTarget.style.boxShadow = '0 4px 8px rgba(255, 77, 79, 0.4)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '0 2px 6px rgba(255, 77, 79, 0.3)';
                }}
              >
                打开火焰图
              </Button>
              <Button
                size="small"
                onClick={() => {
                  navigator.clipboard.writeText(hasFlameGraphLink)
                    .then(() => message.success('🔗 火焰图链接已复制到剪贴板'))
                    .catch(() => message.error('复制失败'));
                }}
                style={{
                  borderColor: '#ff7875',
                  color: '#ff4d4f',
                  borderRadius: '6px'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#fff1f0';
                  e.currentTarget.style.borderColor = '#ff4d4f';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                  e.currentTarget.style.borderColor = '#ff7875';
                }}
              >
                复制链接
              </Button>
            </div>
          </div>
          <div style={{
            marginTop: '8px',
            padding: '6px 8px',
            backgroundColor: 'rgba(255, 255, 255, 0.7)',
            borderRadius: '4px',
            fontSize: '12px',
            color: '#666',
            fontFamily: 'monospace',
            wordBreak: 'break-all'
          }}>
            {hasFlameGraphLink}
          </div>
        </Card>
      )}
      
      <div style={{ marginTop: 16, marginBottom: 16, display: 'flex', justifyContent: 'flex-end' }}>
        <Button 
          style={{ marginRight: 8 }}
          type="primary"
          onClick={analyzeThreadStack}
          loading={analyzing}
          icon={<SearchOutlined />}
        >
          AI分析
        </Button>

        <Button 
          style={{ marginRight: 8 }}
          onClick={() => {
            navigator.clipboard.writeText(output)
              .then(() => message.success('已复制到剪贴板'))
              .catch(() => message.error('复制失败'));
          }}
        >
          复制
        </Button>
        <Button onClick={onClose} style={{ marginRight: 8 }}>
          关闭
        </Button>
        <Button 
          type="primary" 
          icon={<DownloadOutlined />}
          onClick={() => downloadThreadDump(output, podName, title)}
        >
          下载
        </Button>
      </div>
      
      {analyzing && (
        <div style={{ textAlign: 'center', margin: '16px 0' }}>
          <Spin tip="AI正在分析中..." />
        </div>
      )}
      
      {analysisResult && !analyzing && (
        <Card title="AI分析结果" style={{ marginTop: 16 }}>
          <div style={{ 
            padding: '12px', 
            backgroundColor: '#f5f5f5', 
            borderRadius: '4px',
            whiteSpace: 'pre-line'
          }}>
            {/* 使用 dangerouslySetInnerHTML 因为后端返回的是 HTML */}
            <div dangerouslySetInnerHTML={{ __html: analysisResult }} />
          </div>
        </Card>
      )}
    </div>
  );
};

// 操作按钮样式
const actionButtonStyle = {
  border: 'none',
  boxShadow: 'none',
  borderRadius: '6px',
  transition: 'all 0.2s ease',
};

// Pod状态渲染函数
const renderPodStatus = (status: string) => {
  const statusConfig = {
    'Running': {
      color: '#52c41a',
      icon: <CheckCircleOutlined />,
      bgColor: '#f6ffed',
      borderColor: '#b7eb8f'
    },
    'Pending': {
      color: '#faad14',
      icon: <SyncOutlined spin />,
      bgColor: '#fffbe6',
      borderColor: '#ffe58f'
    },
    'Failed': {
      color: '#ff4d4f',
      icon: <CloseCircleOutlined />,
      bgColor: '#fff2f0',
      borderColor: '#ffccc7'
    },
    'Succeeded': {
      color: '#52c41a',
      icon: <CheckCircleOutlined />,
      bgColor: '#f6ffed',
      borderColor: '#b7eb8f'
    },
    'Unknown': {
      color: '#8c8c8c',
      icon: <ExclamationCircleOutlined />,
      bgColor: '#f5f5f5',
      borderColor: '#d9d9d9'
    }
  };

  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig['Unknown'];

  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      padding: '4px 8px',
      borderRadius: '6px',
      backgroundColor: config.bgColor,
      border: `1px solid ${config.borderColor}`,
      fontSize: '12px',
      fontWeight: 500
    }}>
      <span style={{ color: config.color, marginRight: '4px', fontSize: '14px' }}>
        {config.icon}
      </span>
      <span style={{ color: config.color }}>{status}</span>
    </div>
  );
};

// Pod统计信息组件
const PodStatistics: React.FC<{ podList: any[] }> = ({ podList }) => {
  const stats = podList.reduce((acc, pod) => {
    acc.total++;
    acc[pod.status] = (acc[pod.status] || 0) + 1;
    return acc;
  }, { total: 0, Running: 0, Pending: 0, Failed: 0, Succeeded: 0, Unknown: 0 });

  return (
    <Row gutter={16} style={{ marginBottom: 16 }}>
      <Col span={4}>
        <Card size="small" className={styles.statisticsCard} style={{ textAlign: 'center' }}>
          <Statistic
            title={<Text style={{ fontSize: '12px', color: '#666' }}>总计</Text>}
            value={stats.total}
            prefix={<AppstoreOutlined style={{ color: '#1890ff' }} />}
            valueStyle={{ fontSize: '20px', fontWeight: 'bold', color: '#1890ff' }}
          />
        </Card>
      </Col>
      <Col span={4}>
        <Card size="small" className={styles.statisticsCard} style={{ textAlign: 'center' }}>
          <Statistic
            title={<Text style={{ fontSize: '12px', color: '#666' }}>运行中</Text>}
            value={stats.Running}
            prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
            valueStyle={{ fontSize: '20px', fontWeight: 'bold', color: '#52c41a' }}
          />
        </Card>
      </Col>
      <Col span={4}>
        <Card size="small" className={styles.statisticsCard} style={{ textAlign: 'center' }}>
          <Statistic
            title={<Text style={{ fontSize: '12px', color: '#666' }}>等待中</Text>}
            value={stats.Pending}
            prefix={<SyncOutlined style={{ color: '#faad14' }} />}
            valueStyle={{ fontSize: '20px', fontWeight: 'bold', color: '#faad14' }}
          />
        </Card>
      </Col>
      <Col span={4}>
        <Card size="small" className={styles.statisticsCard} style={{ textAlign: 'center' }}>
          <Statistic
            title={<Text style={{ fontSize: '12px', color: '#666' }}>失败</Text>}
            value={stats.Failed}
            prefix={<CloseCircleOutlined style={{ color: '#ff4d4f' }} />}
            valueStyle={{ fontSize: '20px', fontWeight: 'bold', color: '#ff4d4f' }}
          />
        </Card>
      </Col>
      <Col span={4}>
        <Card size="small" className={styles.statisticsCard} style={{ textAlign: 'center' }}>
          <Statistic
            title={<Text style={{ fontSize: '12px', color: '#666' }}>成功</Text>}
            value={stats.Succeeded}
            prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
            valueStyle={{ fontSize: '20px', fontWeight: 'bold', color: '#52c41a' }}
          />
        </Card>
      </Col>
      <Col span={4}>
        <Card size="small" className={styles.statisticsCard} style={{ textAlign: 'center' }}>
          <Statistic
            title={<Text style={{ fontSize: '12px', color: '#666' }}>未知</Text>}
            value={stats.Unknown}
            prefix={<ExclamationCircleOutlined style={{ color: '#8c8c8c' }} />}
            valueStyle={{ fontSize: '20px', fontWeight: 'bold', color: '#8c8c8c' }}
          />
        </Card>
      </Col>
    </Row>
  );
};

const PodManagement: React.FC = () => {
  // 本地存储键名
  const STORAGE_KEYS = {
    CLUSTER: 'pod-management-selected-cluster',
    NAMESPACE: 'pod-management-selected-namespace',
    SEARCH_TEXT: 'pod-management-search-text'
  };

  // 从本地存储获取上次的选择
  const getStoredValue = (key: string, defaultValue: string = '') => {
    try {
      return localStorage.getItem(key) || defaultValue;
    } catch (error) {
      console.warn('Failed to read from localStorage:', error);
      return defaultValue;
    }
  };

  // 保存到本地存储
  const setStoredValue = (key: string, value: string) => {
    try {
      localStorage.setItem(key, value);
    } catch (error) {
      console.warn('Failed to write to localStorage:', error);
    }
  };

  const [podList, setPodList] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [clusterList, setClusterList] = useState<any[]>(fixedClusterOptions);
  const [selectedCluster, setSelectedCluster] = useState<string>(() =>
    getStoredValue(STORAGE_KEYS.CLUSTER, 'ali-test')
  );
  const [namespaceList, setNamespaceList] = useState<any[]>([]);
  const [selectedNamespace, setSelectedNamespace] = useState<string>(() =>
    getStoredValue(STORAGE_KEYS.NAMESPACE)
  );
  const [searchText, setSearchText] = useState<string>(() =>
    getStoredValue(STORAGE_KEYS.SEARCH_TEXT)
  );
  const [isInitialLoad, setIsInitialLoad] = useState<boolean>(true);

    // 获取 Pod 列表并根据搜索条件过滤
    const fetchPodList = useCallback(async (searchValue?: string) => { // 使用 useCallback
        if (!selectedCluster || !selectedNamespace) return;

        setLoading(true);
        try {
            const response = await clusterService.getPodList({
                cluster: selectedCluster,
                namespace: selectedNamespace
            });
            if (response && response.data) {
                let filteredData = response.data;
                if (searchValue) {
                    filteredData = response.data.filter((pod: any) =>
                        pod.name.toLowerCase().includes(searchValue.toLowerCase())
                    );
                }
                setPodList(filteredData);
            } else {
                setPodList([]);
            }
        } catch (error) {
            console.error('Error fetching pod list:', error);
            message.error('获取 Pod 列表失败');
        } finally {
            setLoading(false);
        }
    }, [selectedCluster, selectedNamespace]);

    const handleClusterChange = (value: string) => {
        setSelectedCluster(value);
        setStoredValue(STORAGE_KEYS.CLUSTER, value);
        setSelectedNamespace('');
        setStoredValue(STORAGE_KEYS.NAMESPACE, '');
        setPodList([]);
        setSearchText(''); // 清空搜索框
        setStoredValue(STORAGE_KEYS.SEARCH_TEXT, '');
    };

    const handleNamespaceChange = (value: string) => {
        setSelectedNamespace(value);
        setStoredValue(STORAGE_KEYS.NAMESPACE, value);
        setPodList([]);
        setSearchText(''); // 清空搜索框
        setStoredValue(STORAGE_KEYS.SEARCH_TEXT, '');
    };

  // 防抖处理搜索
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      fetchPodList(value);
    }, 300), // 300ms 防抖
    [fetchPodList]
  );

    // 搜索处理函数
    const handleSearch = (value: string) => {
        setSearchText(value);
        setStoredValue(STORAGE_KEYS.SEARCH_TEXT, value);
        debouncedSearch(value);
    };

  // 当集群变更时，先拉取该集群下的命名空间列表
  const fetchNamespaces = async (clusterName: string) => {
    if (!clusterName) return;
    try {
      const res = await clusterService.list_namespaces({ cluster: clusterName });
      if (res.result === 'SUCCESS') {
        const nsList = res.data || [];
        setNamespaceList(nsList);

        // 如果是初始加载，检查保存的命名空间是否存在
        if (isInitialLoad) {
          const savedNamespace = getStoredValue(STORAGE_KEYS.NAMESPACE);
          console.log('初始加载 - 保存的命名空间:', savedNamespace);
          console.log('初始加载 - 可用命名空间列表:', nsList.map((ns: any) => ns.name));

          if (savedNamespace && nsList.some((ns: any) => ns.name === savedNamespace)) {
            // 保存的命名空间存在，显式设置以确保UI正确显示
            console.log('恢复保存的命名空间:', savedNamespace);
            setSelectedNamespace(savedNamespace);
          } else if (savedNamespace && !nsList.some((ns: any) => ns.name === savedNamespace)) {
            // 保存的命名空间不存在，清空选择
            setSelectedNamespace('');
            setStoredValue(STORAGE_KEYS.NAMESPACE, '');
          } else if (!savedNamespace) {
            // 没有保存的命名空间，设置默认值
            if (clusterName === 'ali-test') {
              const barrenDevExists = nsList.some((ns: any) => ns.name === 'barren-dev');
              if (barrenDevExists) {
                setSelectedNamespace('barren-dev');
                setStoredValue(STORAGE_KEYS.NAMESPACE, 'barren-dev');
              } else if (nsList.length > 0) {
                setSelectedNamespace(nsList[0].name);
                setStoredValue(STORAGE_KEYS.NAMESPACE, nsList[0].name);
              }
            } else if (nsList.length > 0) {
              setSelectedNamespace(nsList[0].name);
              setStoredValue(STORAGE_KEYS.NAMESPACE, nsList[0].name);
            }
          }
          setIsInitialLoad(false);
        } else {
          // 用户手动切换集群，设置默认命名空间
          if (clusterName === 'ali-test') {
            const barrenDevExists = nsList.some((ns: any) => ns.name === 'barren-dev');
            if (barrenDevExists) {
              setSelectedNamespace('barren-dev');
              setStoredValue(STORAGE_KEYS.NAMESPACE, 'barren-dev');
            } else if (nsList.length > 0) {
              setSelectedNamespace(nsList[0].name);
              setStoredValue(STORAGE_KEYS.NAMESPACE, nsList[0].name);
            } else {
              setSelectedNamespace('');
              setStoredValue(STORAGE_KEYS.NAMESPACE, '');
            }
          } else if (nsList.length > 0) {
            setSelectedNamespace(nsList[0].name);
            setStoredValue(STORAGE_KEYS.NAMESPACE, nsList[0].name);
          } else {
            setSelectedNamespace('');
            setStoredValue(STORAGE_KEYS.NAMESPACE, '');
          }
        }
      } else {
        message.error(res.message || '获取命名空间列表失败');
      }
    } catch (error) {
      console.error('获取命名空间列表失败:', error);
      message.error('获取命名空间列表失败');
    }
  };

  // 监听集群变更，以获取命名空间列表
  useEffect(() => {
    if (selectedCluster) {
      fetchNamespaces(selectedCluster);
    }
  }, [selectedCluster]);

  // 当集群和命名空间均选中时，拉取 Pod 列表
  useEffect(() => {
    if (selectedCluster && selectedNamespace) {
      fetchPodList();
    }
  }, [selectedCluster, selectedNamespace, fetchPodList]);

  // 页面初始化时，如果有保存的搜索文本，执行搜索
  useEffect(() => {
    if (selectedCluster && selectedNamespace && searchText) {
      debouncedSearch(searchText);
    }
  }, [selectedCluster, selectedNamespace, searchText, debouncedSearch]);

    const columns = [
        {
            title: 'Pod名称',
            dataIndex: 'name',
            key: 'name',
            sorter: (a: any, b: any) => a.name.localeCompare(b.name),
            render: (name: string) => (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                    <Avatar
                        size="small"
                        className={styles.podAvatar}
                        style={{ marginRight: 8 }}
                    >
                        {name.charAt(0).toUpperCase()}
                    </Avatar>
                    <div>
                        <Text strong style={{ fontSize: '13px' }}>{name}</Text>
                    </div>
                </div>
            ),
        },
        {
            title: '命名空间',
            dataIndex: 'namespace',
            key: 'namespace',
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            render: (status: string) => renderPodStatus(status),
            filters: [
                { text: 'Running', value: 'Running' },
                { text: 'Pending', value: 'Pending' },
                { text: 'Failed', value: 'Failed' },
                { text: 'Succeeded', value: 'Succeeded' },
                { text: 'Unknown', value: 'Unknown' },
            ],
            onFilter: (value: any, record: any) => record.status === value,
        },
        {
            title: 'IP',
            dataIndex: 'ip',
            key: 'ip',
        },
        {
            title: 'Node',
            dataIndex: 'node',
            key: 'node',
        },
        {
            title: 'Ready',
            dataIndex: 'ready',
            key: 'ready',
            render: (ready: string) => {
                const [readyCount, totalCount] = ready.split('/').map(Number);
                const isReady = readyCount === totalCount;
                return (
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                        <Badge
                            status={isReady ? 'success' : 'warning'}
                            style={{ marginRight: 4 }}
                        />
                        <Text style={{
                            color: isReady ? '#52c41a' : '#faad14',
                            fontWeight: 500,
                            fontSize: '12px'
                        }}>
                            {ready}
                        </Text>
                    </div>
                );
            },
        },
        {
            title: 'Restarts',
            dataIndex: 'restarts',
            key: 'restarts',
            sorter: (a: any, b: any) => a.restarts - b.restarts,
            render: (restarts: number) => {
                let color = '#52c41a';
                let icon = <CheckCircleOutlined />;

                if (restarts > 5) {
                    color = '#ff4d4f';
                    icon = <WarningOutlined />;
                } else if (restarts > 0) {
                    color = '#faad14';
                    icon = <InfoCircleOutlined />;
                }

                return (
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                        <span style={{ color, marginRight: 4, fontSize: '12px' }}>
                            {icon}
                        </span>
                        <Text style={{
                            color,
                            fontWeight: restarts > 0 ? 'bold' : 'normal',
                            fontSize: '12px'
                        }}>
                            {restarts}
                        </Text>
                    </div>
                );
            },
        },
        {
            title: '运行时间',
            dataIndex: 'age',
            key: 'age',
            sorter: (a: any, b: any) => {
                // 解析Go Duration格式并转换为秒数进行比较
                const parseGoDurationToSeconds = (duration: string) => {
                    if (!duration) return 0;

                    let totalSeconds = 0;

                    // 匹配小时
                    const hourMatch = duration.match(/(\d+)h/);
                    if (hourMatch) {
                        totalSeconds += parseInt(hourMatch[1]) * 3600;
                    }

                    // 匹配分钟
                    const minMatch = duration.match(/(\d+)m/);
                    if (minMatch) {
                        totalSeconds += parseInt(minMatch[1]) * 60;
                    }

                    // 匹配秒
                    const secMatch = duration.match(/(\d+)s/);
                    if (secMatch) {
                        totalSeconds += parseInt(secMatch[1]);
                    }

                    return totalSeconds;
                };

                return parseGoDurationToSeconds(a.age) - parseGoDurationToSeconds(b.age);
            },
            render: (age: string) => {
                if (!age) return <Text style={{ color: '#8c8c8c', fontSize: '12px' }}>-</Text>;

                // 根据运行时间长短确定样式类
                let ageClass = styles.ageNormal; // 默认正常运行

                // 解析Go Duration格式判断样式
                const parseAgeForStyle = (duration: string) => {
                    let totalMinutes = 0;

                    // 匹配小时并转换为分钟
                    const hourMatch = duration.match(/(\d+)h/);
                    if (hourMatch) {
                        totalMinutes += parseInt(hourMatch[1]) * 60;
                    }

                    // 匹配分钟
                    const minMatch = duration.match(/(\d+)m/);
                    if (minMatch) {
                        totalMinutes += parseInt(minMatch[1]);
                    }

                    // 如果只有秒，小于1分钟
                    const secMatch = duration.match(/^(\d+)s$/);
                    if (secMatch && !hourMatch && !minMatch) {
                        return 0; // 小于1分钟
                    }

                    return totalMinutes;
                };

                const totalMinutes = parseAgeForStyle(age);

                // 超过1天(1440分钟)显示蓝色（长期运行）
                if (totalMinutes >= 1440) {
                    ageClass = styles.ageLong;
                }
                // 小于1小时(60分钟)显示橙色（刚启动）
                else if (totalMinutes < 60) {
                    ageClass = styles.ageNew;
                }

                return (
                    <div className={`${styles.ageBadge} ${ageClass}`}>
                        <Text style={{ fontSize: '11px' }}>{formatAge(age)}</Text>
                    </div>
                );
            },
        },
        {
            title: '操作',
            key: 'action',
            render: (_: any, record: any) => {
              // Grafana datasource 映射
              const datasourceMap: Record<string, string> = {
                'ali-prod': '000000015',
                'ali-test': '000000016',
                'ali-guotai': 'DfWXVPiSz',
                'hwyx-prod': 'fuV4zbn4z',
                'ali-v3': 'aeizcwqu9n668b',
              };
              // 取Pod前缀（第一个10位字母数字前的部分）
              const podName = record.name || '';
              const match = podName.match(/^(.*?)-([a-zA-Z0-9]{8,11})(-|$)/);
              let appid = '';
              if (match) {
                appid = `${match[1]}-${record.namespace}`;
              } else {
                // fallback: 取第一个'-'前
                const podPrefix = podName.split('-')[0] || '';
                appid = `${podPrefix}-${record.namespace}`;
              }
              const grafanaUrl = `https://grafana.blacklake.tech/d/cesoPf_7k/3-0e5908e-e7abaf-e5ba94-e794a8-e79b91-e68ea7?orgId=1&refresh=5m&var-datasource=${datasourceMap[selectedCluster] || ''}&var-namespace=${record.namespace}&var-appid=${appid}&var-pod=${record.name}&var-server_uri=%2F%2A%2A`;
              // Loki datasource 映射
              const lokiDatasourceMap: Record<string, string> = {
                'ali-prod': 'nZduqtJnz',
                'ali-test': '_IMPp9Anz',
                'ali-guotai': 'Og_IJPmSz',
                'hwyx-prod': 'AxXj52k7k',
                'ali-v3': 'aejnfvcnl7t34d',
              };
              const lokiUid = lokiDatasourceMap[selectedCluster] || '';
              const lokiUrl = `https://grafana.blacklake.tech/explore?schemaVersion=1&panes=%7B%22csu%22:%7B%22datasource%22:%22${lokiUid}%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22expr%22:%22%7Bnamespace%3D%5C%22${record.namespace}%5C%22,%20pod%3D%5C%22${record.name}%5C%22%7D%20%7C%3D%20%5C%22ERROR%5C%22%22,%22queryType%22:%22range%22,%22datasource%22:%7B%22type%22:%22loki%22,%22uid%22:%22${lokiUid}%22%7D,%22editorMode%22:%22code%22%7D%5D,%22range%22:%7B%22from%22:%22now-30m%22,%22to%22:%22now%22%7D%7D%7D&orgId=1`;
              return (
                <Space size="small">
                  <Tooltip title="线程详情" placement="top">
                    <Button
                      type="text"
                      size="small"
                      icon={<CodeOutlined />}
                      onClick={() => handlePodCommand(record.name, 'jstack 1', '线程详情')}
                      style={{
                        ...actionButtonStyle,
                        color: '#1890ff'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#e6f7ff';
                        e.currentTarget.style.transform = 'scale(1.1)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                        e.currentTarget.style.transform = 'scale(1)';
                      }}
                    />
                  </Tooltip>
                  <Tooltip title="线程Top" placement="top">
                    <Button
                      type="text"
                      size="small"
                      icon={<BarChartOutlined />}
                      onClick={() => handlePodCommand(record.name, 'top -H -b -n 2', '线程top')}
                      style={{
                        ...actionButtonStyle,
                        color: '#52c41a'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#f6ffed';
                        e.currentTarget.style.transform = 'scale(1.1)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                        e.currentTarget.style.transform = 'scale(1)';
                      }}
                    />
                  </Tooltip>
                  <Tooltip title="高CPU堆栈" placement="top">
                    <Button
                      type="text"
                      size="small"
                      icon={<ThunderboltOutlined />}
                      onClick={() => handlePodCommand(record.name, 'curl -O http://**********:8000/thread.sh &> /dev/null; bash thread.sh', '高线程堆栈')}
                      style={{
                        ...actionButtonStyle,
                        color: '#fa8c16'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#fff7e6';
                        e.currentTarget.style.transform = 'scale(1.1)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                        e.currentTarget.style.transform = 'scale(1)';
                      }}
                    />
                  </Tooltip>
                  <Tooltip title="火焰图" placement="top">
                    <Button
                      type="text"
                      size="small"
                      icon={<FireOutlined />}
                      onClick={() => handlePodCommand(record.name, 'curl -O http://**********:8000/arthas.sh &> /dev/null; bash arthas.sh', '火焰图')}
                      style={{
                        ...actionButtonStyle,
                        color: '#ff4d4f'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#fff1f0';
                        e.currentTarget.style.transform = 'scale(1.1)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                        e.currentTarget.style.transform = 'scale(1)';
                      }}
                    />
                  </Tooltip>
                  <Tooltip title="监控详情" placement="top">
                    <Button
                      type="text"
                      size="small"
                      icon={<MonitorOutlined />}
                      onClick={() => window.open(grafanaUrl, '_blank')}
                      style={{
                        ...actionButtonStyle,
                        color: '#722ed1'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#f9f0ff';
                        e.currentTarget.style.transform = 'scale(1.1)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                        e.currentTarget.style.transform = 'scale(1)';
                      }}
                    />
                  </Tooltip>
                  <Tooltip title="错误日志" placement="top">
                    <Button
                      type="text"
                      size="small"
                      icon={<FileTextOutlined />}
                      onClick={() => window.open(lokiUrl, '_blank')}
                      style={{
                        ...actionButtonStyle,
                        color: '#f5222d'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#fff1f0';
                        e.currentTarget.style.transform = 'scale(1.1)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                        e.currentTarget.style.transform = 'scale(1)';
                      }}
                    />
                  </Tooltip>
                </Space>
              );
            },
          },
    ];

  // Modify the handlePodCommand function
  const handlePodCommand = async (podName: string, command: string, title: string = '线程详情') => {
    // 特殊处理火焰图命令 - 显示进度条
    if (title === '火焰图') {
      return handleFlameGraphCommand(podName, command, title);
    }

    try {
      const response = await fetch(
        `/api/v1/pod/command?cluster=${selectedCluster}&namespace=${selectedNamespace}&pod=${podName}&command=${encodeURIComponent(command)}`
      );
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to execute command');
      }

      // Use Modal with custom content
      const modal = Modal.info({
        title: title,
        width: 800,
        content: (
          <ThreadAnalysisModal
            output={data.output}
            podName={podName}
            title={title}
            onClose={() => modal.destroy()}
          />
        ),
        icon: null,
        maskClosable: true,
        okButtonProps: { style: { display: 'none' } } // Hide the default OK button
      });
    } catch (error) {
      message.error('Failed to execute command: ' + (error instanceof Error ? error.message : String(error)));
    }
  };

  // 专门处理火焰图命令的函数
  const handleFlameGraphCommand = async (podName: string, command: string, title: string) => {
    let progressModal: any = null;
    let waitProgress = 0;
    let waitInterval: NodeJS.Timeout | null = null;

    try {
      // 先执行命令
      const response = await fetch(
        `/api/v1/pod/command?cluster=${selectedCluster}&namespace=${selectedNamespace}&pod=${podName}&command=${encodeURIComponent(command)}`
      );
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to execute command');
      }

      // 显示等待火焰图生成的进度条
      progressModal = Modal.info({
        title: (
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <FireOutlined style={{ color: '#ff4d4f' }} />
            <span>🔥 正在生成火焰图...</span>
          </div>
        ),
        width: 500,
        content: (
          <div style={{ padding: '20px 0' }}>
            <div style={{
              marginBottom: '20px',
              textAlign: 'center',
              background: 'linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%)',
              padding: '16px',
              borderRadius: '8px',
              border: '1px solid #b7eb8f'
            }}>
              <div style={{ marginBottom: '12px' }}>
                <Spin size="large" style={{ color: '#52c41a' }} />
              </div>
              <div style={{ color: '#262626', fontSize: '16px', fontWeight: 500 }}>
                ⏳ 等待火焰图生成完成...
              </div>
            </div>
            <Progress
              percent={waitProgress}
              status="active"
              strokeColor={{
                '0%': '#52c41a',
                '50%': '#73d13d',
                '100%': '#95de64',
              }}
              trailColor="#f6ffed"
              strokeWidth={8}
              style={{ marginBottom: '20px' }}
              format={(percent) => (
                <span style={{ color: '#52c41a', fontWeight: 600 }}>
                  {percent}%
                </span>
              )}
            />
            <div style={{
              textAlign: 'center',
              background: '#f6ffed',
              padding: '12px',
              borderRadius: '6px',
              border: '1px solid #b7eb8f'
            }}>
              <div style={{ color: '#262626', fontSize: '14px', marginBottom: '4px' }}>
                目标Pod: <strong style={{ color: '#1890ff' }}>{podName}</strong>
              </div>
              <div style={{ color: '#52c41a', fontSize: '13px' }}>
                🔥 等待火焰图生成中... (预计35秒)
              </div>
            </div>
          </div>
        ),
        icon: null,
        maskClosable: false,
        okButtonProps: { style: { display: 'none' } },
        closable: false
      });

      // 等待进度条动画
      waitInterval = setInterval(() => {
        waitProgress += 2.86; // 35秒内从0到100
        if (waitProgress >= 100) {
          waitProgress = 100;
          if (waitInterval) {
            clearInterval(waitInterval);
            waitInterval = null;
          }
        }

        // 更新等待进度
        if (progressModal) {
          progressModal.update({
            content: (
              <div style={{ padding: '20px 0' }}>
                <div style={{
                  marginBottom: '20px',
                  textAlign: 'center',
                  background: 'linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%)',
                  padding: '16px',
                  borderRadius: '8px',
                  border: '1px solid #b7eb8f'
                }}>
                  <div style={{ marginBottom: '12px' }}>
                    <Spin size="large" style={{ color: '#52c41a' }} />
                  </div>
                  <div style={{ color: '#262626', fontSize: '16px', fontWeight: 500 }}>
                    ⏳ 等待火焰图生成完成...
                  </div>
                </div>
                <Progress
                  percent={Math.round(waitProgress)}
                  status="active"
                  strokeColor={{
                    '0%': '#52c41a',
                    '50%': '#73d13d',
                    '100%': '#95de64',
                  }}
                  trailColor="#f6ffed"
                  strokeWidth={8}
                  style={{ marginBottom: '20px' }}
                  format={(percent) => (
                    <span style={{ color: '#52c41a', fontWeight: 600 }}>
                      {percent}%
                    </span>
                  )}
                />
                <div style={{
                  textAlign: 'center',
                  background: '#f6ffed',
                  padding: '12px',
                  borderRadius: '6px',
                  border: '1px solid #b7eb8f'
                }}>
                  <div style={{ color: '#262626', fontSize: '14px', marginBottom: '4px' }}>
                    目标Pod: <strong style={{ color: '#1890ff' }}>{podName}</strong>
                  </div>
                  <div style={{ color: '#52c41a', fontSize: '13px' }}>
                    🔥 等待火焰图生成中... ({Math.round(waitProgress)}%)
                  </div>
                </div>
              </div>
            )
          });
        }
      }, 1000); // 每秒更新一次

      // 等待35秒
      await new Promise(resolve => setTimeout(resolve, 35000));

      // 清理定时器和进度条
      if (waitInterval) {
        clearInterval(waitInterval);
        waitInterval = null;
      }
      if (progressModal) {
        progressModal.destroy();
        progressModal = null;
      }

      let modalContent = data.output;

      // 处理火焰图输出
      if (data.output) {
        // 从输出中提取HTML文件名
        const htmlFileMatch = data.output.match(/profiler output file will be: (.+\.html)/);
        if (htmlFileMatch) {
          const fullPath = htmlFileMatch[1];
          const fileName = fullPath.split('/').pop(); // 提取文件名

          // 生成appid
          const match = podName.match(/^(.*?)-([a-zA-Z0-9]{8,11})(-|$)/);
          let appid = '';
          if (match) {
            appid = `${match[1]}-${selectedNamespace}`;
          } else {
            const podPrefix = podName.split('-')[0] || '';
            appid = `${podPrefix}-${selectedNamespace}`;
          }

          // 获取当前Pod的Node信息
          const currentPod = podList.find(pod => pod.name === podName);
          let nodeUrl = currentPod?.node || '';

          // 如果Node以cn-shanghai开头，添加.blacklake.tech后缀
          if (nodeUrl.startsWith('cn-shanghai')) {
            nodeUrl += '.blacklake.tech';
          }

          // 生成火焰图链接
          const flameGraphUrl = `http://${nodeUrl}:8888/${appid}/${fileName}`;

          // 在原输出后添加链接提示
          modalContent = `${data.output}\n\n🔥 打开火焰图: ${flameGraphUrl}`;
        }
      }

      // 显示成功提示
      message.success({
        content: (
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <FireOutlined style={{ color: '#52c41a' }} />
            <span>🎉 火焰图生成完成！</span>
          </div>
        ),
        duration: 3
      });

      // 显示结果模态框
      const resultModal = Modal.info({
        title: (
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <FireOutlined style={{ color: '#52c41a' }} />
            <span>✅ {title}生成完成</span>
          </div>
        ),
        width: 800,
        content: (
          <ThreadAnalysisModal
            output={modalContent}
            podName={podName}
            title={title}
            onClose={() => resultModal.destroy()}
          />
        ),
        icon: null,
        maskClosable: true,
        okButtonProps: { style: { display: 'none' } }
      });

    } catch (error) {
      // 清理所有定时器和模态框
      if (waitInterval) {
        clearInterval(waitInterval);
      }
      if (progressModal) {
        progressModal.destroy();
      }

      message.error('火焰图生成失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  };

  return (
    <PageContainer>
      {/* 现代化控制面板 */}
      <Card
        className={styles.controlPanel}
        style={{
          marginBottom: 16,
          borderRadius: '12px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px', flexWrap: 'wrap' }}>
          {/* 集群选择器 */}
          <div style={{ display: 'flex', alignItems: 'center', minWidth: '240px' }}>
            <ClusterOutlined style={{ fontSize: '16px', color: '#1890ff', marginRight: 8 }} />
            <div>
              <Text style={{ fontSize: '12px', color: '#666', display: 'block', marginBottom: '4px' }}>
                集群 <Tooltip title="系统会记住您的选择"><span style={{ color: '#52c41a', fontSize: '10px' }}>●</span></Tooltip>
              </Text>
              <Select
                value={selectedCluster}
                onChange={handleClusterChange}
                style={{ width: '180px', height: '40px' }}
                size="middle"
              >
                {clusterList.map(item => (
                  <Option key={item.name} value={item.name}>
                    {item.displayName}
                  </Option>
                ))}
              </Select>
            </div>
          </div>

          {/* 命名空间选择器 */}
          <div style={{ display: 'flex', alignItems: 'center', minWidth: '300px' }}>
            <AppstoreOutlined style={{ fontSize: '16px', color: '#52c41a', marginRight: 8 }} />
            <div>
              <Text style={{ fontSize: '12px', color: '#666', display: 'block', marginBottom: '4px' }}>
                命名空间 <Tooltip title="系统会记住您的选择"><span style={{ color: '#52c41a', fontSize: '10px' }}>●</span></Tooltip>
              </Text>
              <Select
                showSearch
                optionFilterProp="children"
                filterOption={(input, option) =>
                  String(option?.children)
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
                value={selectedNamespace}
                onChange={handleNamespaceChange}
                style={{ width: '250px', height: '40px' }}
                size="middle"
              >
                {namespaceList.map(ns => (
                  <Option key={ns.name} value={ns.name}>
                    {ns.name}
                  </Option>
                ))}
              </Select>
            </div>
          </div>

          {/* 搜索框 */}
          <div style={{ display: 'flex', alignItems: 'center', flex: 1, minWidth: '300px' }}>
            <SearchOutlined style={{ fontSize: '16px', color: '#722ed1', marginRight: 8 }} />
            <div style={{ width: '75%' }}>
              <Text style={{ fontSize: '12px', color: '#666', display: 'block', marginBottom: '4px' }}>
                搜索Pod <Tooltip title="系统会记住您的搜索内容"><span style={{ color: '#52c41a', fontSize: '10px' }}>●</span></Tooltip>
              </Text>
              <Search
                placeholder="输入Pod名称搜索"
                value={searchText}
                onChange={e => handleSearch(e.target.value)}
                onSearch={value => handleSearch(value)}
                style={{ width: '100%', height: '40px' }}
                size="middle"
              />
            </div>
          </div>

          {/* 刷新按钮 */}
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              loading={loading}
              onClick={() => fetchPodList('')}
              className={styles.refreshButton}
              size="middle"
              style={{ width: '140px', height: '40px' }}
            >
              刷新
            </Button>
          </div>
        </div>
      </Card>

      {/* Pod统计信息 */}
      <PodStatistics podList={podList} />

      {/* Pod列表表格 */}
      <Card className={styles.tableCard}>
        <Table
          rowKey="name"
          dataSource={podList}
          columns={columns}
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            pageSizeOptions: ['10', '20', '50', '100'],
            defaultPageSize: 20,
          }}
          scroll={{ x: 1200 }}
          size="small"
        />
      </Card>
    </PageContainer>
  );
};

export default PodManagement; 