// 运行时间格式化函数测试
// 用于验证Go Duration格式的解析和显示

// 模拟formatAge函数
const formatAge = (age) => {
    if (!age) return '';
    
    // Go Duration格式: "2h30m45s", "1h30m", "45m30s", "30s" 等
    const parseGoDuration = (duration) => {
        const dayMatch = duration.match(/(\d+)h(\d+)m(\d+)s/);
        const hourMinMatch = duration.match(/(\d+)h(\d+)m/);
        const hourMatch = duration.match(/^(\d+)h$/);
        const minSecMatch = duration.match(/(\d+)m(\d+)s/);
        const minMatch = duration.match(/^(\d+)m$/);
        const secMatch = duration.match(/^(\d+)s$/);
        
        if (dayMatch) {
            const hours = parseInt(dayMatch[1]);
            const minutes = parseInt(dayMatch[2]);
            if (hours >= 24) {
                const days = Math.floor(hours / 24);
                const remainingHours = hours % 24;
                return remainingHours > 0 ? `${days}d${remainingHours}h` : `${days}d`;
            }
            return minutes > 0 ? `${hours}h${minutes}m` : `${hours}h`;
        }
        
        if (hourMinMatch) {
            const hours = parseInt(hourMinMatch[1]);
            const minutes = parseInt(hourMinMatch[2]);
            if (hours >= 24) {
                const days = Math.floor(hours / 24);
                const remainingHours = hours % 24;
                return remainingHours > 0 ? `${days}d${remainingHours}h` : `${days}d`;
            }
            return minutes > 0 ? `${hours}h${minutes}m` : `${hours}h`;
        }
        
        if (hourMatch) {
            const hours = parseInt(hourMatch[1]);
            if (hours >= 24) {
                const days = Math.floor(hours / 24);
                const remainingHours = hours % 24;
                return remainingHours > 0 ? `${days}d${remainingHours}h` : `${days}d`;
            }
            return `${hours}h`;
        }
        
        if (minSecMatch) {
            const minutes = parseInt(minSecMatch[1]);
            return `${minutes}m`;
        }
        
        if (minMatch) {
            return `${minMatch[1]}m`;
        }
        
        if (secMatch) {
            return `${secMatch[1]}s`;
        }
        
        return duration;
    };
    
    return parseGoDuration(age);
};

// 测试用例
const testCases = [
    // Go Duration格式测试
    { input: '30s', expected: '30s', description: '30秒' },
    { input: '5m', expected: '5m', description: '5分钟' },
    { input: '5m30s', expected: '5m', description: '5分30秒' },
    { input: '2h', expected: '2h', description: '2小时' },
    { input: '2h30m', expected: '2h30m', description: '2小时30分' },
    { input: '2h30m45s', expected: '2h30m', description: '2小时30分45秒' },
    { input: '25h', expected: '1d1h', description: '25小时' },
    { input: '48h', expected: '2d', description: '48小时' },
    { input: '72h30m', expected: '3d30m', description: '72小时30分' },
    { input: '', expected: '', description: '空值' },
];

// 运行测试
console.log('=== 运行时间格式化测试 ===\n');

testCases.forEach((testCase, index) => {
    const result = formatAge(testCase.input);
    const passed = result === testCase.expected;
    
    console.log(`测试 ${index + 1}: ${testCase.description}`);
    console.log(`输入: "${testCase.input}"`);
    console.log(`期望: "${testCase.expected}"`);
    console.log(`实际: "${result}"`);
    console.log(`结果: ${passed ? '✅ 通过' : '❌ 失败'}`);
    console.log('---');
});

// 样式分类测试
const getAgeStyle = (age) => {
    if (!age) return 'unknown';
    
    const parseAgeForStyle = (duration) => {
        let totalMinutes = 0;
        
        const hourMatch = duration.match(/(\d+)h/);
        if (hourMatch) {
            totalMinutes += parseInt(hourMatch[1]) * 60;
        }
        
        const minMatch = duration.match(/(\d+)m/);
        if (minMatch) {
            totalMinutes += parseInt(minMatch[1]);
        }
        
        const secMatch = duration.match(/^(\d+)s$/);
        if (secMatch && !hourMatch && !minMatch) {
            return 0;
        }
        
        return totalMinutes;
    };
    
    const totalMinutes = parseAgeForStyle(age);
    
    if (totalMinutes >= 1440) return 'long'; // > 1天
    if (totalMinutes < 60) return 'new';     // < 1小时
    return 'normal';                         // 正常
};

console.log('\n=== 样式分类测试 ===\n');

const styleTestCases = [
    { input: '30s', expected: 'new' },
    { input: '30m', expected: 'new' },
    { input: '1h', expected: 'normal' },
    { input: '12h', expected: 'normal' },
    { input: '25h', expected: 'long' },
    { input: '48h', expected: 'long' },
];

styleTestCases.forEach((testCase, index) => {
    const result = getAgeStyle(testCase.input);
    const passed = result === testCase.expected;
    
    console.log(`样式测试 ${index + 1}: ${testCase.input}`);
    console.log(`期望样式: ${testCase.expected}`);
    console.log(`实际样式: ${result}`);
    console.log(`结果: ${passed ? '✅ 通过' : '❌ 失败'}`);
    console.log('---');
});

console.log('\n测试完成！');
