import React, { useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Card, Select, Button, Spin, Typography, List, message } from 'antd';
import { request } from 'umi';
import styles from './index.less';

const { Option } = Select;
const { Text } = Typography;

const AIDiagnosis: React.FC = () => {
  const [clusters, setClusters] = useState<any[]>([]);
  const [namespaces, setNamespaces] = useState<string[]>([]);
  const [selectedCluster, setSelectedCluster] = useState<string>('');
  const [selectedNamespace, setSelectedNamespace] = useState<string>('');
  const [events, setEvents] = useState<any[]>([]);
  const [selectedEvents, setSelectedEvents] = useState<any[]>([]);
  const [aiAnalysis, setAiAnalysis] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [analysisLoading, setAnalysisLoading] = useState<boolean>(false);

  // 获取集群列表
  useEffect(() => {
    const fetchClusters = async () => {
      try {
        const response = await request('/api/cluster/list');
        if (response && response.data) {
          setClusters(response.data);
        }
      } catch (error) {
        message.error('获取集群列表失败');
        console.error('获取集群列表失败:', error);
      }
    };

    fetchClusters();
  }, []);

  // 当选择集群后，获取命名空间列表
  const handleClusterChange = async (value: string) => {
    setSelectedCluster(value);
    setSelectedNamespace('');
    setEvents([]);
    setSelectedEvents([]);
    setAiAnalysis('');
    
    try {
      setLoading(true);
      const response = await request(`/api/cluster/${value}/namespaces`);
      if (response && response.data) {
        setNamespaces(response.data);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      message.error('获取命名空间列表失败');
      console.error('获取命名空间列表失败:', error);
    }
  };

  // 当选择命名空间后，获取事件列表
  const handleNamespaceChange = async (value: string) => {
    setSelectedNamespace(value);
    setEvents([]);
    setSelectedEvents([]);
    setAiAnalysis('');
    
    try {
      setLoading(true);
      const response = await request(`/api/cluster/${selectedCluster}/namespace/${value}/events`);
      if (response && response.data) {
        setEvents(response.data);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      message.error('获取事件列表失败');
      console.error('获取事件列表失败:', error);
    }
  };

  // 选择事件
  const handleEventSelect = (event: any) => {
    const isSelected = selectedEvents.some(e => e.metadata?.uid === event.metadata?.uid);
    
    if (isSelected) {
      setSelectedEvents(selectedEvents.filter(e => e.metadata?.uid !== event.metadata?.uid));
    } else {
      setSelectedEvents([...selectedEvents, event]);
    }
  };

  // 发送事件到AI进行分析
  const handleAIAnalysis = async () => {
    if (selectedEvents.length === 0) {
      message.warning('请至少选择一个事件进行分析');
      return;
    }

    try {
      setAnalysisLoading(true);
      const response = await request(`/api/ai/diagnosis`, {
        method: 'POST',
        data: {
          cluster: selectedCluster,
          namespace: selectedNamespace,
          events: selectedEvents,
        },
        timeout: 120000,
      });
      
      if (response && response.data) {
        setAiAnalysis(response.data);
      }
      setAnalysisLoading(false);
    } catch (error) {
      setAnalysisLoading(false);
      message.error('AI分析失败');
      console.error('AI分析失败:', error);
    }
  };

  return (
    <PageContainer title="AI诊断">
      <Card>
        <div className={styles.selectionContainer}>
          <div className={styles.selectItem}>
            <span className={styles.label}>集群:</span>
            <Select
              placeholder="请选择集群"
              style={{ width: 200 }}
              onChange={handleClusterChange}
              value={selectedCluster || undefined}
            >
              {clusters.map((cluster: any) => (
                <Option key={cluster.id} value={cluster.id}>
                  {cluster.name}
                </Option>
              ))}
            </Select>
          </div>
          
          <div className={styles.selectItem}>
            <span className={styles.label}>命名空间:</span>
            <Select
              placeholder="请选择命名空间"
              style={{ width: 200 }}
              showSearch
              allowClear
              filterOption={(input, option) =>
                (option?.value as string)
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              onChange={handleNamespaceChange}
              value={selectedNamespace || undefined}
              disabled={!selectedCluster}
            >
              {namespaces.map((namespace: string) => (
                <Option key={namespace} value={namespace}>
                  {namespace}
                </Option>
              ))}
            </Select>
          </div>
        </div>
      </Card>

      {loading ? (
        <Card style={{ marginTop: 16, textAlign: 'center' }}>
          <Spin tip="加载中..." />
        </Card>
      ) : (
        <>
          {events.length > 0 && (
            <Card 
              title="事件列表" 
              style={{ marginTop: 16 }}
              extra={
                <Button 
                  type="primary" 
                  onClick={handleAIAnalysis}
                  disabled={selectedEvents.length === 0}
                >
                  AI诊断分析
                </Button>
              }
            >
              <List
                dataSource={events}
                renderItem={event => (
                  <List.Item 
                    key={event.metadata?.uid}
                    className={
                      selectedEvents.some(e => e.metadata?.uid === event.metadata?.uid)
                        ? styles.selectedEvent
                        : ''
                    }
                    onClick={() => handleEventSelect(event)}
                  >
                    <List.Item.Meta
                      title={
                        <div>
                          <Text strong>{event.reason}</Text>
                          <Text type={event.type === 'Warning' ? 'danger' : 'secondary'}>
                            {` [${event.type}]`}
                          </Text>
                        </div>
                      }
                      description={
                        <div>
                          <div>{event.message}</div>
                          <div className={styles.eventMeta}>
                            <span>对象: {event.involvedObject?.kind}/{event.involvedObject?.name}</span>
                            <span>时间: {new Date(event.lastTimestamp).toLocaleString()}</span>
                          </div>
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </Card>
          )}

          {analysisLoading ? (
            <Card style={{ marginTop: 16, textAlign: 'center' }}>
              <Spin tip="AI正在分析中..." />
            </Card>
          ) : (
            aiAnalysis && (
              <Card title="AI诊断结果" style={{ marginTop: 16 }}>
                <div className={styles.aiAnalysisContainer}>
                  <div dangerouslySetInnerHTML={{ __html: aiAnalysis.replace(/\n/g, '<br/>') }} />
                </div>
              </Card>
            )
          )}
        </>
      )}
    </PageContainer>
  );
};

export default AIDiagnosis; 