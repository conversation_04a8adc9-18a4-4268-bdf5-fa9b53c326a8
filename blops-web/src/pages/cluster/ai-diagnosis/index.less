.selectionContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
}

.selectItem {
  display: flex;
  align-items: center;
}

.label {
  margin-right: 8px;
  font-weight: 500;
}

.selectedEvent {
  background-color: #f0f7ff;
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    background-color: #e6f4ff;
  }
}

.eventMeta {
  display: flex;
  justify-content: space-between;
  color: #999;
  font-size: 12px;
  margin-top: 4px;
}

.aiAnalysisContainer {
  max-height: 600px;
  overflow-y: auto;
  padding: 8px;
  
  h1, h2, h3, h4, h5, h6 {
    margin-top: 16px;
    margin-bottom: 8px;
  }
  
  p {
    margin-bottom: 16px;
    line-height: 1.6;
  }
  
  ul, ol {
    padding-left: 24px;
    margin-bottom: 16px;
  }
  
  code {
    background-color: #f5f5f5;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'SFMono-Regular', <PERSON><PERSON><PERSON>, 'Liberation Mono', <PERSON><PERSON>, monospace;
  }
  
  pre {
    background-color: #f5f5f5;
    padding: 16px;
    border-radius: 6px;
    overflow-x: auto;
    margin-bottom: 16px;
  }
  
  blockquote {
    border-left: 4px solid #ddd;
    padding-left: 16px;
    color: #666;
    margin-bottom: 16px;
  }
  
  table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 16px;
    
    th, td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }
    
    th {
      background-color: #f5f5f5;
    }
    
    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
  }
} 