import React, { useState, useEffect, useCallback } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Modal } from 'antd';
import { Card, Table, Select, Row, Col, message, Spin, Tag, Input, Tooltip, Button, Space, Divider, Typography } from 'antd';
import { EyeOutlined, PlusOutlined, ReloadOutlined, SearchOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { list_ingresses, list_namespaces, get_resource } from '@/services/cluster';
import moment from 'moment';
import { debounce } from 'lodash';
import yaml from 'js-yaml';

const { Option } = Select;
const { Search } = Input;
const { Text, Title } = Typography;

// 固定的集群选项
const fixedClusterOptions = [
  { name: 'ali-test', displayName: 'ali-test' },
  { name: 'ali-prod', displayName: 'ali-prod' },
  { name: 'ali-guotai', displayName: 'ali-guotai' },
  { name: 'hwyx-prod', displayName: 'hwyx-prod' },
  { name: 'ali-v3', displayName: 'ali-v3' },
];

// 创建日期格式化函数
const formatDate = (timestamp: string | undefined): string => {
  if (!timestamp) return '-';
  
  try {
    // 处理不同格式的时间戳
    let date;
    if (timestamp.includes('+0800 CST')) {
      // 处理 "2024-04-15 14:27:52 +0800 CST" 格式
      const parsedDate = timestamp.replace(' +0800 CST', '');
      date = new Date(parsedDate);
    } else {
      // 标准ISO格式
      date = new Date(timestamp);
    }
    
    if (isNaN(date.getTime())) {
      return timestamp; // 如果解析失败，直接返回原始字符串
    }
    
    // 使用更简洁的格式：YYYY-MM-DD HH:MM
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  } catch (e) {
    console.error('解析时间戳失败:', e);
    return timestamp;
  }
};

interface IngressRulePath {
  path: string;
  pathType: string;
  backend: {
    service: {
      name: string;
      port: {
        number?: number;
        name?: string;
      };
    };
  };
}

interface IngressRule {
  host?: string;
  http?: {
    paths: IngressRulePath[];
  };
}

interface IngressTLS {
  hosts?: string[];
  secretName?: string;
}

interface IngressItem {
  key: string;
  name: string;
  namespace: string;
  host: string;
  rules: IngressRule[];
  tls?: IngressTLS[];
  creationTimestamp: string;
}

interface NamespaceItem {
  id: string;
  name: string;
}

const IngressDomainsPage: React.FC = () => {
  // 本地存储键名
  const STORAGE_KEYS = {
    CLUSTER: 'ingress-management-selected-cluster',
    NAMESPACE: 'ingress-management-selected-namespace',
    SEARCH_TEXT: 'ingress-management-search-text'
  };

  // 从本地存储获取上次的选择
  const getStoredValue = (key: string, defaultValue: string = '') => {
    try {
      return localStorage.getItem(key) || defaultValue;
    } catch (error) {
      console.warn('Failed to read from localStorage:', error);
      return defaultValue;
    }
  };

  // 保存到本地存储
  const setStoredValue = (key: string, value: string) => {
    try {
      localStorage.setItem(key, value);
    } catch (error) {
      console.warn('Failed to write to localStorage:', error);
    }
  };

  // 状态管理
  const [allIngresses, setAllIngresses] = useState<IngressItem[]>([]);
  const [filteredIngresses, setFilteredIngresses] = useState<IngressItem[]>([]);
  const [namespaces, setNamespaces] = useState<NamespaceItem[]>([]);
  const [selectedCluster, setSelectedCluster] = useState<string>(() =>
    getStoredValue(STORAGE_KEYS.CLUSTER, fixedClusterOptions.length > 0 ? fixedClusterOptions[0].name : '')
  );
  const [selectedNamespace, setSelectedNamespace] = useState<string>(() =>
    getStoredValue(STORAGE_KEYS.NAMESPACE)
  );
  const [loading, setLoading] = useState<boolean>(false);
  const [loadingNamespaces, setLoadingNamespaces] = useState<boolean>(false);
  const [searchText, setSearchText] = useState<string>(() =>
    getStoredValue(STORAGE_KEYS.SEARCH_TEXT)
  );
  const [namespaceDropdownOpen, setNamespaceDropdownOpen] = useState<boolean>(false);
  const [namespaceSearchText, setNamespaceSearchText] = useState<string>('');
  const [isInitialLoad, setIsInitialLoad] = useState<boolean>(true);
  const [detailVisible, setDetailVisible] = useState<boolean>(false);
  const [isYamlModalVisible, setIsYamlModalVisible] = useState<boolean>(false);
  const [yamlContent, setYamlContent] = useState<string>('');
  const [yamlLoading, setYamlLoading] = useState<boolean>(false);
  const [currentIngress, setCurrentIngress] = useState<IngressItem | null>(null);

  // 获取集群列表
  const fetchClusters = useCallback(() => {
    // 使用固定集群选项，不需要发送请求
    // 如果已选择集群，则获取其命名空间
    if (selectedCluster) {
      fetchNamespaces(selectedCluster);
    }
  }, [selectedCluster]);

  // 获取命名空间列表
  const fetchNamespaces = async (clusterName: string) => {
    if (!clusterName) return;

    setLoadingNamespaces(true);
    setAllIngresses([]);
    setFilteredIngresses([]);

    try {
      const res = await list_namespaces({ cluster: clusterName });
      if (res.result === 'SUCCESS') {
        const nsList: NamespaceItem[] = (res.data || []).map((ns: any) => ({ id: ns.name, name: ns.name }));
        setNamespaces(nsList);

        // 如果是初始加载，检查保存的命名空间是否存在
        if (isInitialLoad) {
          const savedNamespace = getStoredValue(STORAGE_KEYS.NAMESPACE);
          console.log('Ingress初始加载 - 保存的命名空间:', savedNamespace);
          console.log('Ingress初始加载 - 可用命名空间列表:', nsList.map(ns => ns.name));

          if (savedNamespace && nsList.some(ns => ns.name === savedNamespace)) {
            // 保存的命名空间存在，显式设置以确保UI正确显示
            console.log('Ingress恢复保存的命名空间:', savedNamespace);
            setSelectedNamespace(savedNamespace);
          } else if (savedNamespace && !nsList.some(ns => ns.name === savedNamespace)) {
            // 保存的命名空间不存在，清空选择
            setSelectedNamespace('');
            setStoredValue(STORAGE_KEYS.NAMESPACE, '');
          } else if (!savedNamespace) {
            // 没有保存的命名空间，设置默认值
            if (clusterName === 'ali-test') {
              const barrenDevExists = nsList.some(ns => ns.name === 'barren-dev');
              if (barrenDevExists) {
                setSelectedNamespace('barren-dev');
                setStoredValue(STORAGE_KEYS.NAMESPACE, 'barren-dev');
              } else if (nsList.length > 0) {
                setSelectedNamespace(nsList[0].id);
                setStoredValue(STORAGE_KEYS.NAMESPACE, nsList[0].id);
              }
            } else if (nsList.length > 0) {
              setSelectedNamespace(nsList[0].id);
              setStoredValue(STORAGE_KEYS.NAMESPACE, nsList[0].id);
            }
          }
          setIsInitialLoad(false);
        } else {
          // 用户手动切换集群，设置默认命名空间
          if (clusterName === 'ali-test') {
            const barrenDevExists = nsList.some(ns => ns.name === 'barren-dev');
            if (barrenDevExists) {
              setSelectedNamespace('barren-dev');
              setStoredValue(STORAGE_KEYS.NAMESPACE, 'barren-dev');
            } else if (nsList.length > 0) {
              setSelectedNamespace(nsList[0].id);
              setStoredValue(STORAGE_KEYS.NAMESPACE, nsList[0].id);
            } else {
              setSelectedNamespace('');
              setStoredValue(STORAGE_KEYS.NAMESPACE, '');
            }
          } else if (nsList.length > 0) {
            setSelectedNamespace(nsList[0].id);
            setStoredValue(STORAGE_KEYS.NAMESPACE, nsList[0].id);
          } else {
            setSelectedNamespace('');
            setStoredValue(STORAGE_KEYS.NAMESPACE, '');
          }
        }
      } else {
        message.error(res.message || '获取命名空间列表失败');
      }
    } catch (error) {
      console.error('获取命名空间列表失败:', error);
      message.error('获取命名空间列表失败');
    } finally {
      setLoadingNamespaces(false);
    }
  };

  // 获取 Ingress 列表
  const fetchIngresses = useCallback(async (clusterName: string, namespace: string): Promise<boolean> => {
    if (!clusterName || !namespace) return false;
    
    setLoading(true);
    
    try {
      const res = await list_ingresses({ cluster: clusterName, namespace });
      if (res.result === 'SUCCESS' && res.data) {
        // 数据处理和格式化
        const processedData = res.data.map((item: any) => ({
          key: `${item.name}-${item.creationTimestamp}-${namespace}`,
          name: item.name,
          namespace,
          host: item.rules && item.rules.length > 0 && item.rules[0].host ? item.rules[0].host : '',
          rules: item.rules || [],
          tls: item.tls || [],
          creationTimestamp: item.creationTimestamp,
          // 添加原始数据以便在详细视图中使用
          originalData: item,
        }));
        
        // 按创建时间排序，最新的在前面
        processedData.sort((a: IngressItem, b: IngressItem) => {
          const timeA = a.creationTimestamp || '';
          const timeB = b.creationTimestamp || '';
          return timeB.localeCompare(timeA); // 降序排列
        });
        
        setAllIngresses(processedData);
        setFilteredIngresses(processedData);
        return true; // Ingresses found and processed
      } else if (res.result === 'SUCCESS' && (!res.data || res.data.length === 0)) {
        // Successful call but no ingresses
        setAllIngresses([]);
        setFilteredIngresses([]);
        return false; // No ingresses found
      } else {
        // API call failed or unexpected result
        message.error(res.message || '获取Ingress列表失败');
        setAllIngresses([]);
        setFilteredIngresses([]);
        return false; // Fetching failed
      }
    } catch (err) {
      console.error('获取Ingress列表失败:', err);
      message.error('获取Ingress列表失败，请查看控制台获取更多信息。');
      setAllIngresses([]);
      setFilteredIngresses([]);
      return false; // Error occurred
    } finally {
      setLoading(false);
    }
  }, []);

  // 初始化加载
  useEffect(() => {
    fetchClusters();
  }, []);

  // 集群变化时获取命名空间
  useEffect(() => {
    if (selectedCluster) {
      fetchNamespaces(selectedCluster);
    }
  }, [selectedCluster]);

  // 命名空间变化时获取 Ingress
  useEffect(() => {
    if (selectedCluster && selectedNamespace) {
      fetchIngresses(selectedCluster, selectedNamespace);
    } else {
      setAllIngresses([]);
      setFilteredIngresses([]);
    }
  }, [selectedCluster, selectedNamespace, fetchIngresses]);

  // 搜索功能 - 只针对 'Host' 列进行模糊搜索
  const debouncedSearch = useCallback(
    debounce((text: string) => {
      if (!text) {
        setFilteredIngresses(allIngresses);
        return;
      }
      const searchLower = text.toLowerCase();
      const filtered = allIngresses.filter(ingress => {
        // 只搜索主机名 (Host)
        // 主机名搜索 - 包括规则中的所有hosts
        if (!ingress.rules || ingress.rules.length === 0) {
          return false;
        }
        
        // 检查所有规则中的host字段
        return ingress.rules.some(rule => 
          rule.host && rule.host.toLowerCase().includes(searchLower)
        );
      });
      setFilteredIngresses(filtered);
    }, 300),
    [allIngresses]
  );

  useEffect(() => {
    debouncedSearch(searchText);
  }, [searchText, debouncedSearch]);

  // 用于命名空间搜索的筛选函数
  const getFilteredNamespaces = () => {
    if (!namespaceSearchText) return namespaces;
    return namespaces.filter(ns => 
      ns.name.toLowerCase().includes(namespaceSearchText.toLowerCase())
    );
  };

  // 刷新数据
  const handleRefresh = async () => {
    if (selectedCluster && selectedNamespace) {
      const ingressesFound = await fetchIngresses(selectedCluster, selectedNamespace);
      if (ingressesFound) {
        message.info('数据已刷新'); 
      } else {
        // Optional: message indicating no ingresses found, or just silence
        // message.info('未找到Ingress规则'); 
      }
    }
  };

  // 添加 Ingress (待实现)
  const handleAddIngress = () => {
    message.info('添加 Ingress 功能待实现');
  };

  // 查看 Ingress 详情 (original, now for basic details if any)
  // const handleViewDetailsOriginal = (record: IngressItem) => {
  //   setCurrentIngress(record);
  //   setDetailVisible(true);
  // };

  // 查看 Ingress YAML 详情
  const handleViewDetails = async (record: IngressItem) => {
    setCurrentIngress(record); // Keep track of the current ingress for modal title or other uses
    setIsYamlModalVisible(true);
    setYamlLoading(true);
    setYamlContent(''); // Clear previous content

    try {
      const params = {
        cluster: selectedCluster,
        namespace: record.namespace,
        name: record.name,
        resourceType: 'ingress', // User confirmed this parameter name
      };
      const response = await get_resource(params);
      // API is expected to return { code: 200, message: "success", result: "SUCCESS", data: { /* k8s_object */ } }
      if (response && response.code === 200 && response.data) {
        if (typeof response.data === 'object' && response.data !== null) {
          // Remove managedFields from metadata if it exists
          if (response.data && response.data.metadata && response.data.metadata.managedFields) {
            delete response.data.metadata.managedFields;
          }
          
          // 移除 kubectl.kubernetes.io/last-applied-configuration 注解
          if (response.data && response.data.metadata && response.data.metadata.annotations && 
              response.data.metadata.annotations['kubectl.kubernetes.io/last-applied-configuration']) {
            delete response.data.metadata.annotations['kubectl.kubernetes.io/last-applied-configuration'];
          }
          
          // Convert the Kubernetes JSON object (which is response.data) to YAML
          setYamlContent(yaml.dump(response.data));
        } else if (typeof response.data === 'string') {
          // If it's already a string (e.g., pre-formatted YAML or an error string from data field)
          setYamlContent(response.data);
        } else {
          const unexpectedDataMsg = 'Received unexpected data format for Ingress resource.';
          setYamlContent(unexpectedDataMsg);
          message.error(unexpectedDataMsg);
        }
      } else {
        // Handle cases where response exists but indicates an error (e.g., non-200 code or missing data)
        // or if response itself is falsy (e.g. network error before server response)
        const errorMsg = response?.message || 'Failed to fetch Ingress YAML. Response structure or code indicates failure.';
        setYamlContent(errorMsg);
        message.error(errorMsg);
        console.error('Failed to fetch Ingress YAML: Response was:', response);
      }
    } catch (error) {
      console.error('Failed to fetch Ingress YAML:', error);
      const errorMsg = 'Failed to fetch Ingress YAML due to an error.';
      setYamlContent(errorMsg);
      message.error(errorMsg);
    } finally {
      setYamlLoading(false);
    }
  };

  // 关闭详情弹窗
  const handleCloseDetails = () => {
    setDetailVisible(false);
    setCurrentIngress(null);
  };

  // 处理集群变化
  const handleClusterChange = (value: string) => {
    setSelectedCluster(value);
    setStoredValue(STORAGE_KEYS.CLUSTER, value);
    setSelectedNamespace('');
    setStoredValue(STORAGE_KEYS.NAMESPACE, '');
    setSearchText('');
    setStoredValue(STORAGE_KEYS.SEARCH_TEXT, '');
    setIsInitialLoad(false); // 用户手动切换集群，不再是初始加载
  };

  // 处理命名空间变化
  const handleNamespaceChange = (value: string) => {
    setSelectedNamespace(value);
    setStoredValue(STORAGE_KEYS.NAMESPACE, value);
    setSearchText('');
    setStoredValue(STORAGE_KEYS.SEARCH_TEXT, '');
  };

  // 命名空间搜索变化
  const handleNamespaceSearch = (value: string) => {
    setNamespaceSearchText(value);
  };

  // 搜索框变化
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchText(value);
    setStoredValue(STORAGE_KEYS.SEARCH_TEXT, value);
  };

  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 280,
      fixed: 'left' as 'left',
      render: (text: string, record: IngressItem) => (
        <div>
          <div style={{ fontWeight: 500 }}>{text}</div>
          <div style={{ fontSize: '0.85em', color: 'gray' }}>{record.namespace}</div>
        </div>
      ),
    },
    {
      title: 'Host',
      dataIndex: 'host',
      key: 'host',
      width: 300,
      render: (text: string, record: IngressItem) => {
        const mainHost = record.rules && record.rules.length > 0 && record.rules[0].host ? record.rules[0].host : '[Any Host]';
        const isTLS = record.tls?.some(t => t.hosts?.includes(mainHost));
        const tlsSecret = record.tls?.find(t => t.hosts?.includes(mainHost) && t.secretName)?.secretName;
        return (
          <div style={{ 
            padding: '4px 8px', 
            margin: '2px 0px', 
            background: 'rgb(245, 245, 245)', 
            borderRadius: '4px', 
            borderLeft: `3px solid ${isTLS ? '#52c41a' : 'rgb(24, 144, 255)'}` 
          }}>
            <div>
              {mainHost}
            </div>
            {isTLS && 
              <div style={{ marginTop: '3px' }}>
                <Tag color="green" style={{ lineHeight: '16px', height: '20px', fontSize: '12px', padding: '0 4px' }}>HTTPS</Tag>
              </div>
            }
            {tlsSecret &&
              <div style={{ fontSize: '0.85em', color: '#1890ff', marginTop: '3px' }}>
                Secret: {tlsSecret}
              </div>
            }
          </div>
        );
      },
    },
    {
      title: '路径映射',
      dataIndex: 'rules',
      key: 'rules',
      width: 450,
      render: (rules: IngressRule[], record: IngressItem) => (
        <div>
          {rules.map((rule, ruleIndex) => {
            // Get the hostname for this rule block
            const hostname = rule.host || '';
            
            return (
              <div key={`${record.key}-rule-${ruleIndex}`} style={{ marginBottom: '0px' }}>
                {hostname && 
                  <div style={{ 
                    fontSize: '12px', 
                    color: 'rgb(136, 136, 136)', 
                    marginBottom: '4px', 
                    fontWeight: 'bold' 
                  }}>
                    {hostname}:
                  </div>
                }
                
                {rule.http?.paths.map((pathObj, pathIndex) => {
                  // Ensure pathObj.path exists before accessing it
                  const path = pathObj.path || '';
                  
                  // Determine path type and set appropriate color
                  const isRootPath = path === '/';
                  const isWildcardPath = path.includes('*');
                  const isApiPath = path.includes('/api');
                  
                  // Set border color based on path type
                  let borderColor = '#1890ff'; // Default blue
                  let pathColor = '#1890ff';
                  
                  if (isRootPath) {
                    borderColor = '#52c41a'; // Green for root path
                    pathColor = '#52c41a';
                  } else if (isWildcardPath) {
                    borderColor = '#faad14'; // Orange for wildcard paths
                    pathColor = '#faad14';
                  } else if (isApiPath) {
                    borderColor = '#1890ff'; // Blue for API paths
                    pathColor = '#1890ff';
                  }
                  
                  // Format path for display
                  let displayPath = path || '[No Path]';
                  
                  return (
                    <div 
                      key={`${record.key}-path-${ruleIndex}-${pathIndex}`} 
                      style={{ 
                        marginBottom: '8px', 
                        padding: '4px 8px', 
                        background: 'rgb(249, 249, 249)', 
                        borderRadius: '4px',
                        borderLeft: `3px solid ${borderColor}`
                      }}
                    >
                      <div>
                        <span style={{ color: pathColor, fontWeight: 'bold' }}>{displayPath}</span>
                        <span style={{ margin: '0px 4px' }}>→</span>
                        <span style={{ color: '#1890ff' }}>{pathObj.backend.service.name}</span>
                        <span>:{pathObj.backend.service.port.number || pathObj.backend.service.port.name}</span>
                      </div>
                    </div>
                  );
                })}
              </div>
            );
          })}
        </div>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'creationTimestamp',
      key: 'creationTimestamp',
      width: 160,
      render: (text: string) => formatDate(text),
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right' as 'right',
      width: 100,
      align: 'center' as 'center',
      render: (text: any, record: IngressItem) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <EyeOutlined 
              style={{ cursor: 'pointer', fontSize: '16px' }} 
              onClick={() => handleViewDetails(record)} 
            />
          </Tooltip>
          <Tooltip title="刷新">
            <ReloadOutlined 
              style={{ cursor: 'pointer', fontSize: '16px' }} 
              onClick={handleRefresh} 
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer 
      title="Ingress域名管理"
      content={null}
    >
      <Card>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Row gutter={16} justify="space-between" align="middle">
            <Col>
              <Space size="middle" align="center">
                <span>
                  集群 <Tooltip title="系统会记住您的选择"><span style={{ color: '#52c41a', fontSize: '10px' }}>●</span></Tooltip>:
                </span>
                <Select
                  style={{ width: 180 }}
                  placeholder="选择集群"
                  onChange={handleClusterChange}
                  value={selectedCluster}
                >
                  {fixedClusterOptions.map(c => <Option key={c.name} value={c.name}>{c.displayName}</Option>)}
                </Select>
                <span>
                  命名空间 <Tooltip title="系统会记住您的选择"><span style={{ color: '#52c41a', fontSize: '10px' }}>●</span></Tooltip>:
                </span>
                <Select
                  style={{ width: 180 }}
                  placeholder="选择命名空间"
                  onChange={handleNamespaceChange}
                  loading={loadingNamespaces}
                  disabled={!selectedCluster}
                  value={selectedNamespace}
                  allowClear
                  showSearch
                  onSearch={handleNamespaceSearch}
                  filterOption={false}
                  onDropdownVisibleChange={setNamespaceDropdownOpen}
                  open={namespaceDropdownOpen}
                >
                  {getFilteredNamespaces().map(ns => <Option key={ns.id} value={ns.id}>{ns.name}</Option>)}
                </Select>
              </Space>
            </Col>
            <Col>
              <Space size="middle">
                <div>
                  <div style={{ marginBottom: 4, fontSize: '12px', color: '#666' }}>
                    搜索Host <Tooltip title="系统会记住您的搜索内容"><span style={{ color: '#52c41a', fontSize: '10px' }}>●</span></Tooltip>
                  </div>
                  <Search
                    placeholder="搜索Host关键字"
                    onChange={handleSearchChange}
                    style={{ width: 280 }}
                    value={searchText}
                    allowClear
                    prefix={<SearchOutlined />}
                  />
                </div>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={handleRefresh}
                  title="刷新数据"
                />
                {/* 临时调试按钮 */}
                <Button
                  type="default"
                  size="small"
                  onClick={() => {
                    const cluster = localStorage.getItem('ingress-management-selected-cluster');
                    const namespace = localStorage.getItem('ingress-management-selected-namespace');
                    const search = localStorage.getItem('ingress-management-search-text');
                    alert(`Ingress存储状态:\n集群: ${cluster}\n命名空间: ${namespace}\n搜索: ${search}`);
                  }}
                >
                  调试
                </Button>
              </Space>
            </Col>
          </Row>
          
          <Divider style={{ margin: '12px 0' }} />
          
          <Spin spinning={loading || loadingNamespaces}>
            <Table
              columns={columns}
              dataSource={filteredIngresses}
              rowKey="key"
              pagination={{ 
                pageSize: 10, 
                showSizeChanger: true, 
                pageSizeOptions: ['10', '20', '50'], 
                showTotal: total => `总计 ${total} 条记录` 
              }}
              scroll={{ x: 'max-content' }}
              locale={{ emptyText: '' }}
            />
          </Spin>

          {/* YAML 详情 Modal */}
          <Modal
            title={`Ingress YAML: ${currentIngress?.name || 'Loading...'}`}
            open={isYamlModalVisible}
            onOk={() => setIsYamlModalVisible(false)}
            onCancel={() => setIsYamlModalVisible(false)}
            width="50%"
            destroyOnClose // Add this to reset modal state if needed when closed
            footer={[
              <Button key="close" onClick={() => setIsYamlModalVisible(false)}>
                Close
              </Button>,
            ]}
          >
            {yamlLoading ? (
              <div style={{ textAlign: 'center', padding: '50px' }}>
                <Spin size="large" />
              </div>
            ) : (
              <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all', maxHeight: '60vh', overflowY: 'auto', background: '#f5f5f5', border: '1px solid #ccc', padding: '10px', borderRadius: '4px' }}>
                {yamlContent}
              </pre>
            )}
          </Modal>

          {/* Original 详情 Modal - can be removed or repurposed if no longer needed */}
          {/* {currentIngress && detailVisible && (
            <Modal
              title={`详情: ${currentIngress.name}`}
              open={detailVisible}
              onCancel={handleCloseDetailModal} // Ensure handleCloseDetailModal is defined if this is uncommented
              footer={null} // Or add relevant footer buttons
            >
              <p>Namespace: {currentIngress.namespace}</p>
              <p>Host: {currentIngress.host}</p>
              <p>CreationTimestamp: {moment(currentIngress.creationTimestamp).format('YYYY-MM-DD HH:mm:ss')}</p>
              {/* Add more details as needed * /}
            </Modal>
          )} */}
        </Space>
      </Card>
    </PageContainer>
  );
};

export default IngressDomainsPage;
