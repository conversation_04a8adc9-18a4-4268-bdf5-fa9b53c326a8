import React, {PureComponent} from 'react';
import {utils} from "umi";
import {timeFormatToLocal} from "@/utils/time";
import {PageHeaderWrapper} from "@ant-design/pro-layout";
import {Card, Table} from "antd";
import {connect} from "dva";

const HOST_STATUS = {
  0: "停用",
  1: "暂停",
  2: "正常",
};

@connect(({resource, loading}) => ({
  resource,
  loading: loading.effects['resource/listHost'],
}))
class HostList extends PureComponent {

  componentDidMount() {
    const {dispatch} = this.props;
    dispatch({
      type: 'resource/listHost',
      payload: {
        data: {},
      },
    });
  }

  render() {
    const {resource} = this.props;
    const hostList = resource.hostList || [];
    const columns = [
      {
        title: '主机名',
        dataIndex: 'name',
      },
      {
        title: '状态',
        render: item => HOST_STATUS[item.status],
      },
      {
        title: '系统信息',
        dataIndex: 'os',
      },
      {
        title: '添加时间',
        render: item => timeFormatToLocal(item.createdAt),
      },
    ];

    return (
      <PageHeaderWrapper>
        <Card title="主机列表">
          <Table
            rowKey="id"
            dataSource={hostList}
            columns={columns}
            pagination={{
              defaultPageSize: 20,
            }}
          />
        </Card>
      </PageHeaderWrapper>
    );
  }
}

export default HostList;
