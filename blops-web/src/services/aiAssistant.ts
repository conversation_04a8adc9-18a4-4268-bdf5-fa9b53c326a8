import { aiRequest } from '@/utils/request';
import { REQ_METHOD } from '@/utils/request';

// AI聊天请求接口
export interface AIChatRequest {
  message: string;
  sessionId?: string;
}

// AI聊天响应接口
export interface AIChatResponse {
  reply: string;
  sessionId: string;
  timestamp: number;
}

// 聊天消息接口
export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
}

// 聊天历史接口
export interface ChatHistory {
  sessionId: string;
  messages: ChatMessage[];
  createdAt: number;
  updatedAt: number;
}

// 快速问题模板接口
export interface QuickTemplate {
  id: string;
  title: string;
  description: string;
  content: string;
  category: string;
}

// API响应格式
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

/**
 * 发送AI聊天消息
 */
export async function sendChatMessage(data: AIChatRequest): Promise<ApiResponse<AIChatResponse>> {
  return await aiRequest('/api/ai/chat', {
    method: REQ_METHOD.POST,
    data,
  });
}

/**
 * 获取聊天历史
 */
export async function getChatHistory(sessionId: string): Promise<ApiResponse<ChatHistory>> {
  return await aiRequest('/api/ai/chat/history', {
    method: REQ_METHOD.GET,
    params: { sessionId },
  });
}

/**
 * 清空聊天历史
 */
export async function clearChatHistory(sessionId?: string): Promise<ApiResponse<null>> {
  return await aiRequest('/api/ai/chat/history', {
    method: REQ_METHOD.DELETE,
    params: sessionId ? { sessionId } : {},
  });
}

/**
 * 获取快速问题模板
 */
export async function getQuickTemplates(): Promise<ApiResponse<QuickTemplate[]>> {
  return await aiRequest('/api/ai/templates', {
    method: REQ_METHOD.GET,
  });
}

export default {
  sendChatMessage,
  getChatHistory,
  clearChatHistory,
  getQuickTemplates,
};
