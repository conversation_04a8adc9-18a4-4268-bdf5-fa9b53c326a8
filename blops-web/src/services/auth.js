import {request} from "umi";
import {REQ_METHOD} from "@/utils/request";

export async function list(params) {
  return request('/api/v1/auth/_list', {
    params,
  });
}

export async function authTypeList() {
  return request('/api/v1/auth/_typeList');
}

export async function addAuth(data) {
  return request('/api/v1/auth/_create', {
    method: REQ_METHOD.POST,
    data,
  });
}

export async function deleteAuth(id) {
  return request('/api/v1/auth/_delete', {
    method: REQ_METHOD.DELETE,
    params: {
      id,
    },
  });
}

export async function addUserAuth(data) {
  return request('/api/v1/auth/userAuth/_create', {
    method: REQ_METHOD.POST,
    data,
  });
}

export async function listUserAuth(params) {
  return request('/api/v1/auth/userAuth/_list', {
    params,
  });
}

export async function deleteUserAuth(id) {
  return request('/api/v1/auth/userAuth/_delete', {
    method: REQ_METHOD.DELETE,
    params: {
      id,
    },
  });
}

export default {
  list,
  authTypeList,
  addAuth,
  deleteAuth,
  addUserAuth,
  listUserAuth,
  deleteUserAuth,
};
