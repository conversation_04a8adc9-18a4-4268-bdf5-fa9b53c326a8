import request from '@/utils/request';
import { REQ_METHOD } from "@/utils/request";

// 定义通用响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data?: T;
  result?: string;
}

// 定义 CronJob 列表请求参数
export interface CronJobListParams {
  namespace: string;
  cluster?: string;
  page?: number;
  pageSize?: number;
}

// 定义 CronJob 详情请求参数
export interface CronJobDetailParams {
  namespace: string;
  name: string;
  cluster?: string;
}

// 定义 CronJob 创建/更新参数
export interface CronJobCreateUpdateParams {
  namespace: string;
  name: string;
  cluster?: string;
  schedule?: string;
  suspend?: boolean;
  jobTemplate?: any; // 可以根据实际需求定义更详细的类型
  yamlData?: string; // 添加 yamlData 字段
}

// 定义 CronJob 删除参数
export interface CronJobDeleteParams {
  namespace: string;
  name: string;
  cluster?: string;
}

// 列出命名空间中的CronJob
export async function list_cronjobs(data: CronJobListParams): Promise<ApiResponse> {
  return await request('/api/v1/cronjob/list', {
    method: REQ_METHOD.POST,
    data,
  });
}

// 获取CronJob详情
export async function get_cronjob(data: CronJobDetailParams): Promise<ApiResponse> {
  return await request('/api/v1/cronjob/get', {
    method: REQ_METHOD.POST,
    data,
  });
}

// 创建CronJob
export async function create_cronjob(data: CronJobCreateUpdateParams): Promise<ApiResponse> {
  return await request('/api/v1/cronjob/create', {
    method: REQ_METHOD.POST,
    data,
  });
}

// 更新CronJob
export async function update_cronjob(data: CronJobCreateUpdateParams): Promise<ApiResponse> {
  return await request('/api/v1/cronjob/update', {
    method: REQ_METHOD.POST,
    data,
  });
}

// 删除CronJob
export async function delete_cronjob(data: CronJobDeleteParams): Promise<ApiResponse> {
  return await request('/api/v1/cronjob/delete', {
    method: REQ_METHOD.POST,
    data,
  });
}

// 获取CronJob创建的Jobs
export async function get_cronjob_jobs(data: {
  cluster?: string;
  namespace: string;
  name: string;
}): Promise<ApiResponse> {
  return await request('/api/v1/cronjob/jobs', {
    method: REQ_METHOD.POST,
    data,
  });
}

// 获取Pod日志
export async function get_pod_logs(data: {
  cluster?: string;
  namespace: string;
  podName: string;
  containerName?: string;
}): Promise<ApiResponse> {
  return await request('/api/v1/pod/logs', {
    method: REQ_METHOD.POST,
    data,
  });
}

// 立即触发 CronJob 执行
export async function trigger_cronjob(data: {
  cluster?: string;
  namespace: string;
  name: string;
}): Promise<ApiResponse> {
  return await request('/api/v1/cronjob/trigger', {
    method: REQ_METHOD.POST,
    data,
  });
}

// 删除与CronJob相关的所有Job
export async function delete_cronjob_jobs(data: {
  cluster?: string;
  namespace: string;
  cronjobName: string;
}): Promise<ApiResponse> {
  return await request('/api/v1/cronjob/delete-jobs', {
    method: REQ_METHOD.POST,
    data,
  });
} 