import {request} from 'umi';
import {REQ_METHOD} from "@/utils/request";

export async function list(params) {
  return request('/api/v1/res_auth/_list', {
    params,
  });
}

export async function listUserResAuthsBycode(params) {
  return request('/api/v1/res_auth/_listuserres', {
    params,
  });
}

export async function listResourcesBycode(params) {
  return request('/api/v1/res_auth/_listres', {
    params,
  });
}

export async function editAndUpdateUserResAuths(data) {
  return request('/api/v1/res_auth/_update', {
    method: REQ_METHOD.PUT,
    data,
  });
}

export async function checkUserResAuth(params) {
  return request('/api/v1/res_auth/_check', {
    params,
  });
}

export async function getUsersByResCode(params) {
  return request('/api/v1/res_auth/_users', {
    params,
  });
}

export async function addAdminUserAuth(params) {
  return request('/api/v1/res_auth/_admin', {
    method: REQ_METHOD.POST,
    params,
  });
}

export async function delUserResAuth(params) {
  return request('/api/v1/res_auth/_delete', {
    method: REQ_METHOD.DELETE,
    params,
  });
}

export default {
  list,
  listUserResAuthsBycode,
  listResourcesBycode,
  editAndUpdateUserResAuths,
  checkUserResAuth,
  getUsersByResCode,
  addAdminUserAuth,
  delUserResAuth,
};
