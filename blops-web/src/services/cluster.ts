import request from '@/utils/request';
import { REQ_METHOD } from "@/utils/request";

// 列出所有可用的集群
export async function list_clusters(data: any = {}) {
  return await request('/api/v1/cluster/list', {
    method: REQ_METHOD.POST,
    data,
  });
}

// 列出集群中的命名空间
export async function list_namespaces(data: any) {
  return await request('/api/v1/cluster/namespace/list', {
    method: REQ_METHOD.POST,
    data,
  });
}

// 列出命名空间中的Deployment
export async function list_deployments(data: any) {
  return await request('/api/v1/cluster/deployment/list', {
    method: REQ_METHOD.POST,
    data,
  });
}

// 列出命名空间中的Service
export async function list_services(data: any) {
  return await request('/api/v1/cluster/service/list', {
    method: REQ_METHOD.POST,
    data,
  });
}

// 列出命名空间中的Ingress
export async function list_ingresses(data: any) {
  return await request('/api/v1/cluster/ingress/list', {
    method: REQ_METHOD.POST,
    data,
  });
}

// 获取资源详情
export async function get_resource(data: any) {
  return await request('/api/v1/cluster/resource/get', {
    method: REQ_METHOD.POST,
    data,
  });
}

// 创建资源
export async function create_resource(data: any) {
  return await request('/api/v1/cluster/resource/create', {
    method: REQ_METHOD.POST,
    data,
  });
}

// 更新资源
export async function update_resource(data: any) {
  return await request('/api/v1/cluster/resource/update', {
    method: REQ_METHOD.POST,
    data,
  });
}

// 删除资源
export async function delete_resource(data: any) {
  return await request('/api/v1/cluster/resource/delete', {
    method: REQ_METHOD.POST,
    data,
  });
}

// 列出指定 Deployment 下的所有 Pod
export async function list_deployment_pods(data: any) {
  return await request('/api/v1/cluster/deployment/pods', {
    method: REQ_METHOD.POST,
    data,
  });
}

export default {
  list_clusters,
  list_namespaces,
  list_deployments,
  list_services,
  list_ingresses,
  get_resource,
  create_resource,
  update_resource,
  delete_resource,
  list_deployment_pods,
  getPodList(data: any) {
    return request('/api/v1/cluster/pods', {
      method: 'GET',
      params: data,
    });
  },
}; 