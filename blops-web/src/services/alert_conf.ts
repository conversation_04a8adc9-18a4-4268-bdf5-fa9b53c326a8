import {request} from 'umi';
import {REQ_METHOD} from "@/utils/request";



export async function list_conf(data: any) {
  return await request('/api/v1/alert/conf/_list', {
    method: REQ_METHOD.POST,
    data,
  });
}

export async function add_conf(data: any) {
  return await request('/api/v1/alert/conf', {
    method: REQ_METHOD.POST,
    data,
  });
}

export async function update_conf(data: any) {
  return await request('/api/v1/alert/conf/_update', {
    method: REQ_METHOD.POST,
    data,
  });
}


export async function get_tree(data: any) {
  return await request('/api/v1/alert/tree', {
    method: REQ_METHOD.GET,
    data,
  });
}

export async function add_tmpl(data: any) {
  return await request('/api/v1/alert/template', {
    method: REQ_METHOD.POST,
    data,
  });
}

export async function update_tmpl(data: any) {
  return await request('/api/v1/alert/template/_update', {
    method: REQ_METHOD.POST,
    data,
  });
}

export async function list_tmpl(data: any) {
  return await request('/api/v1/alert/template/_list', {
    method: REQ_METHOD.POST,
    data,
  });
}

export default {
  list_conf,
  add_conf,
  update_conf,
  add_tmpl,
  update_tmpl,
  get_tree,
  list_tmpl,
};
