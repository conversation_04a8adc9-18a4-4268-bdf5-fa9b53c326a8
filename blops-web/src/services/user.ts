import {request} from 'umi';
import {REQ_METHOD} from "@/utils/request";

export async function login(data: any) {
  return await request('/api/v1/user/_login', {
    method: REQ_METHOD.POST,
    data,
  });
}

export async function profile() {
  return await request('/api/v1/user/_profile');
}

export async function listUser() {
  return await request('/api/v1/user/_list');
}

export default {
  login,
  profile,
  listUser,
};
