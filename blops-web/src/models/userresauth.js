/* eslint-disable @typescript-eslint/no-unused-vars */
import service from '@/services/userresauth';
import { callReducer } from '@/utils/reducer';

const Model = {
  namespace: 'userresauth',

  state: {
    // userResAuthList: [],
    userCodeRessAuthList: [],
    allCodeRessList: [],
    usersByCodeList: [],
  },

  effects: {
    // *listUserResAuth({ payload }, { put, call }) {
    //   const response = yield call(service.list, payload);
    //   if (!response.data) return;
    //   yield callReducer('listUserResAuthReducer', response.data, { put });
    // },
    *listUserRessAuthByCode({ payload, callback }, { put, call }) {
      const response = yield call(service.listUserResAuthsBycode, payload);
      if (!response.data) return;
      yield callReducer('listUserResesAuthByCodeReducer', response.data, { put });
      callback(response);
    },
    *listAllRessByCode({ payload, callback }, { put, call }) {
      const response = yield call(service.listResourcesBycode, payload);
      if (!response.data) return;
      yield callReducer('listAllResesByCodeReducer', response.data, { put });
      callback(response);
    },
    *editAndUpdateUserResAuths({ payload }, { put, call }) {
      const response = yield call(service.editAndUpdateUserResAuths, payload);
      if (!response.data) return;
      yield callReducer('editAndUpdateUserResAuthsReducer', response.data, { put });
    },
    *checkUserResAuth({ payload, callback }, { put, call }) {
      const response = yield call(service.checkUserResAuth, payload);
      callback(response);
      if (!response.data) return;
      yield callReducer('checkUserResAuthReducer', response.data, { put });
    },
    *listUsersByResCode({ payload }, { put, call }) {
      const response = yield call(service.getUsersByResCode, payload);
      if (!response.data) return;
      // callback(response);
      yield callReducer('listUsersByResCodeReducer', response.data, { put });
    },
    *addAdminUserAuth({ payload, callback }, { put, call }) {
      const response = yield call(service.addAdminUserAuth, payload);
      if (!response.data) return;
      callback(response);
      yield callReducer('addAdminUserAuthReducer', response.data, { put });
    },
    *delUserResAuth({ payload }, { put, call }) {
      const response = yield call(service.delUserResAuth, payload);
      if (!response.data) return;
      yield callReducer('delUserResAuthReducer', response.data, { put });
    },
  },

  reducers: {
    // listUserResAuthReducer(state, action) {
    //   return {
    //     ...state,
    //     userResAuthList: action.payload || [],
    //   };
    // },
    listUserResesAuthByCodeReducer(state, action) {
      return {
        ...state,
        userCodeRessAuthList: [...action.payload] || [],
      };
    },
    listAllResesByCodeReducer(state, action) {
      return {
        ...state,
        allCodeRessList: [...action.payload] || [],
      };
    },
    editAndUpdateUserResAuthsReducer(state, action) {
      return {
        ...state,
        userCodeRessAuthList: action.payload || [],
      };
    },
    checkUserResAuthReducer(state, action) {
      return {
        ...state,
      };
    },
    listUsersByResCodeReducer(state, action) {
      return {
        ...state,
        usersByCodeList: action.payload || [],
      };
    },
    addAdminUserAuthReducer(state, action) {
      return {
        ...state,
        usersByCodeList:
          state.usersByCodeList
            .filter(item => item.id !== action.payload.id)
            .concat(action.payload) || [],
      };
    },
    delUserResAuthReducer(state, action) {
      return {
        ...state,
        usersByCodeList: state.usersByCodeList.filter(item => item.id !== action.payload) || [],
      };
    },
  },
};

export default Model;
