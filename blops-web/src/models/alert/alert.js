import service from '@/services/alert';
import svcconf from '@/services/alert_conf';
import { callReducer } from '@/utils/reducer';

const Model = {
  namespace: 'alert',

  state: {
    alertList: [],
    dashboardAlerts: [],
    confList: [],
    confAdd: [],
    ruleList: [],
    treeList: [],
    exprList: [],
    total: 0,
  },

  effects: {
    *listAlert({ payload }, { put, call }) {
      const response = yield call(service.list_alert, payload);
      if (!response.data) return;
      yield callReducer('listAlertReducer', response, { put });
    },
    *getDashboardAlerts({ payload }, { put, call }) {
      const response = yield call(service.list_alert, payload);
      if (!response.data) return;
      yield callReducer('getDashboardAlertsReducer', response, { put });
    },
    *listConf({ payload }, { put, call }) {
      const response = yield call(svcconf.list_conf, payload);
      if (!response.data) return;
      yield callReducer('listConfReducer', response, { put });      yield callReducer('listConfReducer', response, { put });

    },
    *addConf({ payload, callback }, { put, call }) {
      const response = yield call(svcconf.add_conf, payload);
      if (!response.data) return;
      callback(response);
      yield callReducer('addConfReducer', response, { put });
    },
    *updateConf({ payload, callback }, { put, call }) {
      const response = yield call(svcconf.update_conf, payload);
      if (!response.data) return;
      callback(response);
      yield callReducer('updateConfReducer', response, { put });
    },

    *addTmpl({ payload }, { put, call }) {
      const response = yield call(svcconf.add_tmpl, payload);
      if (!response.data) return;
      yield callReducer('addTmplReducer', response, { put });
    },
    *updateTmpl({ payload, callback }, { put, call }) {
      const response = yield call(svcconf.update_tmpl, payload);
      if (!response.data) return;
      callback(response);
      yield callReducer('updateTmplReducer', response, { put });
    },
    *listTmpl({ payload }, { put, call }) {
      const response = yield call(svcconf.list_tmpl, payload);
      if (!response.data) return;
      yield callReducer('listTmplReducer', response, { put });
    },
    *getTree({ payload }, { put, call }) {
      const response = yield call(svcconf.get_tree, payload);
      if (!response.data) return;
      yield callReducer('getTreeReducer', response, { put });
    },
  },

  reducers: {
    listAlertReducer(state, action) {
      return {
        ...state,
        alertList: action.payload.data || [],
        total: action.payload.total
      };
    },
    getDashboardAlertsReducer(state, action) {
      return {
        ...state,
        dashboardAlerts: action.payload.data || [],
      };
    },
    listConfReducer(state, action) {
      return {
        ...state,
        confList: action.payload.data || [],
      };
    },
    addConfReducer(state, action) {
      return {
        ...state,
        confAdd: action.payload.data || [],
      };
    },
    updateConfReducer(state, action) {
      return {
        ...state,
        confAdd: action.payload.data || [],
      };
    },
    addTmplReducer(state, action) {
      return {
        ...state,
      };
    },
    updateTmplReducer(state, action) {
      return {
        ...state,
      };
    },
    getTreeReducer(state, action) {
      return {
        ...state,
        treeList: action.payload.data || [],
      };
    },
    listTmplReducer(state, action) {
      return {
        ...state,
        exprList: action.payload.data || [],
      };
    },
  },
};

export default Model;
