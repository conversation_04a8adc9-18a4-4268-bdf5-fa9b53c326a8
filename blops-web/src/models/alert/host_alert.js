import service from '@/services/host_alert';
import { callReducer } from '@/utils/reducer';

const Model = {
  namespace: 'host_alert',

  state: {
    alertList: [],
  },

  effects: {
    *hostAlert({ payload }, { put, call }) {
      const response = yield call(service.host_alert, payload);
      if (!response.data) return;
      yield callReducer('hostAlertReducer', response.data, { put });
    },
  },

  reducers: {
    hostAlertReducer(state, action) {
      return {
        ...state,
        alertList: action.payload || [],
      };
    },
  },
};

export default Model;
