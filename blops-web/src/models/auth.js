import service from '@/services/auth';
import { callReducer } from '@/utils/reducer';

const Model = {
  namespace: 'auth',

  state: {
    authDetail: {},
    authTypeList: [],
    authList: [],
    userAuthList: [],
  },

  effects: {
    *listAuth({ payload }, { put, call }) {
      const response = yield call(service.list, payload);
      if (!response.data) return;
      yield callReducer('listAuthReducer', response.data, { put });
    },
    *typeList({ payload }, { put, call }) {
      const response = yield call(service.authTypeList, payload);
      if (!response.data) return;
      yield callReducer('listAuthTypesReducer', response.data, { put });
    },
    *addAuth({ payload, callback }, { put, call }) {
      const response = yield call(service.addAuth, payload);
      callback(response);
      if (!response.data) return;
      yield callReducer('addAuthReducer', response.data, { put });
    },
    *deleteAuth({ payload }, { put, call }) {
      const response = yield call(service.deleteAuth, payload);
      if (!response.data) return;
      yield callReducer('deleteAuthReducer', response.data, { put });
    },
    *addUserAuth({ payload }, { put, call }) {
      const response = yield call(service.addUserAuth, payload);
      yield callReducer('addUserAuthReducer', response, { put });
    },

    *listUserAuth({ payload }, { put, call }) {
      const response = yield call(service.listUserAuth, payload);
      yield callReducer('listUserAuthReducer', response, { put });
    },

    *deleteUserAuth({ payload }, { put, call }) {
      const response = yield call(service.deleteUserAuth, payload);
      yield callReducer('deleteUserAuthReducer', response, { put });
    },
  },

  reducers: {
    listAuthTypesReducer(state, action) {
      return {
        ...state,
        authTypeList: action.payload || [],
      };
    },
    listAuthReducer(state, action) {
      return {
        ...state,
        authList: action.payload || [],
      };
    },
    addAuthReducer(state, action) {
      return {
        ...state,
        authDetail: { ...action.payload } || {},
      };
    },
    deleteAuthReducer(state, action) {
      return {
        ...state,
        authList: state.authList.filter(item => item.id !== action.payload),
      };
    },
    addUserAuthReducer(state, action) {
      return {
        ...state,
        userAuthList: state.userAuthList
          .filter(userAuth => userAuth.id !== action.payload.id)
          .concat(action.payload),
      };
    },
    listUserAuthReducer(state, action) {
      return {
        ...state,
        userAuthList: action.payload || [],
      };
    },
    deleteUserAuthReducer(state, action) {
      return {
        ...state,
        userAuthList: state.userAuthList.filter(userAuth => userAuth.id !== action.payload),
      };
    },
  },
};

export default Model;
