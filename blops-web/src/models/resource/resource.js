import service from '@/services/resource';
import { callReducer } from '@/utils/reducer';

const Model = {
  namespace: 'resource',

  state: {
    hostList: [],
  },

  effects: {
    *listHost({ payload }, { put, call }) {
      const response = yield call(service.list_host, payload);
      if (!response.data) return;
      yield callReducer('listHostReducer', response.data, { put });
    },
  },

  reducers: {
    listHostReducer(state, action) {
      return {
        ...state,
        hostList: action.payload || [],
      };
    },
  },
};

export default Model;
