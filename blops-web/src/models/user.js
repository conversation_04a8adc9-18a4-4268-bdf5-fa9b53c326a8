import service from '@/services/user';
import localStorage from '@/utils/localStorage';
import { callReducer, pushUrl } from '@/utils/reducer';

const UserModel = {
  namespace: 'user',

  state: {
    currentUser: {
      id: localStorage.get('id'),
      username: localStorage.get('username'),
      displayName: localStorage.get('displayName'),
    },
    userList: [],
  },

  effects: {
    *login({ payload, callback }, { call, put }) {
      const response = yield call(service.login, payload.data);
      if (!response.data) {
        return;
      }
      callback(response);
      localStorage.set('id', response.data.id);
      localStorage.set('token', response.data.token);
      localStorage.set('username', response.data.username);
      localStorage.set('displayName', response.data.displayName);
      yield callReducer('loginReducer', response, { put }, pushUrl('/', { put }));
    },
    *profile(_, { call, put }) {
      const response = yield call(service.profile);
      yield callReducer('profileReducer', response, { put });
    },
    *listUser(payload, { call, put }) {
      const response = yield call(service.listUser, payload);
      yield callReducer('listUserReducer', response, { put });
    },
  },

  reducers: {
    loginReducer(state, action) {
      return {
        ...state,
        currentUser: action.payload.data || {},
      };
    },
    profileReducer(state, action) {
      return {
        ...state,
        currentUser: {
          ...state.currentUser,
          ...action.payload.data,
        },
      };
    },
    listUserReducer(state, action) {
      return {
        ...state,
        userList: action.payload.data,
      };
    },
  },
};

export default UserModel;
