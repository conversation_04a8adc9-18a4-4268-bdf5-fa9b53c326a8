import { useState, useCallback } from 'react';
import aiAssistantService, { ChatMessage, QuickTemplate } from '@/services/aiAssistant';
import { message } from 'antd';

export interface AIAssistantState {
  // 聊天相关状态
  messages: ChatMessage[];
  sessionId: string;
  loading: boolean;
  
  // UI状态
  visible: boolean;
  templatesVisible: boolean;
  
  // 模板数据
  templates: QuickTemplate[];
  templatesLoaded: boolean;
}

export default function useAIAssistant() {
  const [state, setState] = useState<AIAssistantState>({
    messages: [],
    sessionId: `session_${Date.now()}`,
    loading: false,
    visible: false,
    templatesVisible: false,
    templates: [],
    templatesLoaded: false,
  });

  // 更新状态的辅助函数
  const updateState = useCallback((updates: Partial<AIAssistantState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  // 发送消息
  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim()) {
      message.warning('请输入消息内容');
      return;
    }

    const userMessage: ChatMessage = {
      role: 'user',
      content: content.trim(),
      timestamp: Date.now(),
    };

    // 添加用户消息
    updateState({
      messages: [...state.messages, userMessage],
      loading: true,
    });

    try {
      const response = await aiAssistantService.sendChatMessage({
        message: content.trim(),
        sessionId: state.sessionId,
      });

      if (response.code === 200) {
        const assistantMessage: ChatMessage = {
          role: 'assistant',
          content: response.data.reply,
          timestamp: response.data.timestamp * 1000,
        };

        updateState({
          messages: [...state.messages, userMessage, assistantMessage],
          loading: false,
        });
      } else {
        message.error(response.message || 'AI回复失败');
        updateState({ loading: false });
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      message.error('发送消息失败，请重试');
      updateState({ loading: false });
    }
  }, [state.messages, state.sessionId, updateState]);

  // 清空聊天历史
  const clearHistory = useCallback(async () => {
    try {
      await aiAssistantService.clearChatHistory(state.sessionId);
      updateState({ messages: [] });
      message.success('聊天记录已清空');
    } catch (error) {
      console.error('清空聊天记录失败:', error);
      message.error('清空聊天记录失败');
    }
  }, [state.sessionId, updateState]);

  // 加载快速模板
  const loadTemplates = useCallback(async () => {
    if (state.templatesLoaded) return;

    try {
      const response = await aiAssistantService.getQuickTemplates();
      if (response.code === 200) {
        updateState({
          templates: response.data,
          templatesLoaded: true,
        });
      }
    } catch (error) {
      console.error('加载快速模板失败:', error);
    }
  }, [state.templatesLoaded, updateState]);

  // 打开助手
  const openAssistant = useCallback(() => {
    updateState({ visible: true });
    if (!state.templatesLoaded) {
      loadTemplates();
    }
  }, [state.templatesLoaded, loadTemplates, updateState]);

  // 关闭助手
  const closeAssistant = useCallback(() => {
    updateState({ visible: false });
  }, [updateState]);

  // 显示模板弹窗
  const showTemplates = useCallback(() => {
    updateState({ templatesVisible: true });
  }, [updateState]);

  // 隐藏模板弹窗
  const hideTemplates = useCallback(() => {
    updateState({ templatesVisible: false });
  }, [updateState]);

  // 使用模板
  const useTemplate = useCallback((template: QuickTemplate) => {
    updateState({ templatesVisible: false });
    return template.content;
  }, [updateState]);

  return {
    // 状态
    ...state,
    
    // 操作方法
    sendMessage,
    clearHistory,
    loadTemplates,
    openAssistant,
    closeAssistant,
    showTemplates,
    hideTemplates,
    useTemplate,
    updateState,
  };
}
