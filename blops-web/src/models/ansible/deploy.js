import service from '@/services/ansible';
import { callReducer } from '@/utils/reducer';

const Model = {
  namespace: 'ansible',

  state: {
   
  },

  effects: {
    *deploy({ payload }, { put, call }) {
      const response = yield call(service.ansible_deploy, payload);
      if (!response.data) return;
      yield callReducer('deployReducer', response.data, { put });
    },
  },

  reducers: {
    deployReducer(state, action) {
      return {
        ...state,
      };
    },
  },
};

export default Model;
