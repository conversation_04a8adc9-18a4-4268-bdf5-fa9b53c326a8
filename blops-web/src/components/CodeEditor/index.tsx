import React from 'react';
import { Input } from 'antd';

const { TextArea } = Input;

interface CodeEditorProps {
  value: string;
  language: string;
  height?: string;
  readOnly?: boolean;
  onChange?: (value: string) => void;
}

const CodeEditor: React.FC<CodeEditorProps> = ({
  value,
  language,
  height = '400px',
  readOnly = false,
  onChange,
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (onChange) {
      onChange(e.target.value);
    }
  };

  return (
    <TextArea
      value={value}
      onChange={handleChange}
      style={{ 
        height, 
        fontFamily: 'monospace',
        fontSize: '14px',
        lineHeight: '1.5'
      }}
      readOnly={readOnly}
    />
  );
};

export default CodeEditor; 