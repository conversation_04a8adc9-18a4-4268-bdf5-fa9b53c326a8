/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-console */
import React, { Component } from 'react';
import { Controlled as CodeMirror } from 'react-codemirror2';
import styles from './index.css';
import 'codemirror/lib/codemirror.css';
import 'codemirror/theme/material.css';

require('codemirror/mode/sql/sql');

// eslint-disable-next-line react/prefer-stateless-function
export default class CommonEditor extends Component {
  state = {
    newValue: '',
    hadChange: false,
  };

  componentDidMount = () => {
    this.props.onRef(this);
  };

  editorDidMount = editor => {
    editor.setSize('100%', '100%');
    this.instance = editor;
  };

  onBeforeChange = key => (editor, data, value) => {
    const { readonly } = this.props;
    if (!readonly) {
      this.setState({
        newValue: value,
        hadChange: true,
      });
    }
  };

  onChange = key => (editor, data, value) => {
    if (this.props.onEditorChange) {
      this.props.onEditorChange(key)(value);
    }
    // this.setState({ newValue: value });
  };

  onReset = () => {
    this.setState({
      newValue: '',
    });
  };

  render() {
    const { defaultValue = '', uniqueKey, readonly } = this.props;
    // this.state.defaultValue = defaultValue;
    // console.log("defaultValue", defaultValue);

    let value = defaultValue;
    if (!readonly && this.state.hadChange) {
      value = this.state.newValue;
    }

    return (
      <CodeMirror
        className={styles.CodeMirror}
        value={value}
        editorDidMount={this.editorDidMount}
        scroll={{
          x: 50,
          y: 50,
        }}
        options={{
          mode: 'sql',
          theme: 'material',
          lineWrapping: true,
          readOnly: this.props.readonly === undefined ? true : this.props.readonly,
          cursorHeight: 1,
          singleCursorHeightPerLine: false,
          lineNumbers: true,
        }}
        onBeforeChange={this.onBeforeChange(uniqueKey)}
        onChange={this.onChange(uniqueKey)}
      />
    );
  }
}
