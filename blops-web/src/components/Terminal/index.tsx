import React, { useEffect, useRef } from 'react';
// @ts-ignore
import { Terminal } from 'xterm';
// @ts-ignore
import { FitAddon } from 'xterm-addon-fit';
import 'xterm/css/xterm.css';
import styles from './index.less';

interface TerminalProps {
  cluster: string;
  namespace: string;
  pod: string;
  container?: string;
}

const PodTerminal: React.FC<TerminalProps> = ({ cluster, namespace, pod, container }) => {
  const terminalRef = useRef<HTMLDivElement>(null);
  const terminalInstance = useRef<Terminal | null>(null);
  const socketRef = useRef<WebSocket | null>(null);

  useEffect(() => {
    // 初始化终端
    const term = new Terminal({
      cursorBlink: true,
      theme: {
        background: '#1e1e1e',
        foreground: '#f0f0f0',
      },
      fontSize: 14,
      fontFamily: 'Menlo, Monaco, "Courier New", monospace',
    });

    const fitAddon = new FitAddon();
    term.loadAddon(fitAddon);

    if (terminalRef.current) {
      term.open(terminalRef.current);
      fitAddon.fit();
      terminalInstance.current = term;

      // 连接 WebSocket - 使用固定的localhost:3000地址
      // const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      // const host = window.location.host;
      const wsUrl = `ws://***********:3000/v1/pod/exec?cluster=${cluster}&namespace=${namespace}&pod=${pod}${container ? `&container=${container}` : ''}`;
      
      console.log('Connecting to WebSocket URL:', wsUrl);
    //   term.writeln(`正在连接到 Pod 终端... (${wsUrl})`);
      
      try {
        const socket = new WebSocket(wsUrl);
        socketRef.current = socket;

        socket.onopen = () => {
          console.log('WebSocket connection established');
          term.writeln('连接到 Pod 终端成功！');
        };

        socket.onmessage = (event) => {
          term.write(event.data);
        };

        socket.onclose = (event) => {
          console.log('WebSocket connection closed:', event);
          term.writeln('\r\n连接已关闭');
          term.writeln(`关闭代码: ${event.code}, 原因: ${event.reason || '未知'}`);
        };

        socket.onerror = (error) => {
          console.error('WebSocket 错误:', error);
          term.writeln('\r\n连接错误，请检查网络或服务器状态');
          term.writeln('请检查后端服务是否正常运行，以及 WebSocket 端点是否可访问');
        };

        // 监听终端输入并发送到 WebSocket
        term.onData((data: string) => {
          if (socket.readyState === WebSocket.OPEN) {
            socket.send(data);
          } else {
            console.warn('Cannot send data, WebSocket is not open. ReadyState:', socket.readyState);
          }
        });

        // 监听终端大小变化
        term.onResize(({ cols, rows }: { cols: number; rows: number }) => {
          if (socket.readyState === WebSocket.OPEN) {
            // 发送终端大小调整消息
            const resizeMessage = new Uint8Array(5);
            resizeMessage[0] = 1; // 表示这是一个大小调整消息
            resizeMessage[1] = cols & 0xFF;
            resizeMessage[2] = (cols >> 8) & 0xFF;
            resizeMessage[3] = rows & 0xFF;
            resizeMessage[4] = (rows >> 8) & 0xFF;
            socket.send(resizeMessage);
          }
        });
      } catch (error) {
        console.error('Error creating WebSocket connection:', error);
        term.writeln(`\r\n创建 WebSocket 连接时出错: ${error}`);
      }

      // 窗口大小变化时调整终端大小
      const handleResize = () => {
        fitAddon.fit();
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
        if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
          socketRef.current.close();
        }
        term.dispose();
      };
    }
  }, [cluster, namespace, pod, container]);

  return <div ref={terminalRef} className={styles.terminal} />;
};

export default PodTerminal; 