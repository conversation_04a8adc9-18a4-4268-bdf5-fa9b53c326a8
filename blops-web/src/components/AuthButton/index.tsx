import { Component } from 'react';
import { Button } from 'antd';
import { connect } from 'dva';
import type {ButtonProps} from "antd";
import type {Dispatch} from "redux";
import type {API} from "@/dtos/typings";

interface AuthBtnProps extends ButtonProps {
  dispatch?: Dispatch<any>,
  /**
   * 资源Id, 非admin时必须
   */
  res_id?: number,
  authcode: string,
  /**
   * 是否开启权限
   */
  disabled_auth?: boolean,
  text?: string,
}

@connect(({ userresauth }: any) => ({
  userresauth,
  // loading: loading.effects['userresauth/checkUserResAuth'],
}))
export default class AuthButton extends Component<AuthBtnProps> {
  onAuthButtonClick = () => {
    console.log('click auth button');
    // 检查用户对资源的操作权限
    const { dispatch = () => {}, onClick, res_id, authcode, disabled_auth = false } = this.props;
    if (disabled_auth) {
      if (onClick) {
        // @ts-ignore
        onClick();
      }
      return;
    }
    dispatch({
      type: 'userresauth/checkUserResAuth',
      payload: {
        id: res_id,
        code: authcode,
      },
      callback: (resp: API.Resp) => {
        if (
          resp.code &&
          (resp.code === 'SUCCESS' || resp.code === 'success') &&
          resp.data === true
        ) {
          if (onClick) {
            // @ts-ignore
            onClick();
          }
        } else {
          console.error('no auth');
        }
      },
    });
  };

  render() {
    const { children, type, className, style, disabled, icon, size, text = '' } = this.props;
    const btnProps = {...this.props};
    if (btnProps['dispatch']) {
      // @ts-ignore
      delete btnProps['dispatch'];
    }
    return (
      <Button
        {...btnProps}
        type={type}
        disabled={!!disabled}
        style={{ ...style }}
        className={className}
        onClick={this.onAuthButtonClick}
        icon={icon}
        size={size}
      >
        {text ? text : children}
      </Button>
    );
  }
}
