import React, { useState, useEffect } from 'react';
import { Card, Table, Tabs, Button, Space, Modal, Spin, Typography, Tag, Tooltip, Popconfirm } from 'antd';
import { get_cronjob_jobs, get_pod_logs, trigger_cronjob } from '@/services/cronjob';
import { formatDate } from '@/utils/date';
import CodeEditor from '@/components/CodeEditor';
import { message } from 'antd';
import { EyeOutlined, EditOutlined, DeleteOutlined, PlusOutlined, ReloadOutlined, ExportOutlined, PlayCircleOutlined } from '@ant-design/icons';

const { TabPane } = Tabs;
const { Text } = Typography;

interface CronJobExecutionsProps {
  cluster?: string;
  namespace: string;
  name: string;
  visible: boolean;
  onClose: () => void;
  initialActiveTab?: string;
}

const CronJobExecutions: React.FC<CronJobExecutionsProps> = ({
  cluster,
  namespace,
  name,
  visible,
  onClose,
  initialActiveTab = 'jobs',
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [jobs, setJobs] = useState<any[]>([]);
  const [selectedJob, setSelectedJob] = useState<any>(null);
  const [selectedPod, setSelectedPod] = useState<any>(null);
  const [podLogs, setPodLogs] = useState<string>('');
  const [logsLoading, setLogsLoading] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>(initialActiveTab);

  const fetchJobs = async () => {
    setLoading(true);
    try {
      const response = await get_cronjob_jobs({
        cluster,
        namespace,
        name,
      });
      if (response.code === 0 || response.code === 200) {
        setJobs(response.data || []);
      } else {
        console.error('获取任务列表失败:', response.message);
      }
    } catch (error) {
      console.error('获取任务列表出错:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchPodLogs = async (podName: string) => {
    setLogsLoading(true);
    try {
      const response = await get_pod_logs({
        cluster,
        namespace,
        podName,
      });
      if (response.code === 0 || response.code === 200) {
        setPodLogs(response.data || '没有日志');
      } else {
        setPodLogs(`获取日志失败: ${response.message}`);
      }
    } catch (error) {
      setPodLogs(`获取日志出错: ${error}`);
    } finally {
      setLogsLoading(false);
    }
  };

  useEffect(() => {
    if (visible) {
      fetchJobs();
      setActiveTab(initialActiveTab);
    }
  }, [visible, namespace, name, cluster, initialActiveTab]);

  const handleViewPod = (pod: any) => {
    setSelectedPod(pod);
    fetchPodLogs(pod.name);
    setActiveTab('logs');
  };

  const handleViewJob = (job: any) => {
    setSelectedJob(job);
    setActiveTab('job-details');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Succeeded':
        return 'success';
      case 'Failed':
        return 'error';
      case 'Active':
        return 'processing';
      default:
        return 'default';
    }
  };

  const handleTriggerCronJob = async () => {
    try {
      setLoading(true);
      const res = await trigger_cronjob({
        cluster,
        namespace,
        name,
      });
      
      if (res.code === 0 || res.code === 200) {
        message.success(`${name} 触发执行成功`);
        await fetchJobs();
        if (jobs.length > 0) {
          setSelectedJob(jobs[0]);
          setActiveTab('job-details');
        }
      } else {
        message.error(res.message || '触发执行失败');
      }
    } catch (error) {
      console.error('触发 CronJob 执行失败:', error);
      message.error('触发执行失败');
    } finally {
      setLoading(false);
    }
  };

  const jobColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '创建时间',
      dataIndex: 'creationTimestamp',
      key: 'creationTimestamp',
      render: (text: string) => formatDate(text),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text: string) => <Tag color={getStatusColor(text)}>{text}</Tag>,
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: any) => (
        <Space size="middle">
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              onClick={() => handleViewJob(record)} 
            />
          </Tooltip>
          {record.pods && record.pods.length > 0 && (
            <Tooltip title="查看日志">
              <Button 
                type="link" 
                onClick={() => handleViewPod(record.pods[0])}
              >
                查看日志
              </Button>
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  const podColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '创建时间',
      dataIndex: 'creationTimestamp',
      key: 'creationTimestamp',
      render: (text: string) => formatDate(text),
    },
    {
      title: '阶段',
      dataIndex: 'phase',
      key: 'phase',
      render: (text: string) => {
        const color = 
          text === 'Running' ? 'green' :
          text === 'Succeeded' ? 'blue' :
          text === 'Failed' ? 'red' : 'default';
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: any) => (
        <Button type="link" onClick={() => handleViewPod(record)}>
          查看日志
        </Button>
      ),
    },
  ];

  return (
    <Modal
      title={`${name} 执行结果`}
      open={visible}
      onCancel={onClose}
      width={1000}
      footer={[
        <Button 
          key="trigger" 
          type="primary" 
          icon={<PlayCircleOutlined />}
          onClick={handleTriggerCronJob} 
          loading={loading}
        >
          立即执行
        </Button>,
        <Button key="refresh" onClick={fetchJobs} loading={loading}>
          刷新
        </Button>,
        <Button key="close" onClick={onClose}>
          关闭
        </Button>,
      ]}
    >
      <Spin spinning={loading}>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="执行记录" key="jobs">
            <Table
              dataSource={jobs}
              columns={jobColumns}
              rowKey="name"
              pagination={false}
              locale={{ emptyText: '没有执行记录' }}
            />
          </TabPane>
          
          {selectedJob && (
            <TabPane tab="任务详情" key="job-details">
              <Card title="任务信息" size="small" style={{ marginBottom: 16 }}>
                <p><Text strong>名称:</Text> {selectedJob.name}</p>
                <p><Text strong>创建时间:</Text> {formatDate(selectedJob.creationTimestamp)}</p>
                <p><Text strong>状态:</Text> <Tag color={getStatusColor(selectedJob.status)}>{selectedJob.status}</Tag></p>
              </Card>
              
              {selectedJob.pods && selectedJob.pods.length > 0 && (
                <Card title="Pod 列表" size="small">
                  <Table
                    dataSource={selectedJob.pods}
                    columns={podColumns}
                    rowKey="name"
                    pagination={false}
                  />
                </Card>
              )}
            </TabPane>
          )}
          
          {selectedPod && (
            <TabPane tab="Pod 日志" key="logs">
              <Card title={`Pod: ${selectedPod.name}`} size="small" style={{ marginBottom: 16 }}>
                <p><Text strong>名称:</Text> {selectedPod.name}</p>
                <p><Text strong>创建时间:</Text> {formatDate(selectedPod.creationTimestamp)}</p>
                <p><Text strong>阶段:</Text> <Tag color={selectedPod.phase === 'Running' ? 'green' : selectedPod.phase === 'Succeeded' ? 'blue' : 'default'}>{selectedPod.phase}</Tag></p>
              </Card>
              
              <Spin spinning={logsLoading}>
                <CodeEditor
                  value={podLogs}
                  language="plaintext"
                  height="400px"
                  readOnly
                />
              </Spin>
            </TabPane>
          )}
        </Tabs>
      </Spin>
    </Modal>
  );
};

export default CronJobExecutions; 