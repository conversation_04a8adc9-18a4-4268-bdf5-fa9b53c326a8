import React, { Component } from 'react';
import { Button, Modal } from 'antd';

export default class FormModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      modalVisible: false,
    };
  }

  componentDidMount() {
    this.props.onRef(this);
  }

  showModal = e => {
    if (e) e.stopPropagation();
    this.setState({
      modalVisible: true,
    });
  };

  hideModal = () => {
    this.setState({
      modalVisible: false,
    });
  };

  render() {
    const { hidden, children, formId, title, width, btnStyle, onCancel, btnLabel, onBtnClick } = this.props;
    const okBtProps = {
      htmlType: 'submit',
      form: formId,
    };
    const btn =
      btnStyle && btnStyle.hidden
        ? null
        : <div>
            <Button
              hidden={!!hidden}
              style={btnStyle || { marginLeft: 20 }}
              type="primary"
              onClick={onBtnClick || this.showModal}
            >
              {btnLabel || title}
            </Button>
            <br />
            <br />
        </div>;
    return (
      <div>
        {!!hidden ? null : btn}
        <Modal
          width={width || '400px'}
          title={title}
          visible={this.state.modalVisible}
          // onOk={this.onOk}
          onCancel={onCancel || this.hideModal}
          destroyOnClose
          okText="确认"
          cancelText="取消"
          okButtonProps={{
            ...okBtProps,
          }}
        >
          {children}
        </Modal>
      </div>
    );
  }
}
