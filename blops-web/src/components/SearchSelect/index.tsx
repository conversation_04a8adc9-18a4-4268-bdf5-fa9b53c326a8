import type {SelectProps} from 'antd';
import {Select} from 'antd';
import {Component} from 'react';
import type {BaseKV} from "@/dtos/typings";
import type {LabeledValue} from "antd/lib/select";

const {Option} = Select;

interface SProps extends SelectProps {
  optionList?: BaseKV[] | string[];
  onSelected?: (v: LabeledValue) => void;
  initValue?: any;
  clear?: boolean;
  disabled?: boolean;
}

class SearchSelect extends Component<SProps> {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  onChange = (value: any, option: any) => {
    if (this.props.onSelected) {
      this.props.onSelected(value);
    }
    const {onChange} = this.props;
    if (onChange) {
      onChange(value?.value, option);
    }
  };

  onBlur = () => {
  };

  onFocus = () => {
    console.log('focus');
  };

  onSearch = (val: any) => {
    if (this.props.onSearch) {
      this.props.onSearch(val);
    }
  };

  render() {
    const {
      placeholder = '',
      children,
      style,
      onPopupScroll,
      clear = false,
      mode,
      allowClear = true,
      initValue,
      labelInValue = true,
      optionList = [],
      disabled = false,
    } = this.props;
    let innerOptions = [];
    if (children) {
      // @ts-ignore
      innerOptions = [...children];
    } else {
      let isObject = false;
      if (optionList.length > 0 && typeof optionList[0] === "object") {
        isObject = true;
      }
      if (isObject) {
        for (let i = 0; i < optionList.length; i++) {
          const one = optionList[i] as BaseKV;
          innerOptions.push(
            <Option
              key={i}
              label={one.value}
              value={one.key}
            >
              {one.value}
            </Option>,
          );
        }
      } else {
        for (let i = 0; i < optionList.length; i++) {
          innerOptions.push(
            <Option
              key={i}
              label={optionList[i]}
              value={optionList[i]}
            >
              {optionList[i]}
            </Option>,
          );
        }
      }
    }
    let value = initValue;
    if (initValue && typeof initValue !== "object") {
      value = {
        label: initValue,
        value: initValue,
      };
    }
    let options = {
      key: 'normal',
      value: undefined,
    };
    if (!clear) {
      options = {
        key: 'clear',
        value,
      };
    }
    return (
      <Select
        {...options}
        disabled={disabled}
        placeholder={placeholder}
        labelInValue={labelInValue}
        showSearch
        style={style}
        mode={mode}
        onChange={this.onChange}
        allowClear={allowClear}
        onPopupScroll={onPopupScroll}
        onSearch={this.onSearch}
        // filterOption={true}
        optionFilterProp="label"
      >
        {innerOptions}
      </Select>
    );
  }
}

export default SearchSelect;
