import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import {
  Button,
  Drawer,
  Input,
  List,
  Typography,
  Spin,
  message,
  Tooltip,
  Dropdown,
  Menu,
  Modal,
  Card,
  Tag,
  Space,
  Empty,
  Avatar,
  Switch,
  Popover,
  Badge,
  Divider,
  ConfigProvider,
} from 'antd';
import {
  RobotOutlined,
  SendOutlined,
  ClearOutlined,
  QuestionCircleOutlined,
  CloseOutlined,
  HistoryOutlined,
  UserOutlined,
  CopyOutlined,
  ReloadOutlined,
  LikeOutlined,
  DislikeOutlined,
  SearchOutlined,
  SettingOutlined,
  SunOutlined,
  MoonOutlined,
  ExpandOutlined,
  CompressOutlined,
  CheckOutlined,
  LoadingOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import aiAssistantService, {
  ChatMessage,
  QuickTemplate,
  AIChatRequest,
} from '@/services/aiAssistant';
import styles from './index.less';

const { TextArea } = Input;
const { Text, Title } = Typography;

// 消息状态枚举
enum MessageStatus {
  SENDING = 'sending',
  SENT = 'sent',
  ERROR = 'error',
}

// 主题类型
type ThemeType = 'light' | 'dark';

// 扩展的消息接口
interface ExtendedChatMessage extends ChatMessage {
  id?: string;
  status?: MessageStatus;
  feedback?: 'like' | 'dislike' | null;
}

interface AIAssistantProps {
  defaultTheme?: ThemeType;
  enableDragResize?: boolean;
  enableVoiceInput?: boolean;
  maxWidth?: number;
  minWidth?: number;
}

const AIAssistant: React.FC<AIAssistantProps> = ({
  defaultTheme = 'light',
  enableDragResize = true,
  maxWidth = 500,
  minWidth = 320,
}) => {
  // 基础状态
  const [visible, setVisible] = useState(false);
  const [messages, setMessages] = useState<ExtendedChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string>('');
  const [templates, setTemplates] = useState<QuickTemplate[]>([]);
  const [templatesVisible, setTemplatesVisible] = useState(false);

  // 新增状态
  const [theme, setTheme] = useState<ThemeType>(defaultTheme);
  const [drawerWidth, setDrawerWidth] = useState(400);
  const [isResizing, setIsResizing] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [isExpanded, setIsExpanded] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<any>(null);
  const drawerRef = useRef<HTMLDivElement>(null);
  const resizeRef = useRef<HTMLDivElement>(null);

  // 生成消息ID
  const generateMessageId = () => `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  // 自动滚动到底部
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);



  // 复制消息内容
  const copyMessage = useCallback(async (content: string) => {
    try {
      await navigator.clipboard.writeText(content.replace(/<[^>]*>/g, ''));
      message.success('已复制到剪贴板');
    } catch (error) {
      message.error('复制失败');
    }
  }, []);

  // 消息反馈
  const handleMessageFeedback = useCallback((messageId: string, feedback: 'like' | 'dislike') => {
    setMessages(prev => prev.map(msg =>
      msg.id === messageId
        ? { ...msg, feedback: msg.feedback === feedback ? null : feedback }
        : msg
    ));
    message.success(feedback === 'like' ? '感谢您的反馈！' : '我们会改进回答质量');
  }, []);

  // 重新生成回复
  const regenerateResponse = useCallback(async (messageIndex: number) => {
    if (messageIndex <= 0) return;

    const userMessage = messages[messageIndex - 1];
    if (userMessage.role !== 'user') return;

    // 移除当前AI回复
    setMessages(prev => prev.slice(0, messageIndex));

    // 重新发送请求
    await sendMessage(userMessage.content, false);
  }, [messages]);

  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  // 初始化会话ID和主题
  useEffect(() => {
    if (!sessionId) {
      setSessionId(`session_${Date.now()}`);
    }

    // 从localStorage加载主题设置
    const savedTheme = localStorage.getItem('ai-assistant-theme') as ThemeType;
    if (savedTheme && savedTheme !== theme) {
      setTheme(savedTheme);
    }
  }, []);

  // 保存主题设置
  useEffect(() => {
    localStorage.setItem('ai-assistant-theme', theme);
  }, [theme]);

  // 加载快速模板
  const loadTemplates = async () => {
    try {
      const response = await aiAssistantService.getQuickTemplates();
      if (response.code === 200) {
        setTemplates(response.data);
      }
    } catch (error) {
      console.error('加载快速模板失败:', error);
    }
  };

  // 发送消息（增强版）
  const sendMessage = async (content: string, clearInput = true) => {
    if (!content.trim()) {
      message.warning('请输入消息内容');
      return;
    }

    const userMessageId = generateMessageId();
    const userMessage: ExtendedChatMessage = {
      id: userMessageId,
      role: 'user',
      content: content.trim(),
      timestamp: Date.now(),
      status: MessageStatus.SENDING,
    };

    // 添加用户消息到界面
    setMessages(prev => [...prev, userMessage]);
    if (clearInput) {
      setInputValue('');
    }
    setLoading(true);

    // 更新消息状态为已发送
    setTimeout(() => {
      setMessages(prev => prev.map(msg =>
        msg.id === userMessageId
          ? { ...msg, status: MessageStatus.SENT }
          : msg
      ));
    }, 500);

    try {
      const request: AIChatRequest = {
        message: content.trim(),
        sessionId,
      };

      const response = await aiAssistantService.sendChatMessage(request);

      if (response.code === 200) {
        const assistantMessageId = generateMessageId();
        const assistantMessage: ExtendedChatMessage = {
          id: assistantMessageId,
          role: 'assistant',
          content: response.data.reply, // 直接设置完整内容，实现即时显示
          timestamp: response.data.timestamp * 1000,
          status: MessageStatus.SENT,
        };

        // 直接添加完整的AI消息，无需打字动画
        setMessages(prev => [...prev, assistantMessage]);
      } else {
        message.error(response.message || 'AI回复失败');
        // 更新用户消息状态为错误
        setMessages(prev => prev.map(msg =>
          msg.id === userMessageId
            ? { ...msg, status: MessageStatus.ERROR }
            : msg
        ));
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      message.error('发送消息失败，请重试');
      // 更新用户消息状态为错误
      setMessages(prev => prev.map(msg =>
        msg.id === userMessageId
          ? { ...msg, status: MessageStatus.ERROR }
          : msg
      ));
    } finally {
      setLoading(false);
    }
  };

  // 清空聊天历史
  const clearHistory = async () => {
    Modal.confirm({
      title: '确认清空',
      content: '确定要清空所有聊天记录吗？此操作不可恢复。',
      onOk: async () => {
        try {
          await aiAssistantService.clearChatHistory(sessionId);
          setMessages([]);
          message.success('聊天记录已清空');
        } catch (error) {
          console.error('清空聊天记录失败:', error);
          message.error('清空聊天记录失败');
        }
      },
    });
  };

  // 使用快速模板
  const useTemplate = (template: QuickTemplate) => {
    setInputValue(template.content);
    setTemplatesVisible(false);
  };

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage(inputValue);
    }
  };

  // 键盘快捷键处理
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + Enter 发送消息
      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter' && visible) {
        e.preventDefault();
        if (inputValue.trim()) {
          sendMessage(inputValue);
        }
      }

      // Escape 关闭助手
      if (e.key === 'Escape' && visible) {
        closeAssistant();
      }

      // Ctrl/Cmd + K 打开助手
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        if (!visible) {
          openAssistant();
        } else {
          inputRef.current?.focus();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [visible, inputValue]);

  // 拖拽调整大小
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!enableDragResize) return;

    setIsResizing(true);
    const startX = e.clientX;
    const startWidth = drawerWidth;

    const handleMouseMove = (e: MouseEvent) => {
      const newWidth = Math.max(minWidth, Math.min(maxWidth, startWidth - (e.clientX - startX)));
      setDrawerWidth(newWidth);
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [enableDragResize, drawerWidth, minWidth, maxWidth]);

  // 打开助手
  const openAssistant = useCallback(() => {
    setVisible(true);
    setUnreadCount(0);
    if (templates.length === 0) {
      loadTemplates();
    }
    // 延迟聚焦输入框
    setTimeout(() => {
      inputRef.current?.focus();
    }, 300);
  }, [templates.length]);

  // 关闭助手
  const closeAssistant = useCallback(() => {
    setVisible(false);
    setSearchValue('');
  }, []);

  // 切换主题
  const toggleTheme = useCallback(() => {
    setTheme(prev => prev === 'light' ? 'dark' : 'light');
  }, []);

  // 切换展开状态
  const toggleExpanded = useCallback(() => {
    setIsExpanded(prev => !prev);
  }, []);

  // 搜索消息
  const searchMessages = useCallback((value: string) => {
    if (!value.trim()) return;
    setSearchValue(value.trim());

    // 找到第一个匹配的消息并滚动到它
    const matchIndex = messages.findIndex(msg =>
      msg.content.toLowerCase().includes(value.toLowerCase())
    );

    if (matchIndex >= 0) {
      const messageElement = document.getElementById(`message-${messages[matchIndex].id}`);
      messageElement?.scrollIntoView({ behavior: 'smooth', block: 'center' });
      // 高亮显示效果
      messageElement?.classList.add(styles.highlightedMessage);
      setTimeout(() => {
        messageElement?.classList.remove(styles.highlightedMessage);
      }, 2000);
    } else {
      message.info('未找到匹配内容');
    }
  }, [messages]);

  // 过滤消息（用于搜索）
  const filteredMessages = useMemo(() => {
    if (!searchValue) return messages;
    return messages.filter(msg =>
      msg.content.toLowerCase().includes(searchValue.toLowerCase())
    );
  }, [messages, searchValue]);

  // 快速模板菜单
  const templateMenu = useMemo(() => {
    // 按类别分组模板
    const groupedTemplates: Record<string, QuickTemplate[]> = {};
    templates.forEach(template => {
      if (!groupedTemplates[template.category]) {
        groupedTemplates[template.category] = [];
      }
      groupedTemplates[template.category].push(template);
    });

    return (
      <Menu style={{ maxHeight: 400, overflowY: 'auto' }}>
        {Object.entries(groupedTemplates).map(([category, items]) => (
          <Menu.ItemGroup key={category} title={category}>
            {items.map(template => (
              <Menu.Item key={template.id} onClick={() => useTemplate(template)}>
                <div>
                  <div style={{ fontWeight: 500 }}>{template.title}</div>
                  <div style={{ fontSize: 12, color: theme === 'dark' ? '#aaa' : '#666' }}>{template.description}</div>
                </div>
              </Menu.Item>
            ))}
          </Menu.ItemGroup>
        ))}
      </Menu>
    );
  }, [templates, theme]);

  // 主题配置
  const themeConfig = useMemo(() => ({
    token: theme === 'dark' ? {
      colorBgContainer: '#1f1f1f',
      colorBgElevated: '#262626',
      colorText: '#ffffff',
      colorTextSecondary: '#bfbfbf',
      colorBorder: '#434343',
      colorPrimary: '#1890ff',
    } : {},
  }), [theme]);

  return (
    <ConfigProvider theme={themeConfig}>
      {/* 悬浮按钮 */}
      <div className={`${styles.floatingButton} ${theme === 'dark' ? styles.darkTheme : ''}`}>
        <Badge count={unreadCount} size="small">
          <Tooltip title={
            <div>
              <div>AI助手</div>
              <div style={{ fontSize: '12px', opacity: 0.8 }}>
                快捷键: Ctrl+K
              </div>
            </div>
          } placement="left">
            <Button
              type="primary"
              shape="circle"
              size="large"
              icon={<RobotOutlined />}
              onClick={openAssistant}
              className={`${styles.assistantButton} ${styles.breathingEffect}`}
            />
          </Tooltip>
        </Badge>
      </div>

      {/* AI助手抽屉 */}
      <Drawer
        title={
          <div className={styles.drawerHeader}>
            <div className={styles.headerLeft}>
              <Space>
                <Avatar
                  icon={<RobotOutlined />}
                  className={styles.aiAvatar}
                  size="small"
                />
                <div>
                  <div className={styles.headerTitle}>AI助手</div>
                  <div className={styles.headerSubtitle}>
                    {loading ? '正在思考...' : `${messages.length} 条消息`}
                  </div>
                </div>
              </Space>
            </div>
            <div className={styles.headerRight}>
              <Space size="small">
                <Tooltip title="搜索消息">
                  <Popover
                    content={
                      <Input.Search
                        placeholder="搜索消息内容"
                        onSearch={searchMessages}
                        style={{ width: 200 }}
                        allowClear
                      />
                    }
                    trigger="click"
                    placement="bottomRight"
                  >
                    <Button
                      type="text"
                      icon={<SearchOutlined />}
                      size="small"
                      className={styles.headerButton}
                    />
                  </Popover>
                </Tooltip>

                <Tooltip title="快速模板">
                  <Button
                    type="text"
                    icon={<QuestionCircleOutlined />}
                    onClick={() => setTemplatesVisible(true)}
                    size="small"
                    className={styles.headerButton}
                  />
                </Tooltip>

                <Tooltip title={`切换到${theme === 'light' ? '深色' : '浅色'}主题`}>
                  <Button
                    type="text"
                    icon={theme === 'light' ? <MoonOutlined /> : <SunOutlined />}
                    onClick={toggleTheme}
                    size="small"
                    className={styles.headerButton}
                  />
                </Tooltip>

                <Tooltip title={isExpanded ? '收起' : '展开'}>
                  <Button
                    type="text"
                    icon={isExpanded ? <CompressOutlined /> : <ExpandOutlined />}
                    onClick={toggleExpanded}
                    size="small"
                    className={styles.headerButton}
                  />
                </Tooltip>

                <Tooltip title="清空聊天">
                  <Button
                    type="text"
                    icon={<ClearOutlined />}
                    onClick={clearHistory}
                    size="small"
                    className={styles.headerButton}
                    danger
                  />
                </Tooltip>
              </Space>
            </div>
          </div>
        }
        placement="right"
        width={isExpanded ? Math.min(800, window.innerWidth * 0.8) : drawerWidth}
        open={visible}
        onClose={closeAssistant}
        className={`${styles.assistantDrawer} ${theme === 'dark' ? styles.darkDrawer : ''}`}
        bodyStyle={{
          padding: 0,
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
          background: theme === 'dark' ? '#1f1f1f' : '#ffffff'
        }}
        maskClosable={false}
        destroyOnClose={false}
      >
        {/* 拖拽调整大小的手柄 */}
        {enableDragResize && !isExpanded && (
          <div
            ref={resizeRef}
            className={`${styles.resizeHandle} ${isResizing ? styles.resizing : ''}`}
            onMouseDown={handleMouseDown}
          />
        )}
        {/* 聊天消息区域 */}
        <div className={`${styles.messagesContainer} ${theme === 'dark' ? styles.darkContainer : ''}`}>
          {messages.length === 0 ? (
            <div className={styles.welcomeMessage}>
              <div className={styles.welcomeCard}>
                <div className={styles.welcomeIcon}>
                  <Avatar
                    icon={<RobotOutlined />}
                    size={64}
                    className={styles.aiAvatarLarge}
                  />
                </div>
                <Title level={4} className={styles.welcomeTitle}>欢迎使用AI助手</Title>
                <Text className={styles.welcomeText}>
                  我是您的Kubernetes运维助手，可以帮您解决集群管理、故障排查、性能优化等问题。
                </Text>
                <div className={styles.welcomeFeatures}>
                  <Card className={styles.featureCard}>
                    <QuestionCircleOutlined className={styles.featureIcon} />
                    <div className={styles.featureTitle}>专业知识</div>
                    <div className={styles.featureDesc}>Kubernetes、容器化、云原生技术专家</div>
                  </Card>
                  <Card className={styles.featureCard}>
                    <HistoryOutlined className={styles.featureIcon} />
                    <div className={styles.featureTitle}>上下文记忆</div>
                    <div className={styles.featureDesc}>支持多轮对话，记住对话历史</div>
                  </Card>
                </div>
                <div className={styles.welcomeShortcuts}>
                  <div className={styles.shortcutTitle}>快捷键</div>
                  <div className={styles.shortcutItem}>
                    <Tag color="blue">Ctrl+K</Tag> 打开/聚焦助手
                  </div>
                  <div className={styles.shortcutItem}>
                    <Tag color="blue">Ctrl+Enter</Tag> 发送消息
                  </div>
                  <div className={styles.shortcutItem}>
                    <Tag color="blue">Esc</Tag> 关闭助手
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <>
              {filteredMessages.map((msg, index) => {
                const isUser = msg.role === 'user';
                const messageId = msg.id || `msg-${index}`;
                const showStatus = isUser && msg.status;

                return (
                  <div
                    id={`message-${messageId}`}
                    key={messageId}
                    className={`${styles.messageItem} ${
                      isUser ? styles.userMessage : styles.assistantMessage
                    } ${searchValue && msg.content.toLowerCase().includes(searchValue.toLowerCase()) ? styles.highlightedMessage : ''}`}
                  >
                    {!isUser && (
                      <Avatar
                        icon={<RobotOutlined />}
                        className={styles.messageAvatar}
                        size="small"
                      />
                    )}

                    <div className={styles.messageContent}>
                      <div
                        className={styles.messageText}
                        dangerouslySetInnerHTML={{
                          __html: isUser ? msg.content.replace(/\n/g, '<br/>') : msg.content,
                        }}
                      />

                      <div className={styles.messageFooter}>
                        <div className={styles.messageTime}>
                          {new Date(msg.timestamp).toLocaleTimeString()}
                          {showStatus && (
                            <span className={styles.messageStatus}>
                              {msg.status === MessageStatus.SENDING && (
                                <LoadingOutlined className={styles.statusIcon} />
                              )}
                              {msg.status === MessageStatus.SENT && (
                                <CheckOutlined className={styles.statusIcon} />
                              )}
                              {msg.status === MessageStatus.ERROR && (
                                <ExclamationCircleOutlined className={styles.statusIcon} />
                              )}
                            </span>
                          )}
                        </div>

                        {!isUser && (
                          <div className={styles.messageActions}>
                            <Tooltip title="复制">
                              <Button
                                type="text"
                                icon={<CopyOutlined />}
                                size="small"
                                onClick={() => copyMessage(msg.content)}
                                className={styles.actionButton}
                              />
                            </Tooltip>

                            <Tooltip title="重新生成">
                              <Button
                                type="text"
                                icon={<ReloadOutlined />}
                                size="small"
                                onClick={() => regenerateResponse(index)}
                                className={styles.actionButton}
                                disabled={loading}
                              />
                            </Tooltip>

                            <Tooltip title="有帮助">
                              <Button
                                type="text"
                                icon={<LikeOutlined />}
                                size="small"
                                onClick={() => handleMessageFeedback(messageId, 'like')}
                                className={`${styles.actionButton} ${msg.feedback === 'like' ? styles.activeAction : ''}`}
                              />
                            </Tooltip>

                            <Tooltip title="没帮助">
                              <Button
                                type="text"
                                icon={<DislikeOutlined />}
                                size="small"
                                onClick={() => handleMessageFeedback(messageId, 'dislike')}
                                className={`${styles.actionButton} ${msg.feedback === 'dislike' ? styles.activeAction : ''}`}
                              />
                            </Tooltip>
                          </div>
                        )}
                      </div>
                    </div>

                    {isUser && (
                      <Avatar
                        icon={<UserOutlined />}
                        className={styles.messageAvatar}
                        size="small"
                      />
                    )}
                  </div>
                );
              })}

              {loading && (
                <div className={styles.loadingMessage}>
                  <div className={styles.typingIndicator}>
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                  <span className={styles.loadingText}>AI正在思考...</span>
                </div>
              )}
            </>
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* 输入区域 */}
        <div className={`${styles.inputContainer} ${theme === 'dark' ? styles.darkInput : ''}`}>
          <div className={styles.inputWrapper}>
            <div className={styles.inputBox}>
              <TextArea
                ref={inputRef}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="输入您的问题... (Ctrl+Enter 发送)"
                autoSize={{ minRows: 1, maxRows: 6 }}
                disabled={loading}
                className={styles.messageInput}
                bordered={false}
              />

              <div className={styles.inputToolbar}>
                <div className={styles.inputLeft}>
                  <Dropdown overlay={templateMenu} trigger={['click']} placement="topLeft">
                    <Tooltip title="快速模板">
                      <Button
                        type="text"
                        icon={<QuestionCircleOutlined />}
                        size="small"
                        className={styles.toolbarButton}
                      />
                    </Tooltip>
                  </Dropdown>

                  <Tooltip title="清空输入">
                    <Button
                      type="text"
                      icon={<ClearOutlined />}
                      size="small"
                      onClick={() => setInputValue('')}
                      disabled={!inputValue.trim()}
                      className={styles.toolbarButton}
                    />
                  </Tooltip>
                </div>

                <div className={styles.inputRight}>
                  <div className={styles.characterCount}>
                    {inputValue.length}/2000
                  </div>

                  <Button
                    type="primary"
                    icon={loading ? <LoadingOutlined /> : <SendOutlined />}
                    onClick={() => sendMessage(inputValue)}
                    loading={loading}
                    disabled={!inputValue.trim() || inputValue.length > 2000}
                    size="small"
                    className={styles.sendButton}
                  >
                    发送
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* 输入提示 */}
          <div className={styles.inputHint}>
            <Text type="secondary" className={styles.hintText}>
              支持 Markdown 格式 •
              <Tag color="blue" className={styles.hintTag}>Ctrl+Enter</Tag> 发送 •
              <Tag color="blue" className={styles.hintTag}>Shift+Enter</Tag> 换行
            </Text>
          </div>
        </div>
      </Drawer>

      {/* 快速模板弹窗 */}
      <Modal
        title={
          <div className={styles.modalHeader}>
            <QuestionCircleOutlined className={styles.modalIcon} />
            <span>快速问题模板</span>
          </div>
        }
        open={templatesVisible}
        onCancel={() => setTemplatesVisible(false)}
        footer={null}
        width={700}
        className={`${styles.templateModal} ${theme === 'dark' ? styles.darkModal : ''}`}
      >
        <div className={styles.templatesContainer}>
          <div className={styles.templateGrid}>
            {templates.map(template => (
              <Card
                key={template.id}
                size="small"
                className={styles.templateCard}
                onClick={() => useTemplate(template)}
                hoverable
                actions={[
                  <Button
                    type="link"
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      useTemplate(template);
                    }}
                  >
                    使用模板
                  </Button>
                ]}
              >
                <div className={styles.templateHeader}>
                  <Text strong className={styles.templateTitle}>{template.title}</Text>
                  <Tag color="processing" className={styles.categoryTag}>{template.category}</Tag>
                </div>
                <Text type="secondary" className={styles.templateDescription}>
                  {template.description}
                </Text>
                <div className={styles.templatePreview}>
                  <Text code className={styles.previewText}>
                    {template.content.length > 100
                      ? `${template.content.substring(0, 100)}...`
                      : template.content
                    }
                  </Text>
                </div>
              </Card>
            ))}
          </div>

          {templates.length === 0 && (
            <Empty
              description="暂无模板"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          )}
        </div>
      </Modal>
    </ConfigProvider>
  );
};

export default AIAssistant;
