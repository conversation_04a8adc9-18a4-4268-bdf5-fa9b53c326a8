// AI助手悬浮按钮
.floatingButton {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;

  .assistant<PERSON><PERSON><PERSON> {
    width: 60px;
    height: 60px;
    font-size: 26px;
    border: none;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      transform: scale(1.05) translateY(-2px);
      box-shadow: 0 12px 40px rgba(102, 126, 234, 0.5);

      &::before {
        opacity: 0.2;
      }
    }

    &:active {
      transform: scale(0.98);
    }

    .anticon {
      position: relative;
      z-index: 1;
      color: white;
    }
  }

  // 呼吸灯效果
  .breathingEffect {
    animation: breathing 3s ease-in-out infinite;
  }

  // 深色主题
  &.darkTheme {
    .assistantButton {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      box-shadow: 0 8px 32px rgba(79, 172, 254, 0.4);

      &:hover {
        box-shadow: 0 12px 40px rgba(79, 172, 254, 0.5);
      }
    }
  }
}

@keyframes breathing {
  0%, 100% {
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4);
  }
  50% {
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.6), 0 0 0 8px rgba(102, 126, 234, 0.1);
  }
}

// 抽屉样式
.assistantDrawer {
  .ant-drawer-header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 24px;
    background: linear-gradient(90deg, #f8fafc 0%, #f1f5f9 100%);
    backdrop-filter: blur(10px);
  }

  .ant-drawer-body {
    padding: 0;
    height: calc(100vh - 55px);
    background: #fafbfc;
  }

  .ant-drawer-content {
    border-radius: 12px 0 0 12px;
    overflow: hidden;
  }

  // 深色主题
  &.darkDrawer {
    .ant-drawer-header {
      background: linear-gradient(90deg, #1a1a1a 0%, #2d2d2d 100%);
      border-bottom: 1px solid #434343;
    }

    .ant-drawer-body {
      background: #1f1f1f;
    }

    .ant-drawer-content {
      background: #1f1f1f;
    }
  }
}

// 拖拽调整大小手柄
.resizeHandle {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  cursor: col-resize;
  background: transparent;
  transition: background-color 0.2s ease;
  z-index: 10;

  &:hover,
  &.resizing {
    background: #1890ff;
  }

  &::before {
    content: '';
    position: absolute;
    left: -2px;
    top: 0;
    bottom: 0;
    width: 8px;
  }
}

// 抽屉头部
.drawerHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .headerLeft {
    display: flex;
    align-items: center;
    flex: 1;

    .headerTitle {
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
      margin: 0;
    }

    .headerSubtitle {
      font-size: 12px;
      color: #6b7280;
      margin: 0;
    }
  }

  .headerRight {
    display: flex;
    align-items: center;

    .headerButton {
      color: #6b7280;
      border: none;
      transition: all 0.2s ease;

      &:hover {
        color: #1890ff;
        background: rgba(24, 144, 255, 0.1);
      }
    }
  }

  .aiAvatar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: 2px solid #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

// 消息容器
.messagesContainer {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: linear-gradient(180deg, #fafbfc 0%, #f8fafc 100%);
  position: relative;

  &.darkContainer {
    background: linear-gradient(180deg, #1f1f1f 0%, #1a1a1a 100%);
  }

  // 自定义滚动条
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    transition: background 0.2s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }
}

// 欢迎消息
.welcomeMessage {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px 20px;

  .welcomeCard {
    text-align: center;
    max-width: 480px;

    .welcomeIcon {
      margin-bottom: 24px;

      .aiAvatarLarge {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: 4px solid #ffffff;
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
      }
    }

    .welcomeTitle {
      color: #1f2937;
      margin-bottom: 12px;
      font-weight: 600;
    }

    .welcomeText {
      color: #6b7280;
      font-size: 14px;
      line-height: 1.6;
      margin-bottom: 32px;
      display: block;
    }

    .welcomeFeatures {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      margin-bottom: 32px;

      .featureCard {
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        padding: 20px 16px;
        text-align: center;
        transition: all 0.3s ease;
        background: #ffffff;

        &:hover {
          border-color: #667eea;
          box-shadow: 0 4px 20px rgba(102, 126, 234, 0.15);
          transform: translateY(-2px);
        }

        .featureIcon {
          font-size: 24px;
          color: #667eea;
          margin-bottom: 12px;
        }

        .featureTitle {
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 8px;
          font-size: 14px;
        }

        .featureDesc {
          color: #6b7280;
          font-size: 12px;
          line-height: 1.4;
        }
      }
    }

    .welcomeShortcuts {
      background: #f8fafc;
      border-radius: 12px;
      padding: 20px;
      border: 1px solid #e5e7eb;

      .shortcutTitle {
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 12px;
        font-size: 14px;
      }

      .shortcutItem {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
        font-size: 13px;
        color: #6b7280;

        &:last-child {
          margin-bottom: 0;
        }

        .ant-tag {
          margin-right: 8px;
          font-size: 11px;
          padding: 2px 6px;
        }
      }
    }
  }
}

// 消息项
.messageItem {
  margin-bottom: 24px;
  display: flex;
  position: relative;
  transition: all 0.3s ease;

  // 高亮消息（搜索结果）
  &.highlightedMessage {
    animation: highlight 2s ease;
  }

  // 用户消息
  &.userMessage {
    justify-content: flex-end;

    .messageContent {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 18px 18px 4px 18px;
      max-width: 80%;
      box-shadow: 0 4px 16px rgba(102, 126, 234, 0.2);

      .messageText {
        a {
          color: #ffffff;
          text-decoration: underline;

          &:hover {
            text-decoration: none;
          }
        }

        code {
          background: rgba(255, 255, 255, 0.2);
          color: #ffffff;
        }

        pre {
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
        }
      }

      .messageFooter {
        .messageTime {
          color: rgba(255, 255, 255, 0.7);
        }

        .messageStatus {
          margin-left: 6px;

          .statusIcon {
            font-size: 12px;
          }
        }
      }
    }

    .messageAvatar {
      margin-left: 12px;
      background: #1f2937;
      border: 2px solid #ffffff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  // AI助手消息
  &.assistantMessage {
    justify-content: flex-start;

    .messageContent {
      background: white;
      color: #1f2937;
      border-radius: 18px 18px 18px 4px;
      border: 1px solid #e5e7eb;
      max-width: 85%;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .messageAvatar {
      margin-right: 12px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: 2px solid #ffffff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

// 高亮动画
@keyframes highlight {
  0%, 100% {
    background: transparent;
  }
  20%, 80% {
    background: rgba(102, 126, 234, 0.1);
  }
}

// 消息内容
.messageContent {
  padding: 16px 20px;
  word-wrap: break-word;
  position: relative;

  .messageText {
    line-height: 1.6;
    font-size: 14px;

    // 移除打字动画效果，保持简洁样式

    // 处理AI回复中的HTML内容
    h1, h2, h3, h4, h5, h6 {
      margin: 12px 0 8px 0;
      font-weight: 600;
      color: inherit;
    }

    h1 { font-size: 18px; }
    h2 { font-size: 16px; }
    h3 { font-size: 15px; }
    h4, h5, h6 { font-size: 14px; }

    p {
      margin: 8px 0;
      line-height: 1.6;
    }

    ul, ol {
      margin: 8px 0;
      padding-left: 24px;

      li {
        margin: 4px 0;
        line-height: 1.5;
      }
    }

    code {
      background: rgba(102, 126, 234, 0.1);
      color: #667eea;
      padding: 3px 6px;
      border-radius: 4px;
      font-family: 'SFMono-Regular', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', monospace;
      font-size: 13px;
      border: 1px solid rgba(102, 126, 234, 0.2);
    }

    pre {
      background: #f8fafc;
      border: 1px solid #e5e7eb;
      padding: 16px;
      border-radius: 8px;
      overflow-x: auto;
      margin: 12px 0;
      position: relative;

      code {
        background: transparent;
        border: none;
        padding: 0;
        color: #1f2937;
      }

      // 代码复制按钮
      &:hover::after {
        content: '复制';
        position: absolute;
        top: 8px;
        right: 8px;
        background: #667eea;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
      }
    }

    blockquote {
      border-left: 4px solid #667eea;
      padding-left: 16px;
      margin: 12px 0;
      color: #6b7280;
      background: #f8fafc;
      padding: 12px 16px;
      border-radius: 0 8px 8px 0;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin: 12px 0;

      th, td {
        border: 1px solid #e5e7eb;
        padding: 8px 12px;
        text-align: left;
      }

      th {
        background: #f8fafc;
        font-weight: 600;
      }
    }

    a {
      color: #667eea;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .messageFooter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 12px;

    .messageTime {
      font-size: 11px;
      color: #9ca3af;
      display: flex;
      align-items: center;
    }

    .messageActions {
      display: flex;
      gap: 4px;
      opacity: 0;
      transition: opacity 0.2s ease;

      .actionButton {
        color: #9ca3af;
        border: none;
        padding: 4px;
        border-radius: 4px;
        transition: all 0.2s ease;

        &:hover {
          color: #667eea;
          background: rgba(102, 126, 234, 0.1);
        }

        &.activeAction {
          color: #667eea;
          background: rgba(102, 126, 234, 0.1);
        }
      }
    }
  }

  &:hover .messageActions {
    opacity: 1;
  }
}

// 移除打字光标动画

// 用户消息特殊样式
.userMessage .messageContent .messageText {
  code {
    background: rgba(255, 255, 255, 0.2);
  }
  
  pre {
    background: rgba(255, 255, 255, 0.1);
  }
}

// 加载消息
.loadingMessage {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 16px 20px;
  margin-bottom: 16px;

  .typingIndicator {
    display: flex;
    gap: 4px;
    margin-right: 12px;

    span {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #667eea;
      animation: typing 1.4s infinite ease-in-out;

      &:nth-child(1) { animation-delay: 0s; }
      &:nth-child(2) { animation-delay: 0.2s; }
      &:nth-child(3) { animation-delay: 0.4s; }
    }
  }

  .loadingText {
    color: #6b7280;
    font-size: 14px;
  }
}

// 打字指示器动画
@keyframes typing {
  0%, 60%, 100% {
    transform: scale(1);
    opacity: 0.5;
  }
  30% {
    transform: scale(1.2);
    opacity: 1;
  }
}

// 输入容器
.inputContainer {
  border-top: 1px solid #e5e7eb;
  background: white;
  padding: 0;

  &.darkInput {
    border-top: 1px solid #434343;
    background: #262626;
  }

  .inputWrapper {
    padding: 20px;

    .inputBox {
      background: #f8fafc;
      border: 2px solid #e5e7eb;
      border-radius: 16px;
      transition: all 0.3s ease;
      overflow: hidden;

      &:focus-within {
        border-color: #667eea;
        box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
        background: #ffffff;
      }

      .messageInput {
        background: transparent;
        border: none;
        padding: 16px 20px 0;
        font-size: 14px;
        line-height: 1.5;
        resize: none;

        &::placeholder {
          color: #9ca3af;
        }

        &:focus {
          box-shadow: none;
        }
      }

      .inputToolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 20px 16px;

        .inputLeft {
          display: flex;
          gap: 4px;

          .toolbarButton {
            color: #9ca3af;
            border: none;
            padding: 4px 8px;
            border-radius: 6px;
            transition: all 0.2s ease;

            &:hover {
              color: #667eea;
              background: rgba(102, 126, 234, 0.1);
            }
          }
        }

        .inputRight {
          display: flex;
          align-items: center;
          gap: 12px;

          .characterCount {
            font-size: 12px;
            color: #9ca3af;
            min-width: 60px;
            text-align: right;
          }

          .sendButton {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 6px 16px;
            height: auto;
            font-weight: 500;
            transition: all 0.3s ease;

            &:hover:not(:disabled) {
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            }

            &:active {
              transform: translateY(0);
            }

            &:disabled {
              background: #e5e7eb;
              color: #9ca3af;
            }
          }
        }
      }
    }
  }

  .inputHint {
    padding: 0 20px 16px;
    text-align: center;

    .hintText {
      font-size: 12px;
      color: #9ca3af;

      .hintTag {
        font-size: 10px;
        padding: 1px 4px;
        margin: 0 2px;
        border-radius: 3px;
      }
    }
  }
}

// 模板模态框
.templateModal {
  .modalHeader {
    display: flex;
    align-items: center;
    gap: 8px;

    .modalIcon {
      color: #667eea;
      font-size: 18px;
    }
  }

  &.darkModal {
    .ant-modal-content {
      background: #262626;
    }

    .ant-modal-header {
      background: #262626;
      border-bottom: 1px solid #434343;
    }
  }
}

// 模板容器
.templatesContainer {
  max-height: 600px;
  overflow-y: auto;

  .templateGrid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
    padding: 8px 0;
  }
}

// 模板卡片
.templateCard {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;

  &:hover {
    border-color: #667eea;
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.15);
    transform: translateY(-2px);
  }

  .ant-card-body {
    padding: 20px;
  }

  .templateHeader {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;

    .templateTitle {
      font-size: 15px;
      font-weight: 600;
      color: #1f2937;
      line-height: 1.4;
    }

    .categoryTag {
      font-size: 11px;
      padding: 2px 8px;
      border-radius: 12px;
      margin-left: 8px;
      flex-shrink: 0;
    }
  }

  .templateDescription {
    display: block;
    font-size: 13px;
    color: #6b7280;
    line-height: 1.5;
    margin-bottom: 12px;
  }

  .templatePreview {
    background: #f8fafc;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 12px;

    .previewText {
      font-size: 12px;
      color: #4b5563;
      line-height: 1.4;
      display: block;
      white-space: pre-wrap;
      word-break: break-word;
    }
  }

  .ant-card-actions {
    border-top: 1px solid #e5e7eb;
    background: #fafbfc;

    .ant-card-actions > li {
      margin: 8px 0;

      .ant-btn-link {
        color: #667eea;
        font-weight: 500;

        &:hover {
          color: #4f46e5;
        }
      }
    }
  }
}

// 深色主题样式
.darkTheme {
  .welcomeCard {
    .welcomeTitle {
      color: #ffffff;
    }

    .welcomeText {
      color: #d1d5db;
    }

    .featureCard {
      background: #374151;
      border-color: #4b5563;

      .featureTitle {
        color: #ffffff;
      }

      .featureDesc {
        color: #d1d5db;
      }
    }

    .welcomeShortcuts {
      background: #374151;
      border-color: #4b5563;

      .shortcutTitle {
        color: #ffffff;
      }

      .shortcutItem {
        color: #d1d5db;
      }
    }
  }

  .messageItem {
    &.assistantMessage .messageContent {
      background: #374151;
      border-color: #4b5563;
      color: #ffffff;

      .messageText {
        code {
          background: rgba(79, 172, 254, 0.2);
          color: #4facfe;
        }

        pre {
          background: #1f2937;
          border-color: #4b5563;

          code {
            color: #e5e7eb;
          }
        }

        blockquote {
          background: #1f2937;
          border-left-color: #4facfe;
          color: #d1d5db;
        }
      }

      .messageFooter .messageTime {
        color: #9ca3af;
      }
    }
  }

  .inputBox {
    background: #374151 !important;
    border-color: #4b5563 !important;

    &:focus-within {
      border-color: #4facfe !important;
      box-shadow: 0 0 0 4px rgba(79, 172, 254, 0.1) !important;
    }

    .messageInput {
      color: #ffffff;

      &::placeholder {
        color: #9ca3af;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .floatingButton {
    bottom: 16px;
    right: 16px;

    .assistantButton {
      width: 52px;
      height: 52px;
      font-size: 22px;
    }
  }

  .assistantDrawer {
    .ant-drawer-content-wrapper {
      width: 100% !important;
    }

    .drawerHeader {
      .headerLeft .headerTitle {
        font-size: 14px;
      }

      .headerRight {
        .headerButton {
          padding: 4px;
        }
      }
    }
  }

  .messagesContainer {
    padding: 16px 12px;
  }

  .messageItem {
    margin-bottom: 20px;

    &.userMessage .messageContent,
    &.assistantMessage .messageContent {
      max-width: 85%;
      padding: 12px 16px;
    }

    .messageAvatar {
      display: none;
    }
  }

  .welcomeMessage .welcomeCard {
    .welcomeFeatures {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .featureCard {
      padding: 16px 12px;
    }
  }

  .inputContainer .inputWrapper {
    padding: 16px;

    .inputBox .inputToolbar {
      padding: 6px 16px 12px;

      .inputRight {
        gap: 8px;

        .sendButton {
          padding: 4px 12px;
          font-size: 13px;
        }
      }
    }
  }

  .templatesContainer .templateGrid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .messageItem {
    &.userMessage .messageContent,
    &.assistantMessage .messageContent {
      max-width: 90%;
      padding: 10px 14px;
      font-size: 13px;
    }
  }

  .inputContainer .inputWrapper .inputBox {
    .messageInput {
      padding: 12px 16px 0;
      font-size: 13px;
    }

    .inputToolbar {
      padding: 4px 16px 10px;
    }
  }
}

// 无障碍设计
.assistantDrawer {
  // 高对比度模式支持
  @media (prefers-contrast: high) {
    .messageItem {
      &.userMessage .messageContent {
        background: #000000;
        color: #ffffff;
        border: 2px solid #ffffff;
      }

      &.assistantMessage .messageContent {
        background: #ffffff;
        color: #000000;
        border: 2px solid #000000;
      }
    }
  }

  // 减少动画模式支持
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.messageItem {
  &.userMessage {
    animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  &.assistantMessage {
    animation: slideInLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

// 悬浮按钮脉冲效果
.assistantButton:not(:hover) {
  animation: pulse 2s infinite;
}

// 抽屉展开动画
.assistantDrawer .ant-drawer-content {
  animation: slideInRight 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
