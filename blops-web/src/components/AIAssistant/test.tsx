import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ConfigProvider } from 'antd';
import AIAssistant from './index';

// Mock the aiAssistant service
jest.mock('@/services/aiAssistant', () => ({
  __esModule: true,
  default: {
    sendChatMessage: jest.fn().mockResolvedValue({
      code: 200,
      data: {
        reply: '<p>这是一个测试回复</p>',
        timestamp: Date.now() / 1000,
      },
    }),
    getQuickTemplates: jest.fn().mockResolvedValue({
      code: 200,
      data: [
        {
          id: '1',
          title: '测试模板',
          description: '这是一个测试模板',
          content: '请帮我分析这个问题',
          category: '故障排查',
        },
      ],
    }),
    clearChatHistory: jest.fn().mockResolvedValue({ code: 200 }),
  },
}));

describe('AIAssistant Component', () => {
  beforeEach(() => {
    // Mock localStorage
    const localStorageMock = {
      getItem: jest.fn(),
      setItem: jest.fn(),
      removeItem: jest.fn(),
      clear: jest.fn(),
    };
    global.localStorage = localStorageMock as any;
    
    // Mock navigator.clipboard
    Object.assign(navigator, {
      clipboard: {
        writeText: jest.fn().mockResolvedValue(undefined),
      },
    });
  });

  const renderComponent = (props = {}) => {
    return render(
      <ConfigProvider>
        <AIAssistant {...props} />
      </ConfigProvider>
    );
  };

  test('renders floating button', () => {
    renderComponent();
    const floatingButton = screen.getByRole('button');
    expect(floatingButton).toBeInTheDocument();
    expect(floatingButton).toHaveClass('assistantButton');
  });

  test('opens drawer when floating button is clicked', async () => {
    renderComponent();
    const floatingButton = screen.getByRole('button');
    
    fireEvent.click(floatingButton);
    
    await waitFor(() => {
      expect(screen.getByText('欢迎使用AI助手')).toBeInTheDocument();
    });
  });

  test('displays welcome message when no messages', async () => {
    renderComponent();
    const floatingButton = screen.getByRole('button');
    
    fireEvent.click(floatingButton);
    
    await waitFor(() => {
      expect(screen.getByText('欢迎使用AI助手')).toBeInTheDocument();
      expect(screen.getByText(/我是您的Kubernetes运维助手/)).toBeInTheDocument();
    });
  });

  test('supports theme switching', async () => {
    renderComponent({ defaultTheme: 'light' });
    const floatingButton = screen.getByRole('button');
    
    fireEvent.click(floatingButton);
    
    await waitFor(() => {
      const themeButton = screen.getByLabelText(/切换到深色主题/);
      expect(themeButton).toBeInTheDocument();
      
      fireEvent.click(themeButton);
      expect(localStorage.setItem).toHaveBeenCalledWith('ai-assistant-theme', 'dark');
    });
  });

  test('sends message and receives reply', async () => {
    renderComponent();
    const floatingButton = screen.getByRole('button');
    
    fireEvent.click(floatingButton);
    
    await waitFor(() => {
      const input = screen.getByPlaceholderText(/输入您的问题/);
      const sendButton = screen.getByText('发送');
      
      fireEvent.change(input, { target: { value: '测试消息' } });
      fireEvent.click(sendButton);
    });
    
    await waitFor(() => {
      expect(screen.getByText('这是一个测试回复')).toBeInTheDocument();
    });
  });

  test('supports keyboard shortcuts', async () => {
    renderComponent();
    
    // Test Ctrl+K to open assistant
    fireEvent.keyDown(document, { key: 'k', ctrlKey: true });
    
    await waitFor(() => {
      expect(screen.getByText('欢迎使用AI助手')).toBeInTheDocument();
    });
    
    // Test Escape to close assistant
    fireEvent.keyDown(document, { key: 'Escape' });
    
    await waitFor(() => {
      expect(screen.queryByText('欢迎使用AI助手')).not.toBeInTheDocument();
    });
  });

  test('supports drag resize when enabled', () => {
    renderComponent({ enableDragResize: true });
    const floatingButton = screen.getByRole('button');
    
    fireEvent.click(floatingButton);
    
    // The resize handle should be present
    const resizeHandle = document.querySelector('.resizeHandle');
    expect(resizeHandle).toBeInTheDocument();
  });

  test('displays quick templates', async () => {
    renderComponent();
    const floatingButton = screen.getByRole('button');
    
    fireEvent.click(floatingButton);
    
    await waitFor(() => {
      const templateButton = screen.getByLabelText('快速模板');
      fireEvent.click(templateButton);
    });
    
    await waitFor(() => {
      expect(screen.getByText('快速问题模板')).toBeInTheDocument();
      expect(screen.getByText('测试模板')).toBeInTheDocument();
    });
  });

  test('supports message actions', async () => {
    renderComponent();
    const floatingButton = screen.getByRole('button');
    
    fireEvent.click(floatingButton);
    
    // Send a message first
    await waitFor(() => {
      const input = screen.getByPlaceholderText(/输入您的问题/);
      const sendButton = screen.getByText('发送');
      
      fireEvent.change(input, { target: { value: '测试消息' } });
      fireEvent.click(sendButton);
    });
    
    // Wait for reply and test copy action
    await waitFor(() => {
      const copyButton = screen.getByLabelText('复制');
      fireEvent.click(copyButton);
      expect(navigator.clipboard.writeText).toHaveBeenCalled();
    });
  });

  test('supports responsive design', () => {
    // Mock window.innerWidth for mobile
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 480,
    });
    
    renderComponent();
    const floatingButton = screen.getByRole('button');
    
    fireEvent.click(floatingButton);
    
    // On mobile, drawer should take full width
    const drawer = document.querySelector('.ant-drawer-content-wrapper');
    expect(drawer).toHaveStyle('width: 100%');
  });

  test('supports accessibility features', () => {
    renderComponent();
    const floatingButton = screen.getByRole('button');
    
    // Button should have proper aria attributes
    expect(floatingButton).toHaveAttribute('aria-label');
    
    fireEvent.click(floatingButton);
    
    // Drawer should have proper focus management
    const drawer = screen.getByRole('dialog');
    expect(drawer).toBeInTheDocument();
  });
});
