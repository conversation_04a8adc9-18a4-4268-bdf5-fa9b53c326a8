import React, { Component } from 'react';
import { AutoComplete } from 'antd';

export default class CommonAutoComplete extends Component {
  handleSearch = value => {
    // 搜索补全项时
    if (this.props.onSearch) {
      this.props.onSearch(value);
    }
  };

  onChange = value => {
    if (this.props.onChange) {
      this.props.onChange(value);
    }
  };

  render() {
    const {
      placeholder = '',
      key,
      style,
      initValue,
      clear = false,
    } = this.props;
    return clear ? (
      <AutoComplete
        key="clear"
        style={
          style || {
            width: 220,
          }
        }
        placeholder={placeholder}
      />
    ) : (
      <AutoComplete
        key={key}
        allowClear
        value={initValue}
        style={
          style || {
            width: 220,
          }
        }
        onSearch={this.handleSearch}
        onChange={this.onChange}
        placeholder={placeholder}
      />
    );
  }
}
