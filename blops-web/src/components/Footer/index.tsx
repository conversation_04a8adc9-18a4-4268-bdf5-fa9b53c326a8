import { useIntl } from 'umi';
import { DefaultFooter } from '@ant-design/pro-layout';

const Footer: React.FC = () => {
  const intl = useIntl();
  const defaultMessage = intl.formatMessage({
    id: 'app.copyright.produced',
    defaultMessage: '黑湖科技-技术部-基础架构',
  });

  const currentYear = new Date().getFullYear();

  return (
    <DefaultFooter
      copyright={`${currentYear} ${defaultMessage}`}
      links={[
        {
          key: 'black lake',
          title: '黑湖智造',
          href: 'https://www.blacklake.cn',
          blankTarget: true,
        },
      ]}
    />
  );
};

export default Footer;
