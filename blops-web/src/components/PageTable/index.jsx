import React, { Component } from 'react';
import { Table } from 'antd';

// eslint-disable-next-line react/prefer-stateless-function
export class PageTable extends Component {
  render() {
    const {
      columns,
      dataSource,
      loading,
      onPageChange,
      onPageSizeChange,
      page,
      size,
      total,
    } = this.props;
    return (
      <Table
        bordered
        size="middle"
        loading={loading}
        pagination={{
          showSizeChanger: true,
          onShowSizeChange: onPageSizeChange,
          pageSizeOptions: ['10', '20', '30', '50'],
          pageSize: size,
          defaultCurrent: 1,
          current: page,
          total,
          onChange: (p, s) => onPageChange(p, s),
        }}
        columns={columns}
        dataSource={dataSource}
        style={{ overflow: 'scroll', whiteSpace: 'nowrap' }}
      />
    );
  }
}

export default PageTable;
