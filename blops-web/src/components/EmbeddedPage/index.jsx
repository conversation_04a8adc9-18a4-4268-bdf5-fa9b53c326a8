import React, { useState } from 'react';
import { Spin } from 'antd';
import styles from './index.module.less';

const EmbeddedPage = ({ src, title }) => {
  const [loading, setLoading] = useState(true);

  const handleLoad = () => {
    setLoading(false);
  };

  return (
    <div className={styles.embeddedPageContainer}>
      {loading && (
        <div className={styles.spinContainer}>
          <Spin size="large" tip={`Loading ${title}...`} />
        </div>
      )}
      <iframe
        src={src}
        title={title}
        className={styles.iframe}
        onLoad={handleLoad}
        style={{ display: loading ? 'none' : 'block' }}
        sandbox="allow-scripts allow-same-origin allow-popups allow-forms allow-modals allow-downloads"
        allowFullScreen={true}
        referrerPolicy="no-referrer"
      />
    </div>
  );
};

export default EmbeddedPage; 