.embeddedPageContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden; /* To ensure iframe does not cause scrollbars on container */
  position: relative; /* Ensure proper positioning context */
}

.spinContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  position: absolute; /* Position it over the iframe space */
  top: 0;
  left: 0;
  background-color: #fff; /* Optional: background for the spinner */
  z-index: 10; /* Ensure spinner is on top */
}

.iframe {
  flex-grow: 1;
  border: none;
  height: 100%;
  width: 100%;
  min-height: 600px; /* Provide a minimum height */
  overflow: hidden;
} 