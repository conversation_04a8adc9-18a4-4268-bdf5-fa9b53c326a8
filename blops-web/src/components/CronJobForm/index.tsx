import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { Form, Input, Select, Radio, Button, Space, Row, Col, Card, Tooltip, Switch } from 'antd';
import { PlusOutlined, DeleteOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import yaml from 'js-yaml';

const { Option } = Select;
const { TextArea } = Input;

// 自定义TextArea组件，保持光标位置
const CursorPreservingTextArea: React.FC<any> = ({ value, onChange, ...props }) => {
  const textAreaRef = useRef<HTMLTextAreaElement>(null);
  const [internalValue, setInternalValue] = useState(value || '');
  const [isFocused, setIsFocused] = useState(false);

  // 同步外部value到内部state
  useEffect(() => {
    if (!isFocused) {
      setInternalValue(value || '');
    }
  }, [value, isFocused]);

  // 处理输入变化
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setInternalValue(newValue);

    // 创建一个新的事件对象传递给父组件
    if (onChange) {
      onChange(e);
    }
  };

  // 处理焦点事件
  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
    // 失去焦点时同步值
    setInternalValue(value || '');
  };

  return (
    <TextArea
      ref={textAreaRef}
      value={isFocused ? internalValue : (value || '')}
      onChange={handleChange}
      onFocus={handleFocus}
      onBlur={handleBlur}
      {...props}
    />
  );
};

interface CronJobFormProps {
  initialValues?: any;
  onValuesChange?: (values: any) => void;
  onGenerateYaml?: (yamlContent: string) => void;
}

const CronJobForm: React.FC<CronJobFormProps> = ({
  initialValues,
  onValuesChange,
  onGenerateYaml,
}) => {
  const [form] = Form.useForm();
  const [envVars, setEnvVars] = useState<{ key: string; value: string }[]>([]);

  useEffect(() => {
    if (initialValues) {
      // 解析 YAML 为表单值
      try {
        if (typeof initialValues === 'string') {
          console.log('解析初始 YAML:', initialValues);
          const parsedYaml = yaml.load(initialValues) as any;
          const formValues = convertYamlToFormValues(parsedYaml);
          console.log('转换后的表单值:', formValues);
          form.setFieldsValue(formValues);
          
          // 提取环境变量
          const containers = parsedYaml.spec?.jobTemplate?.spec?.template?.spec?.containers || [];
          if (containers.length > 0) {
            const envs = containers[0]?.env || [];
            setEnvVars(envs.map((env: any) => ({ key: env.name || '', value: env.value || '' })));
          }
        }
      } catch (error) {
        console.error('解析 YAML 失败:', error, initialValues);
      }
    }
  }, [initialValues, form]);

  // 防抖处理，避免频繁触发YAML生成
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  const handleValuesChange = (changedValues: any, allValues: any) => {
    if (onValuesChange) {
      onValuesChange(allValues);
    }

    // 清除之前的定时器
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // 设置新的定时器，延迟生成YAML
    debounceTimerRef.current = setTimeout(() => {
      const yamlObj = convertFormValuesToYaml(allValues);
      const yamlContent = yaml.dump(yamlObj);
      if (onGenerateYaml) {
        onGenerateYaml(yamlContent);
      }
    }, 300); // 300ms防抖
  };

  // 清理定时器
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  const convertYamlToFormValues = (yamlObj: any) => {
    if (!yamlObj) return {};
    
    const containers = yamlObj.spec?.jobTemplate?.spec?.template?.spec?.containers || [];
    
    return {
      name: yamlObj.metadata?.name || '',
      namespace: yamlObj.metadata?.namespace || 'default',
      schedule: yamlObj.spec?.schedule || '0 * * * *',
      imageSource: 'public', // 默认值
      imageName: containers.length > 0 ? containers[0].image : '',
      command: containers.length > 0 ? containers[0].command?.join(' ') : '',
      args: containers.length > 0 ? containers[0].args?.join(' ') : '',
      suspend: yamlObj.spec?.suspend || false,
    };
  };

  const convertFormValuesToYaml = (values: any) => {
    const { name, namespace, schedule, imageName, command, args, suspend } = values;
    
    // 处理命令和参数
    // 改进命令拆分逻辑
    let commandArray: string[] = [];
    if (command && command.trim()) {
      // 检查是否是 "/bin/sh -c" 这种模式
      if (command.includes('/bin/sh -c') || command.includes('/bin/bash -c')) {
        // 拆分成 ["/bin/sh", "-c"] 或 ["/bin/bash", "-c"]
        const parts = command.trim().split(' ');
        commandArray = [parts[0], parts[1]];
      } else {
        // 其他命令按空格拆分
        commandArray = command.split(' ').filter(Boolean);
      }
    }
    
    // 参数处理 - 保持整体作为一个参数，避免拆分复杂的 shell 命令
    let argsArray: string[] = [];
    if (args && args.trim()) {
      // 如果是 shell 命令模式（即 command 包含 -c），则整个 args 作为一个参数
      if (commandArray.includes('-c')) {
        argsArray = [args.trim()];
      } else {
        // 否则按空格拆分成多个参数
        argsArray = args.split(' ').filter(Boolean);
      }
    }
    
    // 构建环境变量
    const envArray = envVars.filter(env => env.key.trim() !== '') // 过滤掉空键
      .map(({ key, value }) => ({
        name: key.trim(),
        value: value
      }));
    
    // 保留原始 yaml 中的一些关键字段
    let originalYaml: any = {};
    try {
      if (typeof initialValues === 'string') {
        originalYaml = yaml.load(initialValues) || {};
      }
    } catch (e) {
      console.error('解析原始 YAML 失败', e);
    }
    
    // 创建新的 YAML 对象，保留一些原始信息
    return {
      apiVersion: 'batch/v1beta1',
      kind: 'CronJob',
      metadata: {
        name,
        namespace,
        ...(originalYaml.metadata?.labels && { labels: originalYaml.metadata.labels }),
        ...(originalYaml.metadata?.annotations && { annotations: originalYaml.metadata.annotations }),
      },
      spec: {
        schedule, // 确保 schedule 被正确更新
        suspend,
        ...(originalYaml.spec?.successfulJobsHistoryLimit && 
          { successfulJobsHistoryLimit: originalYaml.spec.successfulJobsHistoryLimit }),
        ...(originalYaml.spec?.failedJobsHistoryLimit && 
          { failedJobsHistoryLimit: originalYaml.spec.failedJobsHistoryLimit }),
        ...(originalYaml.spec?.concurrencyPolicy && 
          { concurrencyPolicy: originalYaml.spec.concurrencyPolicy }),
        jobTemplate: {
          spec: {
            ...(originalYaml.spec?.jobTemplate?.spec?.activeDeadlineSeconds &&
              { activeDeadlineSeconds: originalYaml.spec.jobTemplate.spec.activeDeadlineSeconds }),
            backoffLimit: originalYaml.spec?.jobTemplate?.spec?.backoffLimit ?? 0,
            template: {
              ...(originalYaml.spec?.jobTemplate?.spec?.template?.metadata && 
                { metadata: originalYaml.spec.jobTemplate.spec.template.metadata }),
              spec: {
                containers: [
                  {
                    name: 'main',
                    image: imageName,
                    ...(commandArray.length > 0 && { command: commandArray }),
                    ...(argsArray.length > 0 && { args: argsArray }),
                    ...(envArray.length > 0 && { env: envArray }),
                    // 添加固定的资源限制
                    resources: {
                      limits: {
                        memory: '512Mi',
                        cpu: '100m'
                      },
                      requests: {
                        memory: '128Mi',
                        cpu: '50m'
                      }
                    },
                    // 保留原始容器的其他属性，但不使用原有的 resources
                    ...(originalYaml.spec?.jobTemplate?.spec?.template?.spec?.containers?.[0]?.volumeMounts && 
                      { volumeMounts: originalYaml.spec.jobTemplate.spec.template.spec.containers[0].volumeMounts }),
                  },
                ],
                restartPolicy: 'Never',
                ...(originalYaml.spec?.jobTemplate?.spec?.template?.spec?.volumes &&
                  { volumes: originalYaml.spec.jobTemplate.spec.template.spec.volumes }),
              },
            },
          },
        },
      },
    };
  };

  const addEnvVar = () => {
    setEnvVars([...envVars, { key: '', value: '' }]);
  };

  const removeEnvVar = (index: number) => {
    const newEnvVars = [...envVars];
    newEnvVars.splice(index, 1);
    setEnvVars(newEnvVars);
    handleValuesChange({}, { envVars: newEnvVars });
  };

  const updateEnvVar = (index: number, field: 'key' | 'value', value: string) => {
    const newEnvVars = [...envVars];
    newEnvVars[index][field] = value;
    setEnvVars(newEnvVars);
    handleValuesChange({}, { envVars: newEnvVars });
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onValuesChange={(changedValues, allValues) => handleValuesChange(changedValues, allValues)}
      initialValues={{
        schedule: '0 * * * *',
        imageSource: 'public',
        suspend: false,
        namespace: 'default',
        imageName: 'nginx',
        command: '/bin/bash -c',
        args: 'echo "Hello World"',
      }}
    >
      <Form.Item
        label="任务名称"
        name="name"
        rules={[{ required: true, message: '请输入任务名称' }]}
      >
        <Input placeholder="以字母开头，只能包含小写字母、数字和连字符" />
      </Form.Item>

      <Form.Item
        label="命名空间"
        name="namespace"
        rules={[{ required: true, message: '请输入命名空间' }]}
      >
        <Input placeholder="命名空间" value="default" disabled />
      </Form.Item>

      <Form.Item
        label={
          <span>
            Cron 表达式
            <Tooltip title="Cron 表达式格式: 分钟 小时 日期 月份 星期">
              <QuestionCircleOutlined style={{ marginLeft: 4 }} />
            </Tooltip>
          </span>
        }
        name="schedule"
        rules={[{ required: true, message: '请输入Cron表达式' }]}
      >
        <Input placeholder="例如: 0 * * * * (每小时)" />
      </Form.Item>

      <div style={{ marginBottom: 16 }}>
        <Space>
          <Button 
            type="link" 
            style={{ color: form.getFieldValue('schedule') === '*/5 * * * *' ? '#1890ff' : undefined }}
            onClick={() => {
              form.setFieldsValue({ schedule: '*/5 * * * *' });
              handleValuesChange({ schedule: '*/5 * * * *' }, form.getFieldsValue());
            }}
          >
            每五分钟
          </Button>
          <Button 
            type="link" 
            style={{ color: form.getFieldValue('schedule') === '0 * * * *' ? '#1890ff' : undefined }}
            onClick={() => {
              form.setFieldsValue({ schedule: '0 * * * *' });
              handleValuesChange({ schedule: '0 * * * *' }, form.getFieldsValue());
            }}
          >
            每小时
          </Button>
          <Button 
            type="link" 
            style={{ color: form.getFieldValue('schedule') === '0 8 * * *' ? '#1890ff' : undefined }}
            onClick={() => {
              form.setFieldsValue({ schedule: '0 8 * * *' });
              handleValuesChange({ schedule: '0 8 * * *' }, form.getFieldsValue());
            }}
          >
            每天上午8点
          </Button>
          <Button 
            type="link" 
            style={{ color: form.getFieldValue('schedule') === '0 0 * * *' ? '#1890ff' : undefined }}
            onClick={() => {
              form.setFieldsValue({ schedule: '0 0 * * *' });
              handleValuesChange({ schedule: '0 0 * * *' }, form.getFieldsValue());
            }}
          >
            每天零点
          </Button>
        </Space>
      </div>

      <Form.Item
        label="暂停任务"
        name="suspend"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>

      <Form.Item
        label="镜像名称"
        name="imageName"
        rules={[{ required: true, message: '请输入镜像名称' }]}
      >
        <Input placeholder="例如: busybox" />
      </Form.Item>

      <Form.Item
        label={
          <span>
            运行命令
            <Tooltip title="如果需要执行shell命令，推荐使用 '/bin/sh -c' 或 '/bin/bash -c'">
              <QuestionCircleOutlined style={{ marginLeft: 4 }} />
            </Tooltip>
          </span>
        }
        name="command"
      >
        <Input placeholder="例如: /bin/sh -c" />
      </Form.Item>
      
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Button 
            type="link" 
            style={{ color: form.getFieldValue('command') === '/bin/sh -c' ? '#1890ff' : undefined }}
            onClick={() => {
              form.setFieldsValue({ command: '/bin/sh -c' });
              handleValuesChange({ command: '/bin/sh -c' }, form.getFieldsValue());
            }}
          >
            Shell 命令
          </Button>
          <Button 
            type="link" 
            style={{ color: form.getFieldValue('command') === '/bin/bash -c' ? '#1890ff' : undefined }}
            onClick={() => {
              form.setFieldsValue({ command: '/bin/bash -c' });
              handleValuesChange({ command: '/bin/bash -c' }, form.getFieldsValue());
            }}
          >
            Bash 命令
          </Button>
        </Space>
      </div>

      {useMemo(() => (
        <Form.Item
          label={
            <span>
              命令参数
              <Tooltip title="当运行命令为'/bin/sh -c'时，整个参数将作为一个完整的shell命令执行">
                <QuestionCircleOutlined style={{ marginLeft: 4 }} />
              </Tooltip>
            </span>
          }
          name="args"
        >
          <CursorPreservingTextArea placeholder="例如: sleep 10 && curl www.qq.com" rows={3} />
        </Form.Item>
      ), [])}
      
      <div style={{ marginBottom: 16 }}>
        <div>常用命令示例：</div>
        <Space wrap>
          <Button
            type="link"
            style={{ color: form.getFieldValue('args') === 'echo "Hello World"' ? '#1890ff' : undefined }}
            onClick={() => {
              const newValues = {
                imageName: 'busybox',
                command: '/bin/sh -c',
                args: 'echo "Hello World"'
              };
              form.setFieldsValue(newValues);
              handleValuesChange(newValues, { ...form.getFieldsValue(), ...newValues });
            }}
          >
            简单输出
          </Button>
          <Button
            type="link"
            onClick={() => {
              const newValues = {
                imageName: 'busybox',
                command: '/bin/sh -c',
                args: 'sleep 10 && echo "Task completed"'
              };
              form.setFieldsValue(newValues);
              handleValuesChange(newValues, { ...form.getFieldsValue(), ...newValues });
            }}
          >
            延时任务
          </Button>
          <Button
            type="link"
            onClick={() => {
              const newValues = {
                imageName: 'busybox',
                command: '/bin/sh -c',
                args: 'date > /tmp/date.txt'
              };
              form.setFieldsValue(newValues);
              handleValuesChange(newValues, { ...form.getFieldsValue(), ...newValues });
            }}
          >
            输出到文件
          </Button>
          <Button
            type="link"
            style={{ color: '#1890ff' }}
            onClick={() => {
              const pythonScript = `cat > /tmp/demo.py << EOF
import socket
hostname = socket.gethostname()
try:
    # 尝试连接到一个外部地址（不实际发送数据）来确定本机用于外部通信的IP
    with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
        s.connect(("*******", 80)) # 连接到Google DNS服务器的80端口
        ip_address = s.getsockname()[0]
except Exception:
    # 如果上述方法失败（例如无网络连接），尝试用主机名解析IP（可能得到127.0.0.1）
    try:
        ip_address = socket.gethostbyname(hostname)
    except socket.gaierror:
        ip_address = "无法获取IP" # 最终的备用方案
print(f"主机名: {hostname}")
print(f"IP 地址: {ip_address}")
EOF
python /tmp/demo.py`;
              const newValues = {
                imageName: 'python',
                command: '/bin/sh -c',
                args: pythonScript
              };
              form.setFieldsValue(newValues);
              handleValuesChange(newValues, { ...form.getFieldsValue(), ...newValues });
            }}
          >
            Python脚本
          </Button>
          <Button
            type="link"
            onClick={() => {
              const newValues = {
                imageName: 'nginx',
                command: '/bin/sh -c',
                args: 'curl -s https://ifconfig.me'
              };
              form.setFieldsValue(newValues);
              handleValuesChange(newValues, { ...form.getFieldsValue(), ...newValues });
            }}
          >
            出口IP
          </Button>
          <Button
            type="link"
            onClick={() => {
              const newValues = {
                imageName: 'harbor.blacklake.tech/infra/ncat:v1',
                command: '/usr/local/bin/ncat',
                args: '-zv -w 3 www.qq.com 8080'
              };
              form.setFieldsValue(newValues);
              handleValuesChange(newValues, { ...form.getFieldsValue(), ...newValues });
            }}
          >
            端口探测
          </Button>
        </Space>
      </div>

      <Card title="资源限制" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={12}>
            <div style={{ marginBottom: 8 }}><strong>CPU 限制:</strong> 100m（0.1核）</div>
            <div><strong>CPU 请求:</strong> 50m（0.05核）</div>
          </Col>
          <Col span={12}>
            <div style={{ marginBottom: 8 }}><strong>内存限制:</strong> 512Mi</div>
            <div><strong>内存请求:</strong> 128Mi</div>
          </Col>
        </Row>
        <div style={{ marginTop: 8, color: '#888' }}>
          <small>注: 资源限制已预设，无需手动配置</small>
        </div>
      </Card>

      <Card title="环境变量" extra={<Button type="link" icon={<PlusOutlined />} onClick={addEnvVar}>添加</Button>}>
        {envVars.map((env, index) => (
          <Row key={index} gutter={16} style={{ marginBottom: 8 }}>
            <Col span={11}>
              <Input 
                placeholder="变量名" 
                value={env.key} 
                onChange={(e) => updateEnvVar(index, 'key', e.target.value)} 
              />
            </Col>
            <Col span={11}>
              <Input 
                placeholder="变量值" 
                value={env.value} 
                onChange={(e) => updateEnvVar(index, 'value', e.target.value)} 
              />
            </Col>
            <Col span={2}>
              <Button 
                type="text" 
                danger 
                icon={<DeleteOutlined />} 
                onClick={() => removeEnvVar(index)} 
              />
            </Col>
          </Row>
        ))}
      </Card>
    </Form>
  );
};

export default CronJobForm; 