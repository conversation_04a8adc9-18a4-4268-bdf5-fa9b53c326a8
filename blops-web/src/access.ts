/**
 * @see https://umijs.org/zh-CN/plugins/plugin-access
 * */
// Define the API namespace
declare namespace API {
  interface CurrentUser {
    access?: string[];
    username?: string;
    userId?: string;
    email?: string;
    displayName?: string;
    token?: string;
    // Add other properties as needed
  }
}

export default function access(initialState: { currentUser?: API.CurrentUser } | undefined) {
  const { currentUser } = initialState ?? {};

  // 如果用户名为null，尝试使用其他标识符
  const userId = currentUser?.userId || currentUser?.email || currentUser?.displayName;
  console.log('尝试使用的用户标识:', userId);
  
  // 定义有权限的用户列表 - 可以包含多种标识符
  const kubernetesUsers = ['田如云','高帅旗','齐冠权','gaoshuai','任永强','廖诗秩','陈继强',]; 
  
  // 检查当前用户是否在允许列表中
  const hasKubernetesAccess = userId && kubernetesUsers.some(u => 
    u.toLowerCase() === String(userId).toLowerCase()
  );
  
  // 也可以通过token检查
  const token = currentUser?.token;
  const isAuthenticated = !!token;
  
  // 如果需要，可以添加一个临时的全局访问权限用于测试
  const temporaryAccess = false; // 改为false，只允许列表中的用户访问
  
  const canAccess = hasKubernetesAccess || (isAuthenticated && temporaryAccess);
  console.log('是否有权限访问Kubernetes:', canAccess);
  
  return {
    canAccessKubernetes: canAccess,
    canAccessKubernetesList: canAccess,
    // 可以添加更多权限检查
  };
}
