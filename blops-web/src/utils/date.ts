export function formatDate(dateString: string): string {
  if (!dateString) return '';
  
  try {
    // 处理带时区信息的日期格式，例如 "2025-04-15 16:43:04 +0800 CST"
    if (dateString.includes('+0800 CST')) {
      // 直接提取日期和时间部分，忽略时区信息
      const parts = dateString.split(' ');
      if (parts.length >= 2) {
        // 只保留日期和时间部分
        return `${parts[0]} ${parts[1]}`;
      }
    }
    
    // 处理ISO格式的日期，例如 "2025-04-15T08:43:04Z"
    if (dateString.includes('T') && (dateString.includes('Z') || dateString.includes('+'))) {
      // 使用Date对象转换为本地时间
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
        timeZone: 'Asia/Shanghai'
      });
    }
    
    // 如果是其他格式，直接返回原始字符串
    return dateString;
  } catch (error) {
    return dateString;
  }
} 