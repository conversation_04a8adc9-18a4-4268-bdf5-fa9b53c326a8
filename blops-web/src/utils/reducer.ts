import { routerRedux } from 'dva/router';

// @ts-ignore
export function* callReducer(reducer: any, payload: any, { put }, callback?: any) {
  if (payload) {
    yield put({
      type: reducer,
      payload,
    });
  }
  if (callback) {
    yield callback();
  }
}

// @ts-ignore
export const pushUrl = (url, { put }) =>
  function* f() {
    yield put(routerRedux.push(url));
  };

export default { callReducer, pushUrl };
