/**
 * request 网络请求工具
 * 更详细的 api 文档: https://github.com/umijs/umi-request
 */
import type {RequestOptionsInit} from 'umi-request';
import { notification } from 'antd';
import localStorage from '@/utils/localStorage';
import { extend } from 'umi-request';

const codeMessage = {
  // 200: '服务器成功返回请求的数据。',
  201: '新建或修改数据成功。',
  202: '一个请求已经进入后台排队（异步任务）。',
  204: '删除数据成功。',
  400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
  401: '用户没有权限（令牌、用户名、密码错误）。',
  403: '用户得到授权，但是访问是被禁止的。',
  404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
  406: '请求的格式不可得。',
  410: '请求的资源被永久删除，且不会再得到的。',
  422: '当创建一个对象时，发生一个验证错误。',
  500: '服务器发生错误，请检查服务器。',
  502: '网关错误。',
  503: '服务不可用，服务器暂时过载或维护。',
  504: '网关超时。',
};

/**
 * 异常处理程序
 */
export const errorHandler = (error: { response: Response }) => {
  const { response } = error;
  if (response && response.status) {
    response
      .json()
      .then(data => {
        // Add type assertion for data.code
        const code = data?.code as keyof typeof codeMessage;
        const errorText = data.message || (code ? codeMessage[code] : '') || response.statusText;
        const { status } = response;
        notification.error({
          message: `请求错误 ${status}`,
          description: errorText,
        });
      })
      .catch(err => {
        // console.log(err);
        notification.error({
          message: `请求错误 ${err}`,
        });
      });
  } else if (!response) {
    notification.error({
      description: '您的网络发生异常，无法连接服务器',
      message: '网络异常',
    });
  }
  return response;
};

/**
 * 配置request请求时的默认参数
 */
const request = extend({
  errorHandler, // 默认错误处理
  credentials: 'include', // 默认请求是否带上cookie
  timeout: 30000, // 默认超时时间30秒
});

// 为AI诊断接口创建一个特殊的请求实例，超时时间为2分钟
const aiRequest = extend({
  errorHandler,
  credentials: 'include',
  timeout: 120000, // 2分钟超时
});

export const ReqInterceptors = [
  (url: string, options: RequestOptionsInit) => ({
    url,
    options: {
      ...options,
      headers: {
        ...options.headers,
        'X-AUTH': localStorage.get('token') || '',
      },
    },
  }),
]
// (response: Response, options: RequestOptionsInit)
export const ResInterceptors = [
  (response: Response, options: RequestOptionsInit) => {
    if (response) {
      // 只处理非成功状态码
      if (response.status >= 400) {
        response
          .clone()
          .json()
          .then(data => {
            if (data?.result === "SUCCESS") return;
            const code = data?.code as keyof typeof codeMessage;
            const errorText = data.message || (code ? codeMessage[code] : '') || response.statusText;
            notification.error({
              message: `请求错误 ${data?.code}`,
              description: errorText,
            });
          })
          .catch(err => {
            notification.error({
              message: `请求错误 ${err}`,
            });
          });
      }
    } else if (!response) {
      notification.error({
        description: '您的网络发生异常，无法连接服务器',
        message: '网络异常',
      });
    }
    return response;
  },
]


export const REQ_METHOD = {
  POST: 'POST',
  GET: 'GET',
  PUT: 'PUT',
  DELETE: 'DELETE',
}

// 应用请求拦截器
ReqInterceptors.forEach(interceptor => {
  request.interceptors.request.use(interceptor);
  aiRequest.interceptors.request.use(interceptor);
});

// 应用响应拦截器
ResInterceptors.forEach(interceptor => {
  request.interceptors.response.use(interceptor);
  aiRequest.interceptors.response.use(interceptor);
});

export default request;
export { aiRequest };
