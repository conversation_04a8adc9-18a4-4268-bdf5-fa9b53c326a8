import type {Settings as LayoutSettings} from '@ant-design/pro-layout';
import {PageLoading} from '@ant-design/pro-layout';
import type {RunTimeLayoutConfig, RequestConfig} from 'umi';
import {history, Link} from 'umi';
import RightContent from '@/components/RightContent';
import Footer from '@/components/Footer';
import AIAssistant from '@/components/AIAssistant';
import {useModel} from 'umi';
import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import defaultSettings from '../config/defaultSettings';
import localStorage from "@/utils/localStorage";
import {errorHandler, ReqInterceptors} from '@/utils/request';
import type {API} from "@/dtos/typings";
import * as qs from "query-string";

const loginPath = '/user/login';

/** 获取用户信息比较慢的时候会展示一个 loading */
export const initialStateConfig = {
  loading: <PageLoading/>,
};

export const request: RequestConfig = {
  timeout: 20000,
  errorConfig: {},
  middlewares: [],
  requestInterceptors: [...ReqInterceptors],
  // responseInterceptors: [...ResInterceptors],
  errorHandler,
  credentials: 'include',
};

// @ts-ignore
window.connectModel = (key: any, name: any) => {
  return (WrappedComponent: JSX.IntrinsicAttributes) => {
    return (props: JSX.IntrinsicAttributes) => {
      const model = useModel(name);
      const data = {[key]: model};
      // @ts-ignore
      return <WrappedComponent {...props} {...data}/>;
    };
  };
};

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<{
  currentUser?: API.CurrentUser;
  settings?: Partial<LayoutSettings>;
  collapsed?: boolean;
}> {
  // const fetchUserInfo = async () => {
  //   try {
  //     const resp = await profile();
  //     return resp?.data;
  //   } catch (error) {
  //     history.push(loginPath);
  //   }
  //   return undefined;
  // };
  // 如果是登录页面，不执行
  if (history.location.pathname !== '/user/login') {
    try {
      if (!localStorage.get("token")) {
        history.push(loginPath)
      }
      // const currentUser = await fetchUserInfo();
      const currentUser: API.CurrentUser = {
        username: localStorage.get("username"),
        userId: localStorage.get("userId"),
        token: localStorage.get("token"),
        email: localStorage.get("email"),
        displayName: localStorage.get("displayName")
      };
      return {
        currentUser,
        settings: {
          ...defaultSettings,
        },
        collapsed: false,
      };
    } catch (error) {
      history.push(loginPath);
    }
  }
  return {
    settings: {
      ...defaultSettings,
    },
    collapsed: false,
  };
}

// ProLayout 支持的api https://procomponents.ant.design/components/layout
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const layout: RunTimeLayoutConfig = ({initialState, setInitialState}) => {
  const setCollapsed = () => {
    setInitialState((s) => {
      if (s) {
        s.collapsed = !s?.collapsed;
      }
      return {...s};
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    }).then(_ => {});
  }
  return {
    rightContentRender: () => <RightContent/>,
    disableContentMargin: false,
    // waterMarkProps: {
    //   content: initialState?.currentUser?.displayName,
    // },
    footerRender: () => <Footer/>,
    onPageChange: () => {
      const {location} = history;
      // 如果没有登录，重定向到 login
      if (!localStorage?.get('token') && location.pathname !== loginPath) {
        history.push(loginPath);
      }
    },
    menuHeaderRender: undefined,
    collapsedButtonRender: false,
    collapsed: initialState?.collapsed,
    onCollapse: setCollapsed,
    headerContentRender:()=>{
      return (
        <div
          onClick={() => setCollapsed()}
          style={{
            cursor: 'pointer',
            fontSize: '16px',
          }}
        >
          {!!initialState?.collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
        </div>
      );
    },
    menuItemRender: (menuItemProps, defaultDom) => {
      if (menuItemProps.isUrl) {
        return defaultDom;
      }
      // @ts-ignore
      return <Link to={menuItemProps.path}>{defaultDom}</Link>;
    },
    breadcrumbRender: (routers = []) => [
      {
        path: '/',
        breadcrumbName: '首页',
      },
      ...routers,
    ],
    // 自定义 403 页面
    // unAccessible: <div>unAccessible</div>,
    // 添加AI助手组件到布局中
    childrenRender: (children) => {
      // 在登录页面不显示AI助手
      if (history.location.pathname === loginPath) {
        return children;
      }
      return (
        <>
          {children}
          <AIAssistant />
        </>
      );
    },
    ...initialState?.settings,
  };
};


/**
 * umi配置
 */

/**
 * umi location query 统一配置
 */
// @ts-ignore
export function onRouteChange({location}) {
  location.query = qs.parse(qs.stringify(location.query), {
    parseBooleans: true,
    parseNumbers: true,
  });
}
