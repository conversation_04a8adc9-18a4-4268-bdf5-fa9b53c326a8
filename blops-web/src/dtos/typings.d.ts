
declare namespace API {
  type Resp = {
    status: number,
    message: string,
    code: string,
    data: any,
  }

  type CurrentUser = {
    username?: string;
    userId?: number;
    email?: string;
    token?: string;
    displayName?: string;
  };
}

export type BaseKV = {
  key: string | number,
  value: any,
}

export class BasePO {
  id?: number;
  orgId?: number;
  creatorId?: number;
  operatorId?: number;
  updatedAt?: number;
  createdAt?: number;
  deletedAt?: number;
}

interface FormFieldData {
  name: string | number | (string | number)[];
  value?: string | number;
  touched?: boolean;
  validating?: boolean;
  errors?: string[];
}

export type FormType = 'add' | 'edit';
