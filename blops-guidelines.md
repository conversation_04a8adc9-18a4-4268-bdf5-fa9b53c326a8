# Blops 后端 (blops) 开发规范

## 1. 引言

本规范旨在为使用 Go 语言和 Gin Web 框架开发 Blops 后端应用提供标准。结构、命名和 API 设计的一致性对于可维护性和可扩展性至关重要。

## 2. 项目结构

遵循既定的项目结构，该结构符合常见的 Go 实践：

-   **`main.go`**: 应用入口点，Gin 初始化，主路由设置。
-   **`app/`**: 核心应用逻辑目录。
    -   **`controllers/`**: HTTP 请求处理器（Gin handlers）。连接路由与服务。保持控制器层轻薄。（例如 `ai_diagnosis_controller.go`）
    -   **`services/`**: 业务逻辑接口定义。（例如 `user_service.go`）
    -   **`services/impl/`**: 服务接口的具体实现。（例如 `user_service_impl.go`）
    -   **`dtos/`**: 数据传输对象（Data Transfer Objects），用于 API 请求/响应体。（例如 `ai_diagnosis_dto.go`）
    -   **`job/`**: 后台任务定义（如果有）。
    -   **`app.go`**: 应用设置，中间件注册。
-   **`base/`**: 应用中通用的基础结构（例如 `base.go` 定义 `ApiRes`）。
-   **`config/`**: 配置加载和管理。
-   **`docs/`**: 生成的 Swagger/OpenAPI 文档。
-   **`enums/`**: 枚举类型或常量定义（例如 `http/http.go` 用于响应辅助函数）。
-   **`kube/`**: Kubernetes 客户端交互逻辑。
-   **`logger/`**: 日志设置和工具。
-   **`models/`**: 数据模型（例如，数据库模型（如果适用），Swagger 辅助模型）。
-   **`rest/`**: 用于与外部 REST API 交互的客户端（例如 `feishu/`，通用客户端）。
-   **`router/`**: Gin 路由定义，将路径映射到控制器。（例如 `ai_diagnosis_router.go`）
-   **`utils/`**: 通用工具函数（例如 `jwt.go`, `helpers.go`）。
-   **`go.mod`, `go.sum`**: Go 模块依赖管理文件。
-   **`scripts/`**: 构建或实用工具脚本。

## 3. 命名规范

-   **包 (Packages):** `lowercase` (小写)，简短，描述性强（例如 `controllers`, `services`, `kube`）。
-   **文件:** `snake_case.go` (下划线分隔) （例如 `user_controller.go`, `api_response.go`）。
-   **结构体/接口:** `PascalCase` (大驼峰) （例如 `UserController`, `KubernetesService`）。
-   **函数/方法:** 导出的使用 `PascalCase`，未导出的使用 `camelCase` (小驼峰)。
-   **变量:** `camelCase`。对常用变量使用简短、符合 Go 习惯的名称（例如 `ctx` 代表 `context.Context`, `err` 代表 `error`, `w` 代表 `http.ResponseWriter`, `r` 代表 `*http.Request`, `db` 代表 `*sql.DB`）。
-   **常量:** 导出的常量使用 `PascalCase`（在 `enums` 或相关包内），未导出的使用 `camelCase`。
-   **DTOs:** 使用 `DTO` 后缀（例如 `UserLoginReqDTO`, `AIDiagnosisResponseDTO`）。

## 4. 编码风格与格式化

-   **格式化:** 严格使用 `gofmt` 和 `goimports`。配置 IDE 在保存时自动运行它们。
-   **Linting:** 使用 `golangci-lint` 及标准配置来强制执行代码风格并捕获常见错误。
-   **简洁性:** 编写简单、清晰、简洁的 Go 代码。避免不必要的复杂性或抽象。遵循 "Effective Go" 中的原则。
-   **错误处理:**
    -   在函数调用返回错误后立即显式检查错误 (`if err != nil { ... }`)。
    -   返回错误而不是 panic，除非在初始化过程中遇到真正异常/不可恢复的情况。
    -   使用 `fmt.Errorf("上下文信息: %w", err)` 来包装错误以提供上下文。使用 `errors.Is` 和 `errors.As` 来检查特定类型的错误。
-   **注释:**
    -   对所有导出的函数、类型和常量使用 Go 文档注释 (`// FunctionName 用于...`)。
    -   在必要时使用 `//` 解释实现细节的 *原因*。

## 5. API 设计 (RESTful)

-   **基础路径与版本控制:** 使用带版本的基础路径（例如 `/v1`）。
-   **资源命名:** 对资源集合使用复数名词（例如 `/v1/clusters`, `/v1/users`）。对于多词资源，统一使用 kebab-case (短横线) 或 snake_case (下划线)（与现有路由如 `_login` 对齐，或选择一个标准，如 `/v1/ai-diagnosis`）。
-   **端点 (Endpoints):**
    -   `GET /v1/resources`: 列出资源。
    -   `POST /v1/resources`: 创建新资源。
    -   `GET /v1/resources/{id}`: 获取特定资源。
    -   `PUT /v1/resources/{id}`: 完全替换资源。
    -   `PATCH /v1/resources/{id}`: 部分更新资源。
    -   `DELETE /v1/resources/{id}`: 删除资源。
    -   对于不符合 CRUD 的特定操作，使用动词（例如 `POST /v1/users/login`, `POST /v1/ai/analyze`）。
-   **请求 (Request):**
    -   请求体使用 JSON 格式 (`Content-Type: application/json`)。
    -   在 `app/dtos/` 中定义请求结构体。
    -   使用 Gin 的绑定功能 (`ctx.ShouldBindJSON()`) 进行解析和基础验证。
-   **响应 (Response):**
    -   所有 JSON 响应统一使用 `base.ApiRes` 结构。
        ```go
        type ApiRes struct {
            Data    interface{} `json:"data"`
            Result  string      `json:"result"` // "SUCCESS", "FAIL", "ERROR"
            Code    int         `json:"code"`   // HTTP 状态码或自定义业务码
            Message string      `json:"message,omitempty"`
            // ... 其他字段，如列表的 Page, Size, Total
        }
        ```
    -   使用辅助函数（如 `enums/http/` 中的函数）创建标准的成功/错误响应。
    -   恰当使用标准的 HTTP 状态码（200, 201, 400, 401, 403, 404, 500 等），并在 `ApiRes` 的 `code` 字段中反映出来。
-   **认证:** 使用包含 JWT 的 `X-AUTH` 请求头。`app/app.go` 中的中间件应处理 token 验证，并可能将用户信息添加到 Gin 上下文中。

## 6. 分层

-   **控制器层 (`app/controllers/`):** 保持轻薄。解析请求，调用服务，使用 `base.ApiRes` 处理响应格式化。不包含业务逻辑。
-   **服务层 (`app/services/`, `app/services/impl/`):** 包含业务逻辑。使用接口进行解耦。与数据层（K8s 客户端、外部 API、数据库等）交互。
-   **数据访问层 (`kube/`, `rest/`, `models/`):** 处理与外部系统（Kubernetes、其他 API、数据库）的交互。

## 7. 错误处理

-   服务层应将错误返回给控制器层。
-   控制器层应将服务层错误映射为合适的 `base.ApiRes` 响应（例如，`NotFound` 错误 -> 404 响应，验证错误 -> 400 响应）。
-   使用 `logger` 包记录错误，特别是意外错误（5xx 错误）。包含相关上下文（请求 ID，用户 ID 等）。

## 8. 配置

-   使用结构化的配置方法（例如 Viper 库，从 `config/config.yaml` 或环境变量加载）。
-   通过中心的 `config` 包/结构体访问配置值。

## 9. 测试

-   使用标准的 `testing` 包为服务层、工具函数和关键逻辑编写单元测试。
-   在单元测试中使用 mocks/stubs 模拟外部依赖（如 Kubernetes 客户端、外部 API）。
-   考虑编写涉及多个组件的集成测试（例如，控制器 -> 服务 -> 模拟 K8s）。

## 10. 文档

-   为所有导出的实体编写 Go 文档注释。
-   在控制器方法中使用 `swaggo` 注解（`// @Summary`, `// @Tags` 等）生成 OpenAPI/Swagger 文档。保持注解的更新。
-   确保生成的 `docs/` 文件已提交到版本库。

## 11. 依赖管理

-   使用 Go Modules (`go.mod`, `go.sum`) 进行依赖管理。
-   合理地保持依赖更新 (`go get -u`)。

## 12. 并发

-   负责任地使用 goroutines。
-   使用 `context.Context` 管理跨 API 边界和后台任务的取消和超时。
-   在处理共享状态时使用适当的同步原语（互斥锁、通道）。

## 13. Git 工作流

-   **分支:** 使用特性分支（例如 `feat/ai-diagnosis-endpoint`, `fix/k8s-event-fetch`）。从主开发分支（例如 `develop` 或 `main`）创建分支。
-   **提交:** 编写清晰、简洁的 commit 消息。考虑使用 Conventional Commits 格式（例如 `feat: add /ai/analyze endpoint`, `fix: handle empty namespace list`）。
-   **Pull Requests (PR):** 使用 PR 合并特性分支。合并前需要代码审查。
-   **合并:** 使用 squash 或 rebase 合并 PR，以保持主分支历史记录的整洁。
