{"name": "mysql", "description": "MySQL 数据库服务", "category": "数据库", "icon": "mysql-icon", "version": "8.0", "maintainer": "admin", "yamlTemplate": "apiVersion: apps/v1\nkind: Deployment\nmetadata:\n  name: {{.name}}\n  namespace: {{.namespace}}\n  labels:\n    app: {{.name}}\nspec:\n  replicas: {{.replicas}}\n  selector:\n    matchLabels:\n      app: {{.name}}\n  template:\n    metadata:\n      labels:\n        app: {{.name}}\n    spec:\n      containers:\n      - name: {{.name}}\n        image: {{.image}}\n        env:\n        - name: MYSQL_ROOT_PASSWORD\n          value: \"{{.rootPassword}}\"\n{{if .createDatabase}}\n        - name: MYSQL_DATABASE\n          value: \"{{.databaseName}}\"\n{{end}}\n{{if .createUser}}\n        - name: MYSQL_USER\n          value: \"{{.username}}\"\n        - name: MYSQL_PASSWORD\n          value: \"{{.password}}\"\n{{end}}\n        ports:\n        - containerPort: {{.port}}\n        resources:\n          requests:\n            memory: \"{{.memoryRequest}}\"\n            cpu: \"{{.cpuRequest}}\"\n          limits:\n            memory: \"{{.memoryLimit}}\"\n            cpu: \"{{.cpuLimit}}\"\n{{if .enablePersistence}}\n        volumeMounts:\n        - name: mysql-data\n          mountPath: /var/lib/mysql\n      volumes:\n      - name: mysql-data\n        persistentVolumeClaim:\n          claimName: {{.pvcName}}\n---\napiVersion: v1\nkind: PersistentVolumeClaim\nmetadata:\n  name: {{.pvcName}}\n  namespace: {{.namespace}}\nspec:\n  accessModes:\n    - ReadWriteOnce\n  resources:\n    requests:\n      storage: {{.storageSize}}\n  storageClassName: {{.storageClassName}}\n{{end}}\n{{if .enableService}}\n---\napiVersion: v1\nkind: Service\nmetadata:\n  name: {{.name}}\n  namespace: {{.namespace}}\nspec:\n  selector:\n    app: {{.name}}\n  ports:\n  - port: {{.servicePort}}\n    targetPort: {{.port}}\n  type: {{.serviceType}}\n{{end}}", "variables": [{"name": "name", "label": "应用名称", "description": "MySQL 应用的名称", "default_value": "mysql", "required": true, "type": "string"}, {"name": "namespace", "label": "命名空间", "description": "部署到的 Kubernetes 命名空间", "default_value": "default", "required": true, "type": "string"}, {"name": "replicas", "label": "副本数", "description": "部署的 Pod 副本数量", "default_value": 1, "required": true, "type": "number"}, {"name": "image", "label": "镜像", "description": "MySQL 容器镜像", "default_value": "mysql:8.0", "required": true, "type": "string"}, {"name": "port", "label": "容器端口", "description": "MySQL 容器暴露的端口", "default_value": 3306, "required": true, "type": "number"}, {"name": "rootPassword", "label": "Root密码", "description": "MySQL Root用户密码", "default_value": "password", "required": true, "type": "string"}, {"name": "createDatabase", "label": "创建数据库", "description": "是否创建初始数据库", "default_value": true, "required": false, "type": "boolean"}, {"name": "databaseName", "label": "数据库名称", "description": "要创建的初始数据库名称", "default_value": "mydb", "required": false, "type": "string"}, {"name": "createUser", "label": "创建用户", "description": "是否创建普通用户", "default_value": false, "required": false, "type": "boolean"}, {"name": "username", "label": "用户名", "description": "MySQL 用户名", "default_value": "user", "required": false, "type": "string"}, {"name": "password", "label": "密码", "description": "MySQL 密码", "default_value": "password", "required": false, "type": "string"}, {"name": "memoryRequest", "label": "内存请求", "description": "容器的内存请求", "default_value": "256Mi", "required": true, "type": "string"}, {"name": "cpuRequest", "label": "CPU请求", "description": "容器的CPU请求", "default_value": "200m", "required": true, "type": "string"}, {"name": "memoryLimit", "label": "内存限制", "description": "容器的内存限制", "default_value": "512Mi", "required": true, "type": "string"}, {"name": "cpuLimit", "label": "CPU限制", "description": "容器的CPU限制", "default_value": "500m", "required": true, "type": "string"}, {"name": "enableService", "label": "启用服务", "description": "是否创建 Kubernetes Service", "default_value": true, "required": true, "type": "boolean"}, {"name": "servicePort", "label": "服务端口", "description": "Service 对外暴露的端口", "default_value": 3306, "required": false, "type": "number"}, {"name": "serviceType", "label": "服务类型", "description": "Kubernetes Service 类型", "default_value": "ClusterIP", "required": false, "type": "select", "options": [{"label": "ClusterIP", "value": "ClusterIP"}, {"label": "NodePort", "value": "NodePort"}, {"label": "LoadBalancer", "value": "LoadBalancer"}]}, {"name": "enablePersistence", "label": "启用持久化", "description": "是否启用持久化存储", "default_value": true, "required": false, "type": "boolean"}, {"name": "pvcName", "label": "PVC名称", "description": "持久卷声明名称", "default_value": "mysql-data", "required": false, "type": "string"}, {"name": "storageSize", "label": "存储大小", "description": "持久卷大小", "default_value": "1Gi", "required": false, "type": "string"}, {"name": "storageClassName", "label": "存储类名称", "description": "Kubernetes存储类名称", "default_value": "standard", "required": false, "type": "string"}]}