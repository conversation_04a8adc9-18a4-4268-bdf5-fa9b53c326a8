{"name": "nginx", "description": "Nginx Web Server", "category": "Web服务器", "icon": "nginx-icon", "version": "1.19", "maintainer": "admin", "yamlTemplate": "apiVersion: apps/v1\nkind: Deployment\nmetadata:\n  name: {{.name}}\n  namespace: {{.namespace}}\n  labels:\n    app: {{.name}}\nspec:\n  replicas: {{.replicas}}\n  selector:\n    matchLabels:\n      app: {{.name}}\n  template:\n    metadata:\n      labels:\n        app: {{.name}}\n    spec:\n      containers:\n      - name: {{.name}}\n        image: {{.image}}\n        ports:\n        - containerPort: {{.port}}\n        resources:\n          requests:\n            memory: \"{{.memoryRequest}}\"\n            cpu: \"{{.cpuRequest}}\"\n          limits:\n            memory: \"{{.memoryLimit}}\"\n            cpu: \"{{.cpuLimit}}\"\n{{if .enableService}}\n---\napiVersion: v1\nkind: Service\nmetadata:\n  name: {{.name}}\n  namespace: {{.namespace}}\nspec:\n  selector:\n    app: {{.name}}\n  ports:\n  - port: {{.servicePort}}\n    targetPort: {{.port}}\n  type: {{.serviceType}}\n{{end}}", "variables": [{"name": "name", "label": "应用名称", "description": "Nginx 应用的名称", "default_value": "nginx", "required": true, "type": "string"}, {"name": "namespace", "label": "命名空间", "description": "部署到的 Kubernetes 命名空间", "default_value": "default", "required": true, "type": "string"}, {"name": "replicas", "label": "副本数", "description": "部署的 Pod 副本数量", "default_value": 1, "required": true, "type": "number"}, {"name": "image", "label": "镜像", "description": "Nginx 容器镜像", "default_value": "nginx:1.19", "required": true, "type": "string"}, {"name": "port", "label": "容器端口", "description": "Nginx 容器暴露的端口", "default_value": 80, "required": true, "type": "number"}, {"name": "memoryRequest", "label": "内存请求", "description": "容器的内存请求", "default_value": "128Mi", "required": true, "type": "string"}, {"name": "cpuRequest", "label": "CPU请求", "description": "容器的CPU请求", "default_value": "100m", "required": true, "type": "string"}, {"name": "memoryLimit", "label": "内存限制", "description": "容器的内存限制", "default_value": "256Mi", "required": true, "type": "string"}, {"name": "cpuLimit", "label": "CPU限制", "description": "容器的CPU限制", "default_value": "200m", "required": true, "type": "string"}, {"name": "enableService", "label": "启用服务", "description": "是否创建 Kubernetes Service", "default_value": true, "required": true, "type": "boolean"}, {"name": "servicePort", "label": "服务端口", "description": "Service 对外暴露的端口", "default_value": 80, "required": false, "type": "number"}, {"name": "serviceType", "label": "服务类型", "description": "Kubernetes Service 类型", "default_value": "ClusterIP", "required": false, "type": "select", "options": [{"label": "ClusterIP", "value": "ClusterIP"}, {"label": "NodePort", "value": "NodePort"}, {"label": "LoadBalancer", "value": "LoadBalancer"}]}]}