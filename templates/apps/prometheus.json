{"name": "prometheus", "description": "Prometheus 监控系统", "category": "监控", "icon": "prometheus-icon", "version": "2.30.3", "maintainer": "admin", "yamlTemplate": "apiVersion: apps/v1\nkind: Deployment\nmetadata:\n  name: {{.name}}\n  namespace: {{.namespace}}\n  labels:\n    app: {{.name}}\nspec:\n  replicas: {{.replicas}}\n  selector:\n    matchLabels:\n      app: {{.name}}\n  template:\n    metadata:\n      labels:\n        app: {{.name}}\n    spec:\n      containers:\n      - name: {{.name}}\n        image: {{.image}}\n        ports:\n        - containerPort: {{.port}}\n        resources:\n          requests:\n            memory: \"{{.memoryRequest}}\"\n            cpu: \"{{.cpuRequest}}\"\n          limits:\n            memory: \"{{.memoryLimit}}\"\n            cpu: \"{{.cpuLimit}}\"\n        volumeMounts:\n        - name: config-volume\n          mountPath: /etc/prometheus/\n      volumes:\n      - name: config-volume\n        configMap:\n          name: {{.configMapName}}\n---\napiVersion: v1\nkind: ConfigMap\nmetadata:\n  name: {{.configMapName}}\n  namespace: {{.namespace}}\ndata:\n  prometheus.yml: |\n    global:\n      scrape_interval: {{.scrapeInterval}}\n      evaluation_interval: {{.evaluationInterval}}\n    alerting:\n      alertmanagers:\n      - static_configs:\n        - targets:\n        {{if .alertmanagerEnabled}}  - \"{{.alertmanagerHost}}:{{.alertmanagerPort}}\"\n        {{end}}\n    rule_files:\n      # - \"first.rules\"\n    scrape_configs:\n      - job_name: prometheus\n        static_configs:\n        - targets: [\"localhost:9090\"]\n      - job_name: kubernetes\n        kubernetes_sd_configs:\n        - role: node\n{{if .enableService}}\n---\napiVersion: v1\nkind: Service\nmetadata:\n  name: {{.name}}\n  namespace: {{.namespace}}\nspec:\n  selector:\n    app: {{.name}}\n  ports:\n  - port: {{.servicePort}}\n    targetPort: {{.port}}\n  type: {{.serviceType}}\n{{end}}", "variables": [{"name": "name", "label": "应用名称", "description": "Prometheus 应用的名称", "default_value": "prometheus", "required": true, "type": "string"}, {"name": "namespace", "label": "命名空间", "description": "部署到的 Kubernetes 命名空间", "default_value": "monitoring", "required": true, "type": "string"}, {"name": "replicas", "label": "副本数", "description": "部署的 Pod 副本数量", "default_value": 1, "required": true, "type": "number"}, {"name": "image", "label": "镜像", "description": "Prometheus 容器镜像", "default_value": "prom/prometheus:v2.30.3", "required": true, "type": "string"}, {"name": "port", "label": "容器端口", "description": "Prometheus 容器暴露的端口", "default_value": 9090, "required": true, "type": "number"}, {"name": "memoryRequest", "label": "内存请求", "description": "容器的内存请求", "default_value": "256Mi", "required": true, "type": "string"}, {"name": "cpuRequest", "label": "CPU请求", "description": "容器的CPU请求", "default_value": "200m", "required": true, "type": "string"}, {"name": "memoryLimit", "label": "内存限制", "description": "容器的内存限制", "default_value": "512Mi", "required": true, "type": "string"}, {"name": "cpuLimit", "label": "CPU限制", "description": "容器的CPU限制", "default_value": "500m", "required": true, "type": "string"}, {"name": "enableService", "label": "启用服务", "description": "是否创建 Kubernetes Service", "default_value": true, "required": true, "type": "boolean"}, {"name": "servicePort", "label": "服务端口", "description": "Service 对外暴露的端口", "default_value": 9090, "required": false, "type": "number"}, {"name": "serviceType", "label": "服务类型", "description": "Kubernetes Service 类型", "default_value": "ClusterIP", "required": false, "type": "select", "options": [{"label": "ClusterIP", "value": "ClusterIP"}, {"label": "NodePort", "value": "NodePort"}, {"label": "LoadBalancer", "value": "LoadBalancer"}]}, {"name": "configMapName", "label": "ConfigMap名称", "description": "Prometheus配置的ConfigMap名称", "default_value": "prometheus-config", "required": true, "type": "string"}, {"name": "scrapeInterval", "label": "采集间隔", "description": "Prometheus采集数据的间隔时间", "default_value": "15s", "required": true, "type": "string"}, {"name": "evaluationInterval", "label": "规则评估间隔", "description": "Prometheus规则评估的间隔时间", "default_value": "15s", "required": true, "type": "string"}, {"name": "alertmanagerEnabled", "label": "启用Alertmanager", "description": "是否配置Alertmanager", "default_value": false, "required": false, "type": "boolean"}, {"name": "alertmanagerHost", "label": "Alertmanager主机", "description": "Alertmanager的主机地址", "default_value": "alertmanager", "required": false, "type": "string"}, {"name": "alertmanagerPort", "label": "Alertmanager端口", "description": "Alertmanager的端口", "default_value": 9093, "required": false, "type": "number"}]}