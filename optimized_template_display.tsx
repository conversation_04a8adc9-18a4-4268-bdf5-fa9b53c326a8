import React, { useState, useMemo } from 'react';
import {
  Modal,
  Card,
  Input,
  Select,
  Tag,
  Button,
  Space,
  Typography,
  Divider,
  Badge,
  Tooltip,
  Empty,
  Row,
  Col,
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  QuestionCircleOutlined,
  StarOutlined,
  ClockCircleOutlined,
  TagsOutlined,
} from '@ant-design/icons';
import { QuickTemplate } from '@/services/aiAssistant';
import styles from './OptimizedTemplateDisplay.less';

const { Text, Title, Paragraph } = Typography;
const { Option } = Select;

interface OptimizedTemplateDisplayProps {
  visible: boolean;
  templates: QuickTemplate[];
  onClose: () => void;
  onSelectTemplate: (template: QuickTemplate) => void;
  theme?: 'light' | 'dark';
}

// 模板分类配置
const TEMPLATE_CATEGORIES = [
  { key: '故障排查', label: '故障排查', color: '#f50', icon: '🔧' },
  { key: '性能优化', label: '性能优化', color: '#2db7f5', icon: '⚡' },
  { key: '安全检查', label: '安全检查', color: '#87d068', icon: '🔒' },
  { key: '资源管理', label: '资源管理', color: '#108ee9', icon: '📊' },
  { key: '部署运维', label: '部署运维', color: '#f5222d', icon: '🚀' },
  { key: '监控告警', label: '监控告警', color: '#fa8c16', icon: '📈' },
  { key: '集群管理', label: '集群管理', color: '#722ed1', icon: '🏗️' },
  { key: '平台使用', label: '平台使用', color: '#13c2c2', icon: '💡' },
];

const OptimizedTemplateDisplay: React.FC<OptimizedTemplateDisplayProps> = ({
  visible,
  templates,
  onClose,
  onSelectTemplate,
  theme = 'light',
}) => {
  const [searchValue, setSearchValue] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [sortBy, setSortBy] = useState<'priority' | 'usage' | 'recent'>('priority');

  // 过滤和排序模板
  const filteredAndSortedTemplates = useMemo(() => {
    let filtered = templates;

    // 按分类过滤
    if (selectedCategory) {
      filtered = filtered.filter(template => template.category === selectedCategory);
    }

    // 按搜索关键词过滤
    if (searchValue) {
      const searchLower = searchValue.toLowerCase();
      filtered = filtered.filter(template =>
        template.title.toLowerCase().includes(searchLower) ||
        template.description.toLowerCase().includes(searchLower) ||
        template.content.toLowerCase().includes(searchLower)
      );
    }

    // 排序
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'priority':
          return (a.priority || 999) - (b.priority || 999);
        case 'usage':
          return (b.usageCount || 0) - (a.usageCount || 0);
        case 'recent':
          return (b.updatedAt || 0) - (a.updatedAt || 0);
        default:
          return 0;
      }
    });

    return filtered;
  }, [templates, selectedCategory, searchValue, sortBy]);

  // 按分类分组模板
  const groupedTemplates = useMemo(() => {
    const groups: Record<string, QuickTemplate[]> = {};
    filteredAndSortedTemplates.forEach(template => {
      if (!groups[template.category]) {
        groups[template.category] = [];
      }
      groups[template.category].push(template);
    });
    return groups;
  }, [filteredAndSortedTemplates]);

  // 获取分类配置
  const getCategoryConfig = (category: string) => {
    return TEMPLATE_CATEGORIES.find(cat => cat.key === category) || {
      key: category,
      label: category,
      color: '#666',
      icon: '📝'
    };
  };

  // 渲染模板卡片
  const renderTemplateCard = (template: QuickTemplate) => {
    const categoryConfig = getCategoryConfig(template.category);
    
    return (
      <Card
        key={template.id}
        className={`${styles.templateCard} ${theme === 'dark' ? styles.darkCard : ''}`}
        hoverable
        onClick={() => onSelectTemplate(template)}
        actions={[
          <Tooltip title="使用模板">
            <Button
              type="link"
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                onSelectTemplate(template);
              }}
            >
              使用模板
            </Button>
          </Tooltip>,
          <Tooltip title="预览内容">
            <Button
              type="link"
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                // 可以添加预览功能
              }}
            >
              预览
            </Button>
          </Tooltip>
        ]}
      >
        <div className={styles.cardHeader}>
          <div className={styles.cardTitle}>
            <Space>
              <span className={styles.categoryIcon}>{categoryConfig.icon}</span>
              <Text strong className={styles.titleText}>
                {template.title}
              </Text>
              {template.priority === 1 && (
                <Badge count="推荐" style={{ backgroundColor: '#52c41a' }} />
              )}
            </Space>
          </div>
          <div className={styles.cardMeta}>
            <Space size="small">
              <Tag color={categoryConfig.color} className={styles.categoryTag}>
                {template.category}
              </Tag>
              {template.usageCount && template.usageCount > 0 && (
                <Tooltip title="使用次数">
                  <Tag icon={<StarOutlined />} color="gold">
                    {template.usageCount}
                  </Tag>
                </Tooltip>
              )}
            </Space>
          </div>
        </div>

        <div className={styles.cardContent}>
          <Paragraph
            className={styles.description}
            ellipsis={{ rows: 2, expandable: false }}
          >
            {template.description}
          </Paragraph>

          <div className={styles.contentPreview}>
            <Text code className={styles.previewText}>
              {template.content.length > 150
                ? `${template.content.substring(0, 150)}...`
                : template.content
              }
            </Text>
          </div>

          {template.tags && template.tags.length > 0 && (
            <div className={styles.tags}>
              <TagsOutlined className={styles.tagsIcon} />
              <Space size="small" wrap>
                {template.tags.map(tag => (
                  <Tag key={tag} size="small" className={styles.tag}>
                    {tag}
                  </Tag>
                ))}
              </Space>
            </div>
          )}
        </div>
      </Card>
    );
  };

  return (
    <Modal
      title={
        <div className={styles.modalHeader}>
          <Space>
            <QuestionCircleOutlined className={styles.modalIcon} />
            <Title level={4} className={styles.modalTitle}>
              快速问题模板
            </Title>
            <Badge count={filteredAndSortedTemplates.length} showZero />
          </Space>
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1200}
      className={`${styles.templateModal} ${theme === 'dark' ? styles.darkModal : ''}`}
      destroyOnClose
    >
      {/* 搜索和过滤工具栏 */}
      <div className={styles.toolbar}>
        <Row gutter={16} align="middle">
          <Col flex="auto">
            <Input
              placeholder="搜索模板标题、描述或内容..."
              prefix={<SearchOutlined />}
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              allowClear
              className={styles.searchInput}
            />
          </Col>
          <Col>
            <Select
              placeholder="选择分类"
              value={selectedCategory}
              onChange={setSelectedCategory}
              allowClear
              style={{ width: 150 }}
              suffixIcon={<FilterOutlined />}
            >
              {TEMPLATE_CATEGORIES.map(category => (
                <Option key={category.key} value={category.key}>
                  <Space>
                    <span>{category.icon}</span>
                    {category.label}
                  </Space>
                </Option>
              ))}
            </Select>
          </Col>
          <Col>
            <Select
              value={sortBy}
              onChange={setSortBy}
              style={{ width: 120 }}
            >
              <Option value="priority">按优先级</Option>
              <Option value="usage">按使用量</Option>
              <Option value="recent">按更新时间</Option>
            </Select>
          </Col>
        </Row>
      </div>

      <Divider />

      {/* 模板内容 */}
      <div className={styles.templatesContainer}>
        {filteredAndSortedTemplates.length === 0 ? (
          <Empty
            description="未找到匹配的模板"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        ) : (
          <div className={styles.templatesContent}>
            {selectedCategory ? (
              // 单分类显示
              <div className={styles.templateGrid}>
                {filteredAndSortedTemplates.map(renderTemplateCard)}
              </div>
            ) : (
              // 分组显示
              Object.entries(groupedTemplates).map(([category, categoryTemplates]) => {
                const categoryConfig = getCategoryConfig(category);
                return (
                  <div key={category} className={styles.categorySection}>
                    <div className={styles.categoryHeader}>
                      <Space>
                        <span className={styles.categoryIcon}>{categoryConfig.icon}</span>
                        <Title level={5} className={styles.categoryTitle}>
                          {categoryConfig.label}
                        </Title>
                        <Badge count={categoryTemplates.length} showZero />
                      </Space>
                    </div>
                    <div className={styles.templateGrid}>
                      {categoryTemplates.map(renderTemplateCard)}
                    </div>
                  </div>
                );
              })
            )}
          </div>
        )}
      </div>

      {/* 统计信息 */}
      <div className={styles.footer}>
        <Space split={<Divider type="vertical" />}>
          <Text type="secondary">
            共 {templates.length} 个模板
          </Text>
          <Text type="secondary">
            显示 {filteredAndSortedTemplates.length} 个结果
          </Text>
          <Text type="secondary">
            覆盖 {TEMPLATE_CATEGORIES.length} 个分类
          </Text>
        </Space>
      </div>
    </Modal>
  );
};

export default OptimizedTemplateDisplay;
