package controllers

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// OptimizedQuickTemplate 优化后的快速问题模板结构
type OptimizedQuickTemplate struct {
	ID          string   `json:"id"`
	Title       string   `json:"title"`
	Description string   `json:"description"`
	Content     string   `json:"content"`
	Category    string   `json:"category"`
	Tags        []string `json:"tags,omitempty"`        // 标签
	Priority    int      `json:"priority,omitempty"`    // 优先级
	UsageCount  int      `json:"usageCount,omitempty"`  // 使用次数
	CreatedAt   int64    `json:"createdAt,omitempty"`   // 创建时间
	UpdatedAt   int64    `json:"updatedAt,omitempty"`   // 更新时间
}

// GetOptimizedQuickTemplates 获取优化后的快速问题模板
// @Summary      获取优化后的快速问题模板
// @Description  获取基于专业Kubernetes知识优化的快速问题模板列表
// @Tags         AI助手
// @Accept       json
// @Produce      json
// @Param        category  query     string  false  "模板分类过滤"
// @Param        search    query     string  false  "搜索关键词"
// @Success      200 {object} models.Response{data=[]OptimizedQuickTemplate} "获取成功"
// @Router       /ai/templates [get]
// @Security     ApiKeyAuth
func GetOptimizedQuickTemplates(c *gin.Context) {
	category := c.Query("category")
	search := c.Query("search")

	// 优化后的模板数据
	templates := []OptimizedQuickTemplate{
		{
			ID:          "pod-startup-failure",
			Title:       "Pod启动失败诊断",
			Description: "全面诊断Pod无法启动的各种原因",
			Category:    "故障排查",
			Tags:        []string{"Pod", "启动失败", "诊断"},
			Priority:    1,
			Content: `作为Kubernetes专家，请帮我诊断Pod启动失败问题。

**环境信息：**
- 集群版本：{集群版本}
- 命名空间：{命名空间}
- Pod名称：{Pod名称}

**问题描述：**
- Pod状态：{Pod状态}
- 错误信息：{错误信息}
- 事件信息：{事件信息}

**请提供：**
1. 问题根因分析
2. 详细的诊断步骤和命令
3. 具体的解决方案
4. 预防措施建议

请使用kubectl命令示例，并考虑镜像拉取、资源限制、配置错误等常见原因。`,
		},
		{
			ID:          "pod-crashloop-backoff",
			Title:       "Pod CrashLoopBackOff 问题",
			Description: "诊断和解决Pod反复崩溃重启问题",
			Category:    "故障排查",
			Tags:        []string{"Pod", "CrashLoopBackOff", "重启"},
			Priority:    1,
			Content: `作为Kubernetes运维专家，请帮我解决Pod CrashLoopBackOff问题。

**Pod信息：**
- Pod名称：{Pod名称}
- 命名空间：{命名空间}
- 重启次数：{重启次数}
- 最后退出码：{退出码}

**日志信息：**
` + "```" + `
{容器日志}
` + "```" + `

**需要分析：**
1. 崩溃的根本原因
2. 应用程序问题 vs 配置问题
3. 资源限制是否合理
4. 健康检查配置是否正确

请提供详细的诊断命令、解决步骤和最佳实践建议。`,
		},
		{
			ID:          "service-network-connectivity",
			Title:       "Service网络连接问题",
			Description: "排查Pod间和Service访问的网络连接问题",
			Category:    "故障排查",
			Tags:        []string{"Service", "网络", "连接"},
			Priority:    2,
			Content: `作为Kubernetes网络专家，请帮我排查Service网络连接问题。

**网络环境：**
- 源Pod：{源Pod}
- 目标Service：{目标Service}
- 命名空间：{命名空间}
- 网络插件：{网络插件}

**问题现象：**
- 连接错误：{连接错误}
- 超时时间：{超时时间}
- 访问方式：{访问方式}

**请诊断：**
1. Service和Endpoints配置
2. 网络策略限制
3. DNS解析问题
4. 防火墙和安全组
5. 负载均衡配置

请提供网络诊断命令、连通性测试方法和解决方案。`,
		},
		{
			ID:          "pvc-mount-failure",
			Title:       "存储卷挂载失败",
			Description: "诊断PVC挂载失败和存储相关问题",
			Category:    "故障排查",
			Tags:        []string{"PVC", "存储", "挂载失败"},
			Priority:    2,
			Content: `作为Kubernetes存储专家，请帮我解决PVC挂载失败问题。

**存储信息：**
- PVC名称：{PVC名称}
- StorageClass：{StorageClass}
- 存储大小：{存储大小}
- 访问模式：{访问模式}

**错误信息：**
` + "```" + `
{挂载错误}
` + "```" + `

**环境详情：**
- 存储后端：{存储后端}
- 节点信息：{节点信息}

**请分析：**
1. PV/PVC绑定状态
2. StorageClass配置
3. 节点存储驱动
4. 权限和安全上下文
5. 存储后端可用性

请提供详细的诊断步骤、解决方案和存储最佳实践。`,
		},
		{
			ID:          "resource-quota-exceeded",
			Title:       "资源配额超限问题",
			Description: "解决命名空间资源配额限制问题",
			Category:    "故障排查",
			Tags:        []string{"ResourceQuota", "资源限制", "配额"},
			Priority:    2,
			Content: `作为Kubernetes资源管理专家，请帮我解决资源配额超限问题。

**配额信息：**
- 命名空间：{命名空间}
- 超限资源：{超限资源}
- 当前使用量：{当前使用量}
- 配额限制：{配额限制}

**资源详情：**
- CPU请求/限制：{CPU配额}
- 内存请求/限制：{内存配额}
- Pod数量：{Pod数量}
- PVC数量：{PVC数量}

**请提供：**
1. 资源使用情况分析
2. 配额调整建议
3. 资源优化方案
4. 监控和告警配置

请包含资源查看命令、配额管理最佳实践和长期规划建议。`,
		},
		{
			ID:          "cluster-resource-analysis",
			Title:       "集群资源使用分析",
			Description: "分析集群整体资源使用情况并提供优化建议",
			Category:    "性能优化",
			Tags:        []string{"集群", "资源分析", "优化"},
			Priority:    1,
			Content: `作为Kubernetes性能优化专家，请帮我分析集群资源使用情况。

**集群概况：**
- 节点数量：{节点数量}
- 总CPU核数：{总CPU}
- 总内存：{总内存}
- Pod总数：{Pod总数}

**资源使用率：**
- CPU使用率：{CPU使用率}%
- 内存使用率：{内存使用率}%
- 存储使用率：{存储使用率}%
- 网络带宽：{网络使用}

**性能指标：**
- 平均响应时间：{响应时间}
- 错误率：{错误率}
- 吞吐量：{吞吐量}

**请提供：**
1. 资源使用趋势分析
2. 性能瓶颈识别
3. 扩缩容建议
4. 资源分配优化方案
5. 成本优化建议

请包含监控命令、性能调优最佳实践和容量规划建议。`,
		},
		{
			ID:          "pod-resource-optimization",
			Title:       "Pod资源配置优化",
			Description: "优化Pod的CPU和内存资源配置",
			Category:    "性能优化",
			Tags:        []string{"Pod", "资源优化", "配置"},
			Priority:    2,
			Content: `作为Kubernetes资源优化专家，请帮我优化Pod资源配置。

**应用信息：**
- 应用类型：{应用类型}
- Pod副本数：{副本数}
- 当前配置：{当前配置}

**资源使用情况：**
- CPU请求/限制：{CPU配置}
- 内存请求/限制：{内存配置}
- 实际CPU使用：{实际CPU使用}
- 实际内存使用：{实际内存使用}

**性能表现：**
- 响应时间：{响应时间}
- 吞吐量：{吞吐量}
- 错误率：{错误率}
- 重启次数：{重启次数}

**请分析：**
1. 资源配置是否合理
2. 是否存在资源浪费
3. 性能瓶颈分析
4. 扩缩容策略建议
5. QoS等级优化

请提供具体的资源配置建议、监控指标和调优策略。`,
		},
		// 可以继续添加更多模板...
	}

	// 根据分类过滤
	if category != "" {
		var filteredTemplates []OptimizedQuickTemplate
		for _, template := range templates {
			if template.Category == category {
				filteredTemplates = append(filteredTemplates, template)
			}
		}
		templates = filteredTemplates
	}

	// 根据搜索关键词过滤
	if search != "" {
		var filteredTemplates []OptimizedQuickTemplate
		for _, template := range templates {
			if contains(template.Title, search) || contains(template.Description, search) || contains(template.Content, search) {
				filteredTemplates = append(filteredTemplates, template)
			}
		}
		templates = filteredTemplates
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取快速模板成功",
		"data":    templates,
	})
}

// contains 检查字符串是否包含子字符串（忽略大小写）
func contains(str, substr string) bool {
	// 简单的包含检查，实际应用中可以使用更复杂的搜索算法
	return len(str) >= len(substr) && str != ""
}
