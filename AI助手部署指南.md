# Blops AI助手部署指南

## 部署步骤

### 1. 后端部署

#### 1.1 确认依赖
确保Go环境已安装并且版本 >= 1.20

#### 1.2 安装依赖
```bash
cd blops
go mod tidy
```

#### 1.3 配置Gemini API
在`blops/app/controllers/ai_diagnosis_controller.go`中配置API密钥：

```go
// 注意：实际部署时应从环境变量或配置文件获取
apiKey := os.Getenv("GEMINI_API_KEY") // 推荐方式
// 或者从配置文件读取
```

#### 1.4 编译运行
```bash
go build -o blops main.go
./blops
```

服务将在端口3000启动。

### 2. 前端部署

#### 2.1 安装依赖
```bash
cd blops-web
npm install
# 或者
yarn install
```

#### 2.2 开发环境运行
```bash
npm run start:dev
# 或者
yarn start:dev
```

#### 2.3 生产环境构建
```bash
npm run build
# 或者
yarn build
```

### 3. 验证部署

#### 3.1 后端API测试
```bash
# 测试AI聊天接口
curl -X POST http://localhost:3000/api/ai/chat \
  -H "Content-Type: application/json" \
  -H "X-AUTH: your-token" \
  -d '{
    "message": "你好，请介绍一下Kubernetes",
    "sessionId": "test-session"
  }'

# 测试快速模板接口
curl -X GET http://localhost:3000/api/ai/templates \
  -H "X-AUTH: your-token"
```

#### 3.2 前端功能测试
1. 访问前端应用
2. 登录系统
3. 查看右下角是否显示AI助手悬浮按钮
4. 点击按钮测试聊天功能

## 配置说明

### 1. 环境变量配置

创建`.env`文件：

```bash
# Gemini API配置
GEMINI_API_KEY=your-gemini-api-key
GEMINI_API_URL=https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent

# 调试模式
DEBUG_LOG_ENABLED=true

# 聊天历史限制
MAX_CHAT_HISTORY=20
```

### 2. 后端配置优化

在`ai_diagnosis_controller.go`中添加配置读取：

```go
import "os"

var (
    geminiAPIKey = getEnvOrDefault("GEMINI_API_KEY", "default-key")
    debugLogEnabled = getEnvOrDefault("DEBUG_LOG_ENABLED", "false") == "true"
    maxChatHistory = getIntEnvOrDefault("MAX_CHAT_HISTORY", 20)
)

func getEnvOrDefault(key, defaultValue string) string {
    if value := os.Getenv(key); value != "" {
        return value
    }
    return defaultValue
}
```

### 3. 前端配置

在`blops-web/config/proxy.ts`中确保AI接口代理配置：

```typescript
export default {
  dev: {
    '/api/ai': {
      target: 'http://localhost:3000',
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
  },
};
```

## 性能优化

### 1. 后端优化

#### 1.1 连接池配置
```go
// 在main.go中配置HTTP客户端连接池
var httpClient = &http.Client{
    Timeout: 120 * time.Second,
    Transport: &http.Transport{
        MaxIdleConns:        100,
        MaxIdleConnsPerHost: 10,
        IdleConnTimeout:     90 * time.Second,
    },
}
```

#### 1.2 聊天历史优化
```go
// 定期清理过期会话
func cleanupExpiredSessions() {
    chatMutex.Lock()
    defer chatMutex.Unlock()
    
    now := time.Now().Unix()
    for sessionID, history := range chatHistories {
        // 清理24小时前的会话
        if now-history.UpdatedAt > 24*3600 {
            delete(chatHistories, sessionID)
        }
    }
}
```

### 2. 前端优化

#### 2.1 懒加载
```typescript
// 使用React.lazy懒加载AI助手组件
const AIAssistant = React.lazy(() => import('@/components/AIAssistant'));

// 在app.tsx中使用Suspense包装
<Suspense fallback={<div>Loading...</div>}>
  <AIAssistant />
</Suspense>
```

#### 2.2 防抖优化
```typescript
// 在输入时添加防抖
import { debounce } from 'lodash';

const debouncedSend = debounce(sendMessage, 300);
```

## 监控和日志

### 1. 后端日志
```go
// 添加结构化日志
import "github.com/sirupsen/logrus"

logrus.WithFields(logrus.Fields{
    "sessionId": sessionID,
    "messageLength": len(req.Message),
    "responseTime": time.Since(startTime),
}).Info("AI chat completed")
```

### 2. 前端错误监控
```typescript
// 在aiAssistant.ts中添加错误上报
const reportError = (error: Error, context: string) => {
  console.error(`AI Assistant Error [${context}]:`, error);
  // 可以集成Sentry等错误监控服务
};
```

## 安全配置

### 1. API安全
```go
// 添加请求频率限制
import "golang.org/x/time/rate"

var limiter = rate.NewLimiter(rate.Every(time.Second), 10) // 每秒最多10个请求

func rateLimitMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        if !limiter.Allow() {
            c.JSON(http.StatusTooManyRequests, gin.H{
                "code": 429,
                "message": "请求过于频繁，请稍后再试",
            })
            c.Abort()
            return
        }
        c.Next()
    }
}
```

### 2. 输入验证
```go
// 添加输入内容验证
func validateChatInput(message string) error {
    if len(message) > 2000 {
        return errors.New("消息内容过长")
    }
    
    // 检查敏感词汇
    sensitiveWords := []string{"password", "secret", "key"}
    lowerMessage := strings.ToLower(message)
    for _, word := range sensitiveWords {
        if strings.Contains(lowerMessage, word) {
            return errors.New("消息包含敏感信息")
        }
    }
    
    return nil
}
```

## 故障排除

### 1. 常见问题

#### 问题1: AI助手按钮不显示
**解决方案:**
1. 检查用户是否已登录
2. 确认不在登录页面
3. 检查浏览器控制台错误

#### 问题2: 发送消息失败
**解决方案:**
1. 检查后端服务是否运行
2. 验证Gemini API密钥是否有效
3. 查看网络连接状态

#### 问题3: AI回复异常
**解决方案:**
1. 检查后端日志
2. 验证API请求格式
3. 确认Gemini API配额

### 2. 调试工具

#### 后端调试
```bash
# 启用详细日志
export DEBUG_LOG_ENABLED=true
./blops

# 查看API调用日志
tail -f /var/log/blops/api.log
```

#### 前端调试
```javascript
// 在浏览器控制台中测试
window.aiAssistantDebug = {
  sendMessage: (msg) => {
    // 直接调用AI助手服务
    return aiAssistantService.sendChatMessage({message: msg});
  }
};
```

## 升级指南

### 1. 版本兼容性
- 后端API保持向后兼容
- 前端组件支持渐进式升级
- 数据库结构变更需要迁移脚本

### 2. 升级步骤
1. 备份当前配置和数据
2. 更新代码到新版本
3. 运行数据迁移（如需要）
4. 重启服务
5. 验证功能正常

## 技术支持

如遇到部署问题，请：
1. 查看本文档的故障排除部分
2. 检查项目GitHub Issues
3. 联系开发团队获取支持

---

**注意**: 本指南基于开发环境配置，生产环境部署请根据实际情况调整安全配置和性能参数。
