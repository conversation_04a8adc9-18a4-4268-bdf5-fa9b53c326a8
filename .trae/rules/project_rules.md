# BLops 代码结构分析

## 概述
BLops 是一个 Kubernetes 集群管理和诊断平台，由前端 (blops-web) 和后端 (blops) 两部分组成。
前端基于 React + UmiJS + Ant Design Pro，后端使用 Go 语言 + Gin 框架开发。

## 前端 (blops-web) 结构

### 技术栈
- React 16.x
- UmiJS 3.x (React 框架)
- Ant Design Pro (UI 组件库)
- TypeScript
- Dva (状态管理)
- Monaco Editor (代码编辑器)
- xterm.js (终端组件)

### 目录结构
```
blops-web/
├── .git/                      # Git 仓库
├── node_modules/              # 依赖包
├── public/                    # 静态资源
├── src/                       # 源码目录
│   ├── .umi/                  # UmiJS 临时文件目录
│   ├── components/            # 公共组件
│   ├── consts/                # 常量定义
│   ├── dtos/                  # 数据传输对象
│   ├── e2e/                   # 端到端测试
│   ├── locales/               # 国际化资源
│   ├── models/                # Dva 数据模型
│   ├── pages/                 # 页面组件
│   │   ├── appmarket/         # 应用市场页面
│   │   ├── cronjob/           # 定时任务页面
│   │   ├── cluster/           # 集群管理页面
│   │   ├── alert/             # 告警页面
│   │   ├── resource/          # 资源管理页面
│   │   ├── user/              # 用户管理页面
│   │   ├── ansible/           # Ansible 相关页面
│   │   ├── file-management/   # 文件管理页面
│   │   ├── 404.tsx            # 404 页面
│   │   └── document.ejs       # HTML 模板
│   ├── services/              # API 服务调用
│   ├── utils/                 # 工具函数
│   ├── app.tsx                # 应用入口
│   ├── access.ts              # 权限控制
│   ├── global.tsx             # 全局设置
│   ├── global.less            # 全局样式
│   └── typings.d.ts           # TypeScript 类型定义
├── config/                    # 配置文件目录
├── tests/                     # 测试
├── .gitlab-ci.yml             # GitLab CI 配置
├── .eslintrc.js               # ESLint 配置
├── .prettierrc.js             # Prettier 配置
├── .stylelintrc.js            # StyleLint 配置
├── Dockerfile                 # Docker 构建文件
├── nginx.conf                 # Nginx 配置
├── package.json               # 项目依赖管理
└── tsconfig.json              # TypeScript 配置
```

## 后端 (blops) 结构

### 技术栈
- Go 1.20
- Gin (Web 框架)
- GORM (ORM 库)
- Go-GitLab (GitLab 客户端)
- Kubernetes Client-Go (K8s 客户端)
- JWT (认证)
- Swagger (API 文档)

### 目录结构
```
blops/
├── .git/                      # Git 仓库
├── app/                       # 应用主要逻辑
│   ├── controllers/           # 控制器层，处理 HTTP 请求
│   │   ├── alert_controller.go    # 告警控制器
│   │   ├── ai_diagnosis_controller.go # AI 诊断控制器
│   │   ├── appmarket.go       # 应用市场控制器
│   │   ├── cluster_controller.go # 集群控制器
│   │   ├── cronjob_controller.go # 定时任务控制器
│   │   ├── pod_controller.go  # Pod 控制器
│   │   ├── resource_controller.go # 资源控制器
│   │   ├── user_controller.go # 用户控制器
│   │   └── rule_controller.go # 规则控制器
│   ├── services/              # 服务层，业务逻辑
│   ├── models/                # 数据模型
│   ├── dtos/                  # 数据传输对象
│   ├── vo/                    # 视图对象
│   ├── converter/             # 转换器
│   ├── job/                   # 后台任务
│   ├── co/                    # 协程相关
│   └── app.go                 # 应用初始化
├── base/                      # 基础设施
├── config/                    # 配置相关
│   └── config.yml             # 配置文件
├── docs/                      # 文档
├── enums/                     # 枚举定义
├── gitlab/                    # GitLab 集成
├── kube/                      # Kubernetes 集成
├── logger/                    # 日志相关
├── migration/                 # 数据库迁移
├── rest/                      # REST API 客户端
├── router/                    # 路由定义
│   ├── router.go              # 路由主文件
│   ├── alert_router.go        # 告警路由
│   ├── ai_diagnosis_router.go # AI 诊断路由
│   ├── appmarket_router.go    # 应用市场路由
│   ├── cluster_router.go      # 集群路由
│   ├── cronjob_router.go      # 定时任务路由
│   ├── pod_router.go          # Pod 路由
│   ├── resource_router.go     # 资源路由
│   └── user_router.go         # 用户路由
├── sql/                       # SQL 脚本
├── templates/                 # 模板文件
├── tmpl/                      # 另一个模板目录
├── utils/                     # 工具函数
├── .gitlab-ci.yml             # GitLab CI 配置
├── Dockerfile                 # Docker 构建文件
├── go.mod                     # Go 模块定义
├── go.sum                     # Go 依赖校验
└── main.go                    # 主入口文件
```

## 系统流程
1. 前端通过 `/src/services` 中的服务调用后端 API
2. 后端 API 请求通过 `router` 路由到对应的 `controllers`
3. 控制器处理请求，调用 `services` 层进行业务逻辑处理
4. 服务层通过 `models` 与数据库交互
5. 后端通过 Kubernetes Client-Go 与 Kubernetes 集群交互

## 主要功能模块
1. 集群管理 (Cluster Management)
2. Pod 管理与监控
3. 告警系统 (Alert)
4. AI 诊断 (AI Diagnosis)
5. 定时任务 (Cronjob)
6. 应用市场 (App Market)
7. 资源管理 (Resource)
8. 用户管理 (User)

## 部署相关
- 前端：Nginx + Docker
- 后端：Docker + Kubernetes
- CI/CD：GitLab CI 