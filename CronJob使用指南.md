# Blops CronJob 使用指南

## 快速开始

### 1. 访问CronJob管理页面
- 登录Blops平台
- 在左侧导航栏点击"定时任务"
- 进入CronJob管理界面

### 2. 基本操作流程
1. **选择集群**: 从下拉列表选择目标Kubernetes集群
2. **选择命名空间**: 选择要操作的命名空间（默认为default）
3. **查看任务列表**: 系统自动加载该命名空间下的所有CronJob
4. **执行操作**: 创建、编辑、删除或查看执行结果

## 创建定时任务

### 方式一：表单配置（推荐新手）

#### 步骤说明
1. 点击"添加定时任务"按钮
2. 选择"窗口配置"标签页
3. 填写以下必填信息：

**基本配置**
```
任务名称: my-backup-job
命名空间: default (自动填充)
Cron表达式: 0 2 * * * (每天凌晨2点执行)
```

**容器配置**
```
镜像源: 公有镜像
镜像名称: alpine:latest
命令: /bin/sh -c
参数: echo "Hello from CronJob at $(date)"
```

**高级配置**
```
暂停状态: 关闭 (任务创建后立即生效)
环境变量: 可选，点击"+"添加
```

4. 点击"创建"按钮完成

### 方式二：YAML编辑（推荐高级用户）

#### 示例YAML配置
```yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: hello-world-job
  namespace: default
spec:
  schedule: "0 */6 * * *"  # 每6小时执行一次
  jobTemplate:
    spec:
      backoffLimit: 0
      template:
        spec:
          containers:
          - name: hello
            image: busybox:1.28
            imagePullPolicy: IfNotPresent
            command:
            - /bin/sh
            - -c
            - date; echo "Hello from the Kubernetes cluster"
          restartPolicy: Never
  suspend: false
```

#### 步骤说明
1. 点击"添加定时任务"按钮
2. 选择"YAML编辑"标签页
3. 在编辑器中输入或粘贴YAML配置
4. 点击"创建"按钮完成

## Cron表达式指南

### 格式说明
```
* * * * *
│ │ │ │ │
│ │ │ │ └─── 星期 (0-7, 0和7都表示周日)
│ │ │ └───── 月份 (1-12)
│ │ └─────── 日期 (1-31)
│ └───────── 小时 (0-23)
└─────────── 分钟 (0-59)
```

### 常用示例
```bash
# 每分钟执行
* * * * *

# 每小时执行（整点）
0 * * * *

# 每天凌晨2点执行
0 2 * * *

# 每周一凌晨3点执行
0 3 * * 1

# 每月1号凌晨4点执行
0 4 1 * *

# 工作日每小时执行
0 * * * 1-5

# 每30分钟执行
*/30 * * * *

# 每2小时执行
0 */2 * * *
```

### 在线工具
推荐使用在线Cron表达式生成器：
- https://crontab.guru/
- https://cron.help/

## 任务管理操作

### 查看任务详情
1. 在任务列表中找到目标任务
2. 点击"查看详情"图标（👁️）
3. 在弹出的详情页面查看完整配置

### 编辑现有任务
1. 点击任务行的"编辑"图标（✏️）
2. 修改配置（支持表单和YAML两种方式）
3. 点击"更新"保存修改

### 删除任务
1. 点击任务行的"删除"图标（🗑️）
2. 在确认对话框中点击"确定"
3. 系统将删除CronJob及其所有历史Job

### 暂停/恢复任务
1. 编辑任务配置
2. 修改"暂停状态"设置
3. 保存更改

## 执行监控

### 查看执行历史
1. 点击任务行的"执行结果"链接
2. 在弹出窗口中查看：
   - **执行记录**：所有Job的执行历史
   - **任务详情**：选中Job的详细信息

### 执行状态说明
- **Complete**: 执行成功
- **Failed**: 执行失败
- **Running**: 正在执行
- **Pending**: 等待执行

### 查看执行日志
1. 在执行记录中找到目标Job
2. 点击"查看日志"链接
3. 在日志窗口中查看详细输出

### 手动触发执行
1. 在执行结果窗口点击"立即执行"按钮
2. 系统会立即创建一个新的Job
3. 可以在执行记录中查看触发结果

## 常见使用场景

### 1. 数据备份任务
```yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: database-backup
spec:
  schedule: "0 2 * * *"  # 每天凌晨2点
  jobTemplate:
    spec:
      backoffLimit: 0
      template:
        spec:
          containers:
          - name: backup
            image: mysql:8.0
            command:
            - /bin/bash
            - -c
            - |
              mysqldump -h $DB_HOST -u $DB_USER -p$DB_PASSWORD $DB_NAME > /backup/backup-$(date +%Y%m%d).sql
            env:
            - name: DB_HOST
              value: "mysql-service"
            - name: DB_USER
              value: "backup_user"
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: mysql-secret
                  key: password
            - name: DB_NAME
              value: "production"
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: backup-pvc
          restartPolicy: Never
```

### 2. 日志清理任务
```yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: log-cleanup
spec:
  schedule: "0 1 * * 0"  # 每周日凌晨1点
  jobTemplate:
    spec:
      backoffLimit: 0
      template:
        spec:
          containers:
          - name: cleanup
            image: alpine:latest
            command:
            - /bin/sh
            - -c
            - |
              find /logs -name "*.log" -mtime +7 -delete
              echo "Cleaned up logs older than 7 days"
            volumeMounts:
            - name: log-volume
              mountPath: /logs
          volumes:
          - name: log-volume
            hostPath:
              path: /var/log/applications
          restartPolicy: Never
```

### 3. 健康检查任务
```yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: health-check
spec:
  schedule: "*/5 * * * *"  # 每5分钟执行
  jobTemplate:
    spec:
      backoffLimit: 0
      template:
        spec:
          containers:
          - name: health-checker
            image: curlimages/curl:latest
            command:
            - /bin/sh
            - -c
            - |
              if curl -f http://my-service:8080/health; then
                echo "Service is healthy"
              else
                echo "Service health check failed" >&2
                exit 1
              fi
          restartPolicy: Never
```

## 故障排查

### 常见问题

#### 1. CronJob不执行
**可能原因**：
- Cron表达式错误
- 任务被暂停
- 集群时区问题

**解决方法**：
- 验证Cron表达式格式
- 检查suspend字段是否为false
- 确认集群时区设置

#### 2. Job执行失败
**排查步骤**：
1. 查看Job状态和错误信息
2. 检查Pod日志
3. 验证镜像和命令是否正确
4. 检查资源限制和权限

#### 3. 任务创建失败
**可能原因**：
- YAML格式错误
- 资源名称冲突
- 权限不足

**解决方法**：
- 验证YAML语法
- 使用唯一的任务名称
- 检查RBAC权限配置

### 调试技巧

#### 1. 使用简单命令测试
```bash
# 先用简单命令测试
command: ["/bin/sh", "-c", "echo 'Hello World'"]
```

#### 2. 增加调试输出
```bash
command: ["/bin/sh", "-c", "set -x; echo 'Debug info'; your-actual-command"]
```

#### 3. 检查环境变量
```bash
command: ["/bin/sh", "-c", "env | sort; your-actual-command"]
```

## 最佳实践

### 1. 命名规范
- 使用描述性的任务名称
- 包含功能和频率信息
- 例如：`daily-backup-mysql`、`weekly-log-cleanup`

### 2. 资源管理
- 设置合适的资源限制
- 使用适当的重启策略
- 考虑任务执行时间和集群负载

### 3. 监控告警
- 定期检查任务执行状态
- 设置失败任务的告警通知
- 监控任务执行时间和资源使用

### 4. 安全考虑
- 使用Secret管理敏感信息
- 最小权限原则
- 定期更新镜像版本

### 5. 维护管理
- 定期清理不需要的历史Job
- 及时更新过期的任务配置
- 备份重要的任务配置

通过以上指南，您可以充分利用Blops平台的CronJob功能，实现高效的定时任务管理。
