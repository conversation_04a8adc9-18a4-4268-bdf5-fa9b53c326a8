package controllers

// GetCompleteOptimizedTemplates 返回完整的优化模板列表
func GetCompleteOptimizedTemplates() []OptimizedQuickTemplate {
	return []OptimizedQuickTemplate{
		// 故障排查类模板
		{
			ID:          "pod-startup-failure",
			Title:       "Pod启动失败诊断",
			Description: "全面诊断Pod无法启动的各种原因",
			Category:    "故障排查",
			Tags:        []string{"Pod", "启动失败", "诊断"},
			Priority:    1,
			Content: `作为Kubernetes专家，请帮我诊断Pod启动失败问题。

**环境信息：**
- 集群版本：{集群版本}
- 命名空间：{命名空间}
- Pod名称：{Pod名称}

**问题描述：**
- Pod状态：{Pod状态}
- 错误信息：{错误信息}
- 事件信息：{事件信息}

**请提供：**
1. 问题根因分析
2. 详细的诊断步骤和命令
3. 具体的解决方案
4. 预防措施建议

请使用kubectl命令示例，并考虑镜像拉取、资源限制、配置错误等常见原因。`,
		},
		{
			ID:          "pod-crashloop-backoff",
			Title:       "Pod CrashLoopBackOff 问题",
			Description: "诊断和解决Pod反复崩溃重启问题",
			Category:    "故障排查",
			Tags:        []string{"Pod", "CrashLoopBackOff", "重启"},
			Priority:    1,
			Content: `作为Kubernetes运维专家，请帮我解决Pod CrashLoopBackOff问题。

**Pod信息：**
- Pod名称：{Pod名称}
- 命名空间：{命名空间}
- 重启次数：{重启次数}
- 最后退出码：{退出码}

**日志信息：**
` + "```" + `
{容器日志}
` + "```" + `

**需要分析：**
1. 崩溃的根本原因
2. 应用程序问题 vs 配置问题
3. 资源限制是否合理
4. 健康检查配置是否正确

请提供详细的诊断命令、解决步骤和最佳实践建议。`,
		},
		{
			ID:          "service-network-connectivity",
			Title:       "Service网络连接问题",
			Description: "排查Pod间和Service访问的网络连接问题",
			Category:    "故障排查",
			Tags:        []string{"Service", "网络", "连接"},
			Priority:    2,
			Content: `作为Kubernetes网络专家，请帮我排查Service网络连接问题。

**网络环境：**
- 源Pod：{源Pod}
- 目标Service：{目标Service}
- 命名空间：{命名空间}
- 网络插件：{网络插件}

**问题现象：**
- 连接错误：{连接错误}
- 超时时间：{超时时间}
- 访问方式：{访问方式}

**请诊断：**
1. Service和Endpoints配置
2. 网络策略限制
3. DNS解析问题
4. 防火墙和安全组
5. 负载均衡配置

请提供网络诊断命令、连通性测试方法和解决方案。`,
		},
		{
			ID:          "dns-resolution-issues",
			Title:       "DNS解析问题排查",
			Description: "诊断集群内DNS解析失败问题",
			Category:    "故障排查",
			Tags:        []string{"DNS", "解析", "CoreDNS"},
			Priority:    2,
			Content: `作为Kubernetes DNS专家，请帮我排查DNS解析问题。

**DNS环境：**
- DNS服务：{DNS服务}
- 解析域名：{解析域名}
- 客户端Pod：{客户端Pod}

**问题现象：**
- 解析错误：{解析错误}
- 解析延迟：{解析延迟}
- 间歇性失败：{间歇性失败}

**请检查：**
1. CoreDNS配置和状态
2. DNS策略设置
3. 网络连通性
4. DNS缓存问题
5. 上游DNS配置

请提供DNS诊断命令、配置检查方法和解决方案。`,
		},
		{
			ID:          "ingress-traffic-issues",
			Title:       "Ingress流量问题",
			Description: "排查Ingress控制器和流量路由问题",
			Category:    "故障排查",
			Tags:        []string{"Ingress", "流量", "路由"},
			Priority:    2,
			Content: `作为Kubernetes Ingress专家，请帮我排查流量问题。

**Ingress信息：**
- Ingress控制器：{Ingress控制器}
- Ingress规则：{Ingress规则}
- 后端服务：{后端服务}

**问题现象：**
- 访问错误：{访问错误}
- 响应异常：{响应异常}
- 证书问题：{证书问题}

**请诊断：**
1. Ingress控制器状态
2. 路由规则配置
3. 后端服务健康状态
4. TLS证书配置
5. 负载均衡设置

请提供Ingress诊断方法、配置检查和解决方案。`,
		},

		// 性能优化类模板
		{
			ID:          "cluster-resource-analysis",
			Title:       "集群资源使用分析",
			Description: "分析集群整体资源使用情况并提供优化建议",
			Category:    "性能优化",
			Tags:        []string{"集群", "资源分析", "优化"},
			Priority:    1,
			Content: `作为Kubernetes性能优化专家，请帮我分析集群资源使用情况。

**集群概况：**
- 节点数量：{节点数量}
- 总CPU核数：{总CPU}
- 总内存：{总内存}
- Pod总数：{Pod总数}

**资源使用率：**
- CPU使用率：{CPU使用率}%
- 内存使用率：{内存使用率}%
- 存储使用率：{存储使用率}%
- 网络带宽：{网络使用}

**性能指标：**
- 平均响应时间：{响应时间}
- 错误率：{错误率}
- 吞吐量：{吞吐量}

**请提供：**
1. 资源使用趋势分析
2. 性能瓶颈识别
3. 扩缩容建议
4. 资源分配优化方案
5. 成本优化建议

请包含监控命令、性能调优最佳实践和容量规划建议。`,
		},
		{
			ID:          "hpa-vpa-optimization",
			Title:       "自动扩缩容优化",
			Description: "优化HPA和VPA自动扩缩容配置",
			Category:    "性能优化",
			Tags:        []string{"HPA", "VPA", "自动扩缩容"},
			Priority:    1,
			Content: `作为Kubernetes自动扩缩容专家，请帮我优化扩缩容策略。

**应用特征：**
- 应用类型：{应用类型}
- 流量模式：{流量模式}
- 资源特征：{资源特征}

**当前配置：**
- HPA配置：{HPA配置}
- VPA配置：{VPA配置}
- 扩缩容历史：{扩缩容历史}

**性能目标：**
- 响应时间：{响应时间目标}
- 资源利用率：{资源利用率目标}
- 成本控制：{成本目标}

**请优化：**
1. 扩缩容指标选择
2. 阈值和参数调优
3. 扩缩容策略设计
4. 监控和告警配置
5. 成本效益分析

请提供配置示例、调优方法和最佳实践。`,
		},

		// 安全检查类模板
		{
			ID:          "security-rbac-audit",
			Title:       "RBAC权限安全审计",
			Description: "审计和优化Kubernetes RBAC权限配置",
			Category:    "安全检查",
			Tags:        []string{"RBAC", "权限", "安全审计"},
			Priority:    1,
			Content: `作为Kubernetes安全专家，请帮我进行RBAC权限安全审计。

**审计范围：**
- 命名空间：{命名空间}
- 用户/服务账户：{账户信息}
- 角色类型：{角色类型}

**当前权限：**
- ClusterRole：{ClusterRole}
- Role：{Role}
- RoleBinding：{RoleBinding}
- ClusterRoleBinding：{ClusterRoleBinding}

**安全关注点：**
- 过度权限：{过度权限}
- 敏感操作：{敏感操作}
- 权限继承：{权限继承}

**请检查：**
1. 最小权限原则遵循情况
2. 危险权限识别
3. 权限分离和隔离
4. 服务账户安全性
5. 权限审计日志

请提供权限优化建议、安全最佳实践和合规性检查清单。`,
		},
		{
			ID:          "pod-security-standards",
			Title:       "Pod安全标准检查",
			Description: "检查Pod安全配置和安全标准合规性",
			Category:    "安全检查",
			Tags:        []string{"Pod", "安全标准", "PSS"},
			Priority:    1,
			Content: `作为Kubernetes Pod安全专家，请帮我检查Pod安全配置。

**Pod信息：**
- Pod名称：{Pod名称}
- 命名空间：{命名空间}
- 安全上下文：{安全上下文}

**安全配置：**
- 特权模式：{特权模式}
- 用户ID：{用户ID}
- 文件系统：{文件系统}
- 网络模式：{网络模式}

**合规要求：**
- 安全标准：{安全标准}
- 合规框架：{合规框架}
- 审计要求：{审计要求}

**请检查：**
1. Pod Security Standards合规性
2. 安全上下文配置
3. 特权和能力设置
4. 文件系统安全
5. 网络安全配置

请提供安全配置建议、合规性检查清单和修复方案。`,
		},

		// 资源管理类模板
		{
			ID:          "namespace-resource-management",
			Title:       "命名空间资源管理",
			Description: "管理和优化命名空间资源配置",
			Category:    "资源管理",
			Tags:        []string{"Namespace", "资源管理", "配额"},
			Priority:    1,
			Content: `作为Kubernetes资源管理专家，请帮我优化命名空间资源管理。

**命名空间信息：**
- 命名空间：{命名空间}
- 用途：{用途}
- 团队：{团队}

**资源配置：**
- ResourceQuota：{ResourceQuota}
- LimitRange：{LimitRange}
- NetworkPolicy：{NetworkPolicy}

**使用情况：**
- Pod数量：{Pod数量}
- 资源使用：{资源使用}
- 存储使用：{存储使用}

**管理需求：**
- 隔离要求：{隔离要求}
- 资源限制：{资源限制}
- 成本控制：{成本控制}

**请提供：**
1. 资源配额设计
2. 限制范围配置
3. 多租户隔离策略
4. 资源监控方案
5. 成本分摊机制

请包含完整的配置示例、管理工具推荐和最佳实践指南。`,
		},

		// 部署运维类模板
		{
			ID:          "application-deployment-guide",
			Title:       "应用部署最佳实践指导",
			Description: "提供应用在Kubernetes上部署的完整指导",
			Category:    "部署运维",
			Tags:        []string{"部署", "应用", "最佳实践"},
			Priority:    1,
			Content: `作为Kubernetes部署专家，请为我的应用提供部署指导。

**应用信息：**
- 应用类型：{应用类型}
- 架构模式：{架构模式}
- 技术栈：{技术栈}
- 依赖服务：{依赖服务}

**部署需求：**
- 环境：{环境}
- 可用性要求：{可用性要求}
- 性能要求：{性能要求}
- 安全要求：{安全要求}

**资源需求：**
- CPU/内存：{资源需求}
- 存储需求：{存储需求}
- 网络需求：{网络需求}

**请提供：**
1. 部署架构设计
2. Kubernetes资源配置
3. 配置管理策略
4. 服务发现配置
5. 健康检查设置
6. 安全配置建议

请包含完整的YAML配置示例、部署脚本和验证方法。`,
		},

		// 监控告警类模板
		{
			ID:          "prometheus-monitoring-setup",
			Title:       "Prometheus监控配置",
			Description: "配置Prometheus监控系统和指标收集",
			Category:    "监控告警",
			Tags:        []string{"Prometheus", "监控", "指标"},
			Priority:    1,
			Content: `作为Kubernetes监控专家，请帮我配置Prometheus监控系统。

**监控需求：**
- 监控对象：{监控对象}
- 关键指标：{关键指标}
- 监控粒度：{监控粒度}

**环境信息：**
- 集群规模：{集群规模}
- 应用数量：{应用数量}
- 数据保留：{数据保留}

**性能要求：**
- 采集频率：{采集频率}
- 查询性能：{查询性能}
- 存储需求：{存储需求}

**请配置：**
1. Prometheus服务器设置
2. 服务发现配置
3. 指标采集规则
4. 存储和保留策略
5. 高可用部署

请提供完整的配置文件、部署脚本和监控仪表板。`,
		},

		// 集群管理类模板
		{
			ID:          "cluster-health-check",
			Title:       "集群健康状态检查",
			Description: "全面检查Kubernetes集群健康状态",
			Category:    "集群管理",
			Tags:        []string{"集群", "健康检查", "诊断"},
			Priority:    1,
			Content: `作为Kubernetes集群管理专家，请帮我进行集群健康检查。

**集群信息：**
- 集群版本：{集群版本}
- 节点数量：{节点数量}
- 组件状态：{组件状态}

**检查范围：**
- 控制平面：{控制平面}
- 工作节点：{工作节点}
- 网络组件：{网络组件}
- 存储系统：{存储系统}

**关注指标：**
- 可用性：{可用性}
- 性能指标：{性能指标}
- 资源使用：{资源使用}
- 错误率：{错误率}

**请检查：**
1. 集群组件状态
2. 节点健康状况
3. 网络连通性
4. 存储可用性
5. 安全配置
6. 性能指标

请提供健康检查脚本、诊断命令和修复建议。`,
		},

		// 平台使用类模板
		{
			ID:          "blops-platform-features",
			Title:       "Blops平台功能使用",
			Description: "了解和使用Blops平台的各项功能",
			Category:    "平台使用",
			Tags:        []string{"Blops", "平台", "功能"},
			Priority:    1,
			Content: `作为Blops平台专家，请帮我了解平台功能使用方法。

**使用场景：**
- 使用目的：{使用目的}
- 用户角色：{用户角色}
- 操作频率：{操作频率}

**功能需求：**
- 集群管理：{集群管理}
- 应用部署：{应用部署}
- 监控告警：{监控告警}
- 日志查看：{日志查看}

**当前问题：**
- 操作困难：{操作困难}
- 功能不熟悉：{功能不熟悉}
- 效率问题：{效率问题}

**请介绍：**
1. 平台核心功能
2. 操作界面指导
3. 常用操作流程
4. 高级功能使用
5. 最佳实践建议

请提供功能说明、操作指南和使用技巧。`,
		},
	}
}
