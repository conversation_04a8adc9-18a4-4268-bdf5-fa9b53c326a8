# AI诊断功能开发项目结构

## 项目概述
AI诊断功能是一个基于前后端分离架构的系统，前端使用React框架，后端使用Go语言开发。

## 前端项目结构 (blops-web)

### 配置文件
- **config/routes.ts**: 路由配置文件，定义了应用的页面路由
- **config/proxy.ts**: 代理配置文件，用于开发环境中的API请求转发
- **config/oneapi.json**: OneAPI配置文件，可能用于API集成

### 核心文件
- **src/access.ts**: 权限控制相关逻辑
- **src/pages/cluster/index.tsx**: 集群管理页面组件
- **src/components/Terminal/index.tsx**: 终端组件，用于命令行交互

## 后端项目结构 (blops)

### 路由层
- **router/router.go**: 主路由配置文件，定义API路由
- **router/alert_router.go**: 告警相关的路由配置

## AI诊断功能
AI诊断功能将通过前后端协作实现：
1. 前端提供用户交互界面，包括诊断请求提交和结果展示
2. 查询kubernetes的namespace events事件日志，展示在页面
3. 支持选择cluster，namespace
![alt text](image.png)
4. 用户选择诊断的namespace，后端查询该namespace的events事件日志
5. 后端处理诊断逻辑，调用AI模型进行分析，并返回诊断结果
AI模型调用：
curl "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=AIzaSyByTYU8l_0Qbqq6AiCQpZOgu-rJ3PfgfLM" \
-H 'Content-Type: application/json' \
-X POST \
-d '{
  "contents": [{
    "parts":[{"text": "k8s 集群管理详解"}]
    }]
   }'

返回结果：
{
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "好的，以下是一份关于 Kubernetes (K8s) 集群管理的详解，涵盖了从集群搭建到日常运维的各个方面。\n\n**一、Kubernetes 概述**\n\n*   **定义：** Kubernetes (简称 K8s) 是一个开源的容器编排平台，用于自动化部署、扩展和管理容器化的应用程序。\n*   **核心特性：**\n    *   **自动化部署和回滚：** 简化应用程序的部署过程，并提供快速回滚到先前版本的功能。\n    *   **服务发现和负载均衡：**  自动将请求路由到健康的容器实例，实现负载均衡。\n    *   **自我修复：** 自动重启失败的容器，并替换已失效的节点。\n    *   **自动扩缩容：** 根据资源利用率自动调整容器数量，以适应流量变化。\n    *   **存储编排：**  支持各种存储解决方案，并提供持久化存储管理。\n    *   **配置管理：**  集中管理应用程序的配置信息，并动态更新。\n    *   **Secret 管理：**  安全地存储和管理敏感信息，例如密码和 API 密钥。\n\n**二、Kubernetes 架构**\n\nKubernetes 集群由以下核心组件构成：\n\n*   **Master Node (控制平面)**\n    *   **kube-apiserver：**  Kubernetes API 服务器，集群的统一入口，接收并处理所有 API 请求。\n    *   **kube-scheduler：**  调度器，负责将 Pod (最小的部署单元) 分配到合适的 Worker Node 上。\n    *   **kube-controller-manager：**  控制器管理器，运行各种控制器，例如：\n        *   **Replication Controller:**  确保指定数量的 Pod 副本始终运行。\n        *   **Node Controller:**  管理 Worker Node 的状态。\n        *   **Service Controller:**  管理 Service 资源。\n        *   **Endpoint Controller:**  维护 Service 和 Pod 之间的映射关系。\n    *   **etcd：**  分布式键值存储系统，用于存储集群的配置数据和状态信息。\n*   **Worker Node (工作节点)**\n    *   **kubelet：**  Node 上的代理，接收 Master Node 的指令，管理 Node 上的 Pod。\n    *   **kube-proxy：**  网络代理，负责实现 Service 的负载均衡和网络转发。\n    *   **Container Runtime (例如 Docker, containerd)：**  负责运行容器。\n\n**三、Kubernetes 核心概念**\n\n*   **Pod：** Kubernetes 中最小的部署单元，可以包含一个或多个容器。  Pod 中的容器共享网络和存储。\n*   **Service：**  定义访问 Pod 的方式，提供稳定的 IP 地址和 DNS 名称，实现服务发现和负载均衡。\n    *   **ClusterIP：** 仅在集群内部可访问的 Service。\n    *   **NodePort：**  将 Service 暴露在每个 Node 的指定端口上，可以通过 Node 的 IP 地址和端口访问。\n    *   **LoadBalancer：**  使用云服务提供商的负载均衡器将 Service 暴露在外部。\n*   **Deployment：**  声明式地管理 Pod 的部署和更新。 Deployment 可以自动创建、更新和回滚 Pod。\n*   **ReplicaSet：**  确保指定数量的 Pod 副本始终运行。  Deployment 通常使用 ReplicaSet 来管理 Pod。\n*   **Namespace：**  将集群资源划分为多个虚拟集群，实现资源隔离和权限控制。\n*   **Ingress：**  管理对集群外部流量的访问，可以根据主机名或路径将流量路由到不同的 Service。\n*   **ConfigMap：**  存储非敏感的配置数据，例如应用程序的配置文件。\n*   **Secret：**  存储敏感信息，例如密码和 API 密钥。\n*   **Volume：**  为 Pod 提供持久化存储。  支持多种 Volume 类型，例如：\n    *   **emptyDir：**  临时存储，Pod 删除时数据也会被删除。\n    *   **hostPath：**  挂载 Node 上的目录到 Pod。\n    *   **PersistentVolume (PV):**  集群中的持久化存储资源。\n    *   **PersistentVolumeClaim (PVC):**  Pod 对 PV 的请求。\n\n**四、Kubernetes 集群搭建**\n\n搭建 Kubernetes 集群有多种方式：\n\n*   **Minikube：**  适用于本地开发和测试的单节点 Kubernetes 集群。\n*   **kubeadm：**  官方推荐的 Kubernetes 集群搭建工具，提供灵活的配置选项。\n*   **Kubespray：**  使用 Ansible 自动化部署 Kubernetes 集群。\n*   **云服务提供商 (例如 AWS EKS, Google GKE, Azure AKS)：**  提供托管的 Kubernetes 集群服务，简化集群的搭建和管理。\n\n**使用 kubeadm 搭建集群的步骤 (简要)：**\n\n1.  **准备环境：**\n    *   至少两台 Linux 服务器 (一台 Master Node，一台或多台 Worker Node)。\n    *   每台服务器都需要安装 Docker 和 kubeadm, kubelet, kubectl。\n    *   禁用 swap。\n    *   确保网络畅通。\n2.  **初始化 Master Node：**\n    ```bash\n    kubeadm init --pod-network-cidr=10.244.0.0/16\n    ```\n    *   `--pod-network-cidr` 指定 Pod 网络 CIDR。\n3.  **配置 kubectl：**\n    ```bash\n    mkdir -p $HOME/.kube\n    sudo cp -i /etc/kubernetes/admin.conf $HOME/.kube/config\n    sudo chown $(id -u):$(id -g) $HOME/.kube/config\n    ```\n4.  **安装网络插件 (例如 Calico, Flannel)：**\n    ```bash\n    kubectl apply -f https://docs.projectcalico.org/manifests/calico.yaml\n    ```\n5.  **加入 Worker Node：**\n    *   在 Master Node 上执行 `kubeadm token create --print-join-command` 获取加入命令。\n    *   在 Worker Node 上执行加入命令。\n\n**五、Kubernetes 常用操作**\n\n*   **kubectl 命令：** Kubernetes 的命令行工具，用于与集群交互。\n\n    *   `kubectl get \u003cresource\u003e`：  获取资源信息，例如 `kubectl get pods`, `kubectl get services`。\n    *   `kubectl create -f \u003cfile.yaml\u003e`：  创建资源，例如 `kubectl create -f deployment.yaml`。\n    *   `kubectl apply -f \u003cfile.yaml\u003e`：  更新资源，例如 `kubectl apply -f deployment.yaml`。\n    *   `kubectl delete \u003cresource\u003e \u003cname\u003e`：  删除资源，例如 `kubectl delete pod my-pod`。\n    *   `kubectl describe \u003cresource\u003e \u003cname\u003e`：  查看资源的详细信息，例如 `kubectl describe pod my-pod`。\n    *   `kubectl logs \u003cpod_name\u003e`：  查看 Pod 的日志。\n    *   `kubectl exec -it \u003cpod_name\u003e -- \u003ccommand\u003e`：  在 Pod 中执行命令，例如 `kubectl exec -it my-pod -- bash`。\n    *   `kubectl scale deployment \u003cdeployment_name\u003e --replicas=\u003cnumber\u003e`：  扩缩容 Deployment。\n*   **YAML 文件：**  用于定义 Kubernetes 资源。  YAML 文件包含资源的类型、名称、配置等信息。\n\n    ```yaml\n    apiVersion: apps/v1\n    kind: Deployment\n    metadata:\n      name: my-deployment\n    spec:\n      replicas: 3\n      selector:\n        matchLabels:\n          app: my-app\n      template:\n        metadata:\n          labels:\n            app: my-app\n        spec:\n          containers:\n          - name: my-container\n            image: nginx:latest\n            ports:\n            - containerPort: 80\n    ```\n\n**六、Kubernetes 日常运维**\n\n*   **监控：**\n    *   **Prometheus + Grafana：**  常用的 Kubernetes 监控解决方案。  Prometheus 收集集群的指标数据，Grafana 用于可视化数据。\n    *   **Heapster：**  用于收集集群的资源利用率数据 (已逐渐被 Metrics Server 取代)。\n    *   **Metrics Server：**  提供 Kubernetes 集群的资源利用率指标，用于自动扩缩容。\n*   **日志管理：**\n    *   **ELK Stack (Elasticsearch, Logstash, Kibana)：**  常用的日志管理解决方案。  Logstash 收集日志数据，Elasticsearch 存储日志数据，Kibana 用于可视化日志数据。\n    *   **Fluentd：**  另一个流行的日志收集器。\n*   **告警：**\n    *   **Alertmanager：**  Prometheus 的告警管理器，用于接收 Prometheus 发送的告警，并发送通知。\n*   **备份和恢复：**\n    *   **etcd 备份：**  定期备份 etcd 数据，以防止数据丢失。\n    *   **Velero：**  用于备份和恢复 Kubernetes 集群的资源。\n*   **升级：**  定期升级 Kubernetes 集群，以获取最新的功能和安全补丁。  升级过程需要谨慎操作，避免影响应用程序的正常运行。\n*   **问题排查：**\n    *   查看 Pod 的状态和日志。\n    *   检查 Node 的状态。\n    *   查看 Kubernetes 组件的日志。\n    *   使用 `kubectl describe` 命令查看资源的详细信息。\n    *   使用网络工具 (例如 `ping`, `traceroute`) 检查网络连接。\n    *   查看 etcd 的状态。\n\n**七、Kubernetes 最佳实践**\n\n*   **资源限制：**  为每个容器设置资源限制 (CPU 和内存)，防止资源耗尽。\n*   **健康检查：**  配置 liveness 和 readiness 探测，确保 Pod 的健康状态。\n*   **命名空间：**  使用命名空间隔离不同的应用程序或团队。\n*   **安全：**\n    *   启用 RBAC (Role-Based Access Control) 进行权限控制。\n    *   使用 NetworkPolicy 限制 Pod 之间的网络流量。\n    *   定期更新 Kubernetes 版本和依赖库，以修复安全漏洞。\n*   **自动化：**  使用 CI/CD 工具自动化部署和更新应用程序。\n*   **监控和告警：**  设置完善的监控和告警系统，及时发现和解决问题。\n*   **持久化存储：**  合理选择存储方案，确保数据的持久性。\n*   **版本控制：**  使用 Git 等版本控制工具管理 Kubernetes 资源清单。\n*   **最小权限原则:** 确保每个组件和服务只拥有完成其任务所需的最小权限。\n*   **定期审查配置:** 定期审查 Kubernetes 集群的配置，包括资源限制、网络策略和安全设置。\n\n**八、高级主题**\n\n*   **Operators：**  用于自动化管理复杂应用程序，例如数据库和消息队列。\n*   **Service Mesh (例如 Istio, Linkerd)：**  提供服务间的流量管理、安全性和可观察性。\n*   **Serverless (例如 Knative)：**  在 Kubernetes 上运行无服务器应用程序。\n*   **Kubernetes Federation：**  管理多个 Kubernetes 集群。\n*   **CNI (Container Network Interface)：**  定义容器网络接口，支持各种网络插件。\n*   **CSI (Container Storage Interface)：**  定义容器存储接口，支持各种存储解决方案。\n\n**九、学习资源**\n\n*   **Kubernetes 官方文档：**  [https://kubernetes.io/docs/](https://kubernetes.io/docs/)\n*   **Kubernetes Tutorials：**  [https://kubernetes.io/docs/tutorials/](https://kubernetes.io/docs/tutorials/)\n*   **Kubernetes in Action：**  一本经典的 Kubernetes 书籍。\n*   **Certified Kubernetes Administrator (CKA)：**  Kubernetes 官方认证。\n\n**总结**\n\nKubernetes 是一个功能强大的容器编排平台，可以帮助您自动化部署、扩展和管理容器化的应用程序。  学习 Kubernetes 需要掌握其核心概念、架构和常用操作。  通过实践和不断学习，您可以更好地利用 Kubernetes 来提高应用程序的效率和可靠性。\n\n希望这份详解能够帮助您更好地理解和使用 Kubernetes。  如果您有任何问题，请随时提出。\n"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "citationMetadata": {
        "citationSources": [
          {
            "startIndex": 2777,
            "endIndex": 2937,
            "uri": "https://blog.51cto.com/u_16065618/9685389"
          },
          {
            "startIndex": 4042,
            "endIndex": 4341,
            "uri": "https://medium.com/cloud-native-daily/unraveling-kubernetes-yaml-files-your-guide-to-declarative-configuration-9b5d6108e49b"
          },
          {
            "startIndex": 4175,
            "endIndex": 4433,
            "uri": "https://medium.com/@nidhiashtikar/kubernetes-interview-question-part1-39a426f71137"
          }
        ]
      },
      "avgLogprobs": -0.17563466520152538
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 7,
    "candidatesTokenCount": 2735,
    "totalTokenCount": 2742,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 7
      }
    ],
    "candidatesTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 2735
      }
    ]
  },
  "modelVersion": "gemini-2.0-flash"
}

根据以上示例，后端需要解析返回的json，提取出text，并返回给前端，前端页面展示需要美化

6. 主要提供了 获取namespace的events事件日志，当前端选择具体的事件信息后，传给AI大模型，并添加上提示词 “你是kubernetes云原生专家，首先按时间顺序整理好这些日志条目概要，然后深入分析一下这些日志信息，使用中文回答。”

7. 数据通过API在前后端之间传输

8. 前端的页面放在 “kubernetes管理” 下面 和 “集群列表” 同级
![alt text](image-1.png)

## 开发计划
1. 后端开发AI诊断API
2. 前端开发诊断界面

