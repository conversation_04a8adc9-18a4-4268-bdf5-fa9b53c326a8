package co

import "blops/app/models/po"

type TemplateCO struct {
	ID        int64      `form:"id" json:"id,omitempty"`
	AlertName string     `form:"alert_name" json:"alert_name" uri:"alert_name"`
	AlertCode string     `form:"alert_code" json:"alert_code" uri:"alert_code"`
	Message   string     `form:"message" json:"message" uri:"message"` // 告警信息
	Expr      string     `form:"expr" json:"expr" uri:"expr"`
	PerchList []po.Perch `form:"perchList" json:"perchList"`
}
