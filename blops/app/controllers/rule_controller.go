package controllers

import (
	"blops/app/co"
	"blops/app/converter"
	"blops/app/models/po"
	. "blops/app/services"
	. "blops/app/services/impl"
	http2 "blops/enums/http"
	"blops/logger"
	"blops/utils"
	"blops/utils/array"

	"github.com/gin-gonic/gin"
)

type ruleController struct {
	ruleSvc     RuleServiceInf
	TemplateSvc TemplateServiceInf
	treeSvc     TreeServiceInf
}

var RuleCtrl *ruleController

func init() {
	RuleCtrl = &ruleController{
		ruleSvc:     RuleSvc,
		TemplateSvc: TemplateSvc,
		treeSvc:     TreeSvc,
	}
}

// AddRule 添加规则
// @Summary      添加规则
// @Description  添加新的告警规则
// @Tags         告警管理
// @Accept       json
// @Produce      json
// @Param        request  body      models.RuleRequest  true  "规则请求参数"
// @Success      200      {object}  models.Response     "添加规则成功"
// @Failure      400      {object}  models.Response     "请求参数错误"
// @Failure      500      {object}  models.Response     "添加规则失败"
// @Router       /v1/alert/conf [post]
// @Security     ApiKeyAuth
func (a *ruleController) AddRule(c *gin.Context) {
	var rule RuleCO
	if utils.CheckParams[RuleCO](c, &rule) != nil {
		return
	}
	logger.Infof("获取的数据内容：%v", rule)
	e := a.ruleSvc.RuleHandler(&rule, "create")
	if e != nil {
		logger.Error(e)
		c.JSON(http2.ServerError().WithExtra(e.Error()))
		return
	}
	c.JSON(http2.Success(true))
	return
}

// UpdateRule 更新规则
// @Summary      更新规则
// @Description  更新现有的告警规则
// @Tags         告警管理
// @Accept       json
// @Produce      json
// @Param        request  body      models.RuleUpdateRequest  true  "规则更新请求参数"
// @Success      200      {object}  models.Response           "更新规则成功"
// @Failure      400      {object}  models.Response           "请求参数错误"
// @Failure      500      {object}  models.Response           "更新规则失败"
// @Router       /v1/alert/conf/_update [post]
// @Security     ApiKeyAuth
func (a *ruleController) UpdateRule(c *gin.Context) {

	var rule RuleCO
	if utils.CheckParams[RuleCO](c, &rule) != nil {
		return
	}
	logger.Infof("获取的数据内容：%v", rule)
	e := a.ruleSvc.RuleHandler(&rule, "update")
	if e != nil {
		logger.Error(e)
		c.JSON(http2.ServerError().WithMsg("处理告警规则失败"))
		return
	}

	c.JSON(http2.Success(true))
	return
}

// GetRule 获取规则
// @Summary      获取规则
// @Description  获取告警规则列表
// @Tags         告警管理
// @Accept       json
// @Produce      json
// @Param        request  body      models.RuleListRequest  true  "规则列表请求参数"
// @Success      200      {object}  models.Response         "获取规则成功"
// @Failure      400      {object}  models.Response         "请求参数错误"
// @Failure      500      {object}  models.Response         "获取规则失败"
// @Router       /v1/alert/conf/_list [post]
// @Security     ApiKeyAuth
func (a *ruleController) GetRule(c *gin.Context) {
	var rule RuleGet
	if utils.CheckParams[RuleGet](c, &rule) != nil {
		return
	}
	if rule.Cluster == "" {
		rule.Cluster = "ali-test"
	}
	logger.Infof("GetRule: %v", rule)
	data, e := a.ruleSvc.GetRule(rule.Types, rule.Cluster)
	if e != nil {
		c.JSON(http2.ServerError().WithMsg("服务错误！"))
		return
	}
	c.JSON(http2.Success(data))
	return
}

// AddTemplate 添加模板
// @Summary      添加模板
// @Description  添加新的告警模板
// @Tags         告警管理
// @Accept       json
// @Produce      json
// @Param        request  body      models.TemplateRequest  true  "模板请求参数"
// @Success      200      {object}  models.Response         "添加模板成功"
// @Failure      400      {object}  models.Response         "请求参数错误"
// @Failure      500      {object}  models.Response         "添加模板失败"
// @Router       /v1/alert/template [post]
// @Security     ApiKeyAuth
func (a *ruleController) AddTemplate(c *gin.Context) {
	var template co.TemplateCO
	if utils.CheckParams[co.TemplateCO](c, &template) != nil {
		return
	}

	logger.Infof("传参：", template)

	perch, err := a.TemplateSvc.PerchHandler(template.PerchList)
	if err != nil {
		c.JSON(http2.BadRequest().WithMsg(err.Error()))
		return
	}
	templatePO := converter.AlertTemplateCO2PO(template)
	templatePO.Perch = perch
	e := a.TemplateSvc.AddTemplate(templatePO)
	if e != nil {
		logger.Error(e)
		c.JSON(http2.BadRequest().WithMsg("添加模版失败"))
		return
	}
	c.JSON(http2.Success(true))
	return
}

// UpdateTemplate 更新模板
// @Summary      更新模板
// @Description  更新现有的告警模板
// @Tags         告警管理
// @Accept       json
// @Produce      json
// @Param        request  body      models.TemplateUpdateRequest  true  "模板更新请求参数"
// @Success      200      {object}  models.Response               "更新模板成功"
// @Failure      400      {object}  models.Response               "请求参数错误"
// @Failure      500      {object}  models.Response               "更新模板失败"
// @Router       /v1/alert/template/_update [post]
// @Security     ApiKeyAuth
func (a *ruleController) UpdateTemplate(c *gin.Context) {
	var Template co.TemplateCO
	if utils.CheckParams[co.TemplateCO](c, &Template) != nil {
		return
	}
	logger.Infof("传参：%v", Template)

	if array.Repeat[po.Perch](Template.PerchList, "Key") {
		c.JSON(http2.BadRequest().WithMsg("占位符Key不可以重复"))
		return
	}
	e := a.TemplateSvc.UpdateTemplate(&Template)
	if e != nil {
		logger.Error(e)
		c.JSON(http2.BadRequest().WithMsg("添加模版失败"))
		return
	}
	c.JSON(http2.Success(true))
	return
}

// PullTemplate 获取模板
// @Summary      获取模板
// @Description  获取告警模板列表
// @Tags         告警管理
// @Accept       json
// @Produce      json
// @Param        request  body      models.TemplateListRequest  true  "模板列表请求参数"
// @Success      200      {object}  models.Response             "获取模板成功"
// @Failure      400      {object}  models.Response             "请求参数错误"
// @Failure      500      {object}  models.Response             "获取模板失败"
// @Router       /v1/alert/template/_list [post]
// @Security     ApiKeyAuth
func (a *ruleController) PullTemplate(c *gin.Context) {
	var Template co.TemplateCO
	if utils.CheckParams[co.TemplateCO](c, &Template) != nil {
		return
	}
	e := a.TemplateSvc.PullTemplate(Template.AlertCode)
	c.JSON(http2.Success(e))
	return
}

// GetTree 获取树结构
// @Summary      获取树结构
// @Description  获取告警规则的树形结构
// @Tags         告警管理
// @Accept       json
// @Produce      json
// @Success      200  {object}  models.Response  "获取树结构成功"
// @Failure      500  {object}  models.Response  "获取树结构失败"
// @Router       /v1/alert/tree [get]
// @Security     ApiKeyAuth
func (a *ruleController) GetTree(c *gin.Context) {
	e := a.treeSvc.GetTree()
	req, _ := c.Get("users")
	logger.Infof("GetTree(c *gin.Context) %s", req)
	c.JSON(http2.Success(e))
	return
}
