package controllers

import (
	"blops/kube"
	"fmt"
	"net/http"
	"time"

	// 引入你的 config 包
	"context"

	"github.com/gin-gonic/gin"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
)

type PodController struct{}

var PodCtrl = &PodController{}

// HandlePodExec 处理 Pod 终端执行请求
func (c *PodController) HandlePodExec(w http.ResponseWriter, r *http.Request) {
	// WebSocket 连接不需要返回 JSON 响应，直接交给 kube 包处理
	clusterName := r.URL.Query().Get("cluster")
	namespace := r.URL.Query().Get("namespace")
	podName := r.URL.Query().Get("pod")
	containerName := r.URL.Query().Get("container")

	if clusterName == "" || namespace == "" || podName == "" {
		http.Error(w, "缺少必要参数", http.StatusBadRequest)
		return
	}

	kube.HandlePodExec(w, r, clusterName, namespace, podName, containerName)
}

// ExecCommand 执行Pod命令
func (c *PodController) ExecCommand(ctx *gin.Context) {
	clusterName := ctx.Query("cluster")
	namespace := ctx.Query("namespace")
	podName := ctx.Query("pod")
	command := ctx.Query("command")

	if clusterName == "" || namespace == "" || podName == "" || command == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "cluster, namespace, pod and command are required"})
		return
	}

	// 使用 /bin/sh -c 执行命令
	commands := []string{"/bin/bash", "-c", command}
	output, err := kube.ExecInPod(clusterName, namespace, podName, commands)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"output": output})
}

// ListPods godoc
// @Summary Get pods in a namespace
// @Description Get a list of pods in the specified cluster and namespace
// @Tags cluster
// @Accept json
// @Produce json
// @Param cluster query string true "Cluster name"
// @Param namespace query string true "Namespace name"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/cluster/pods [get]
func (c *PodController) ListPods(ctx *gin.Context) {
	clusterName := ctx.Query("cluster")
	namespace := ctx.Query("namespace")

	if clusterName == "" || namespace == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"result":  "ERROR",
			"message": "cluster and namespace parameters are required",
		})
		return
	}

	// Use the existing GetKubeConfig function from the kube package
	restConfig := kube.GetKubeConfig(clusterName)
	if restConfig == nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"result":  "ERROR",
			"message": "Failed to get Kubernetes config",
		})
		return
	}

	// Create Kubernetes client
	clientset, err := kubernetes.NewForConfig(restConfig)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"result":  "ERROR",
			"message": "Failed to create Kubernetes client: " + err.Error(),
		})
		return
	}

	// Get Pod list
	pods, err := clientset.CoreV1().Pods(namespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"result":  "ERROR",
			"message": "Failed to list pods: " + err.Error(),
		})
		return
	}

	// Construct response data
	podList := make([]map[string]interface{}, 0)
	for _, pod := range pods.Items {
		// Calculate READY status (containers ready / total containers)
		readyContainers := 0
		totalContainers := len(pod.Spec.Containers)
		for _, containerStatus := range pod.Status.ContainerStatuses {
			if containerStatus.Ready {
				readyContainers++
			}
		}
		readyStatus := fmt.Sprintf("%d/%d", readyContainers, totalContainers)

		// Get restart count
		restartCount := 0
		for _, containerStatus := range pod.Status.ContainerStatuses {
			restartCount += int(containerStatus.RestartCount)
		}

		// Calculate pod age
		podAge := time.Since(pod.CreationTimestamp.Time).Round(time.Second).String()

		podInfo := map[string]interface{}{
			"name":      pod.Name,
			"namespace": pod.Namespace,
			"ready":     readyStatus,
			"status":    string(pod.Status.Phase),
			"restarts":  restartCount,
			"age":       podAge,
			"ip":        pod.Status.PodIP,
			"node":      pod.Spec.NodeName,
		}
		podList = append(podList, podInfo)
	}

	ctx.JSON(http.StatusOK, gin.H{
		"result": "SUCCESS",
		"data":   podList,
	})
}
