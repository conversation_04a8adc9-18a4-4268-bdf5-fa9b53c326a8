package controllers

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// 状态码
const (
	CodeSuccess       = 200
	CodeInvalidParams = 400
	CodeUnauthorized  = 401
	CodeForbidden     = 403
	CodeNotFound      = 404
	CodeInternalError = 500
)

// Response API响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// RespondWithSuccess 返回成功响应
func RespondWithSuccess(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    CodeSuccess,
		Message: "SUCCESS",
		Data:    data,
	})
}

// RespondWithError 返回错误响应
func RespondWithError(c *gin.Context, code int, message string) {
	status := http.StatusOK
	if code == CodeInvalidParams {
		status = http.StatusBadRequest
	} else if code == CodeUnauthorized {
		status = http.StatusUnauthorized
	} else if code == CodeForbidden {
		status = http.StatusForbidden
	} else if code == CodeNotFound {
		status = http.StatusNotFound
	} else if code == CodeInternalError {
		status = http.StatusInternalServerError
	}

	c.JSON(status, Response{
		Code:    code,
		Message: message,
	})
}
