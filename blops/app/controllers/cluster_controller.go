package controllers

import (
	"blops/kube"
	"blops/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

type ClusterController struct{}

var ClusterCtrl = &ClusterController{}

// ListClusters 获取集群列表
// @Summary      获取集群列表
// @Description  获取所有可用的Kubernetes集群列表
// @Tags         集群管理
// @Accept       json
// @Produce      json
// @Success      200  {object}  models.Response  "获取集群列表成功"
// @Failure      500  {object}  models.Response  "获取集群列表失败"
// @Router       /v1/cluster/list [post]
// @Security     ApiKeyAuth
func (c *ClusterController) ListClusters(ctx *gin.Context) {
	clusters, err := kube.ListClusters()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, utils.Response{
			Code:    http.StatusInternalServerError,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	ctx.JSON(http.StatusOK, utils.Response{
		Code:    http.StatusOK,
		Message: "success",
		Result:  "SUCCESS",
		Data:    clusters,
	})
}

// ListNamespaces 获取命名空间列表
// @Summary      获取命名空间列表
// @Description  获取指定集群中的所有命名空间
// @Tags         集群管理
// @Accept       json
// @Produce      json
// @Param        request  body      models.ClusterRequest  true  "集群请求参数"
// @Success      200      {object}  models.Response        "获取命名空间列表成功"
// @Failure      400      {object}  models.Response        "请求参数错误"
// @Failure      500      {object}  models.Response        "获取命名空间列表失败"
// @Router       /v1/cluster/namespace/list [post]
// @Security     ApiKeyAuth
func (c *ClusterController) ListNamespaces(ctx *gin.Context) {
	var req struct {
		Cluster string `json:"cluster" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, utils.Response{
			Code:    http.StatusBadRequest,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	namespaces, err := kube.ListNamespaces(req.Cluster)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, utils.Response{
			Code:    http.StatusInternalServerError,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	ctx.JSON(http.StatusOK, utils.Response{
		Code:    http.StatusOK,
		Message: "success",
		Result:  "SUCCESS",
		Data:    namespaces,
	})
}

// ListDeployments 获取部署列表
// @Summary      获取部署列表
// @Description  获取指定集群和命名空间中的所有部署
// @Tags         集群管理
// @Accept       json
// @Produce      json
// @Param        request  body      models.NamespaceRequest  true  "命名空间请求参数"
// @Success      200      {object}  models.Response          "获取部署列表成功"
// @Failure      400      {object}  models.Response          "请求参数错误"
// @Failure      500      {object}  models.Response          "获取部署列表失败"
// @Router       /v1/cluster/deployment/list [post]
// @Security     ApiKeyAuth
func (c *ClusterController) ListDeployments(ctx *gin.Context) {
	var req struct {
		Cluster   string `json:"cluster" binding:"required"`
		Namespace string `json:"namespace" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, utils.Response{
			Code:    http.StatusBadRequest,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	deployments, err := kube.ListDeployments(req.Cluster, req.Namespace)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, utils.Response{
			Code:    http.StatusInternalServerError,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	ctx.JSON(http.StatusOK, utils.Response{
		Code:    http.StatusOK,
		Message: "success",
		Result:  "SUCCESS",
		Data:    deployments,
	})
}

// ListServices 获取服务列表
// @Summary      获取服务列表
// @Description  获取指定集群和命名空间中的所有服务
// @Tags         集群管理
// @Accept       json
// @Produce      json
// @Param        request  body      models.NamespaceRequest  true  "命名空间请求参数"
// @Success      200      {object}  models.Response          "获取服务列表成功"
// @Failure      400      {object}  models.Response          "请求参数错误"
// @Failure      500      {object}  models.Response          "获取服务列表失败"
// @Router       /v1/cluster/service/list [post]
// @Security     ApiKeyAuth
func (c *ClusterController) ListServices(ctx *gin.Context) {
	var req struct {
		Cluster   string `json:"cluster" binding:"required"`
		Namespace string `json:"namespace" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, utils.Response{
			Code:    http.StatusBadRequest,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	services, err := kube.ListServices(req.Cluster, req.Namespace)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, utils.Response{
			Code:    http.StatusInternalServerError,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	ctx.JSON(http.StatusOK, utils.Response{
		Code:    http.StatusOK,
		Message: "success",
		Result:  "SUCCESS",
		Data:    services,
	})
}

// ListIngresses 获取入口列表
// @Summary      获取入口列表
// @Description  获取指定集群和命名空间中的所有入口
// @Tags         集群管理
// @Accept       json
// @Produce      json
// @Param        request  body      models.NamespaceRequest  true  "命名空间请求参数"
// @Success      200      {object}  models.Response          "获取入口列表成功"
// @Failure      400      {object}  models.Response          "请求参数错误"
// @Failure      500      {object}  models.Response          "获取入口列表失败"
// @Router       /v1/cluster/ingress/list [post]
// @Security     ApiKeyAuth
func (c *ClusterController) ListIngresses(ctx *gin.Context) {
	var req struct {
		Cluster   string `json:"cluster" binding:"required"`
		Namespace string `json:"namespace" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, utils.Response{
			Code:    http.StatusBadRequest,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	ingresses, err := kube.ListIngresses(req.Cluster, req.Namespace)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, utils.Response{
			Code:    http.StatusInternalServerError,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	ctx.JSON(http.StatusOK, utils.Response{
		Code:    http.StatusOK,
		Message: "success",
		Result:  "SUCCESS",
		Data:    ingresses,
	})
}

// ListPodsByDeployment 获取部署的Pod列表
// @Summary      获取部署的Pod列表
// @Description  获取指定部署的所有Pod
// @Tags         集群管理
// @Accept       json
// @Produce      json
// @Param        request  body      models.DeploymentRequest  true  "部署请求参数"
// @Success      200      {object}  models.Response           "获取Pod列表成功"
// @Failure      400      {object}  models.Response           "请求参数错误"
// @Failure      500      {object}  models.Response           "获取Pod列表失败"
// @Router       /v1/cluster/deployment/pods [post]
// @Security     ApiKeyAuth
func (c *ClusterController) ListPodsByDeployment(ctx *gin.Context) {
	var req struct {
		Cluster    string `json:"cluster" binding:"required"`
		Namespace  string `json:"namespace" binding:"required"`
		Deployment string `json:"deployment" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, utils.Response{
			Code:    http.StatusBadRequest,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	pods, err := kube.ListPodsByDeployment(req.Cluster, req.Namespace, req.Deployment)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, utils.Response{
			Code:    http.StatusInternalServerError,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	ctx.JSON(http.StatusOK, utils.Response{
		Code:    http.StatusOK,
		Message: "success",
		Result:  "SUCCESS",
		Data:    pods,
	})
}

// GetResource 获取资源
// @Summary      获取资源
// @Description  获取指定类型的Kubernetes资源
// @Tags         集群管理
// @Accept       json
// @Produce      json
// @Param        request  body      models.ResourceRequest  true  "资源请求参数"
// @Success      200      {object}  models.Response         "获取资源成功"
// @Failure      400      {object}  models.Response         "请求参数错误"
// @Failure      500      {object}  models.Response         "获取资源失败"
// @Router       /v1/cluster/resource/get [post]
// @Security     ApiKeyAuth
func (c *ClusterController) GetResource(ctx *gin.Context) {
	var req struct {
		Cluster      string `json:"cluster" binding:"required"`
		Namespace    string `json:"namespace" binding:"required"`
		ResourceType string `json:"resourceType" binding:"required"`
		Name         string `json:"name" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, utils.Response{
			Code:    http.StatusBadRequest,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	resource, err := kube.GetResource(req.Cluster, req.Namespace, req.ResourceType, req.Name)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, utils.Response{
			Code:    http.StatusInternalServerError,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	ctx.JSON(http.StatusOK, utils.Response{
		Code:    http.StatusOK,
		Message: "success",
		Result:  "SUCCESS",
		Data:    resource,
	})
}

// CreateResource 创建资源
// @Summary      创建资源
// @Description  创建Kubernetes资源
// @Tags         集群管理
// @Accept       json
// @Produce      json
// @Param        request  body      models.CreateResourceRequest  true  "创建资源请求参数"
// @Success      200      {object}  models.Response               "创建资源成功"
// @Failure      400      {object}  models.Response               "请求参数错误"
// @Failure      500      {object}  models.Response               "创建资源失败"
// @Router       /v1/cluster/resource/create [post]
// @Security     ApiKeyAuth
func (c *ClusterController) CreateResource(ctx *gin.Context) {
	var req struct {
		Cluster      string `json:"cluster" binding:"required"`
		Namespace    string `json:"namespace" binding:"required"`
		ResourceType string `json:"resourceType" binding:"required"`
		YamlData     string `json:"yamlData" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, utils.Response{
			Code:    http.StatusBadRequest,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	resource, err := kube.CreateResource(req.Cluster, req.Namespace, req.ResourceType, req.YamlData)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, utils.Response{
			Code:    http.StatusInternalServerError,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	ctx.JSON(http.StatusOK, utils.Response{
		Code:    http.StatusOK,
		Message: "success",
		Result:  "SUCCESS",
		Data:    resource,
	})
}

// UpdateResource 更新资源
// @Summary      更新资源
// @Description  更新Kubernetes资源
// @Tags         集群管理
// @Accept       json
// @Produce      json
// @Param        request  body      models.UpdateResourceRequest  true  "更新资源请求参数"
// @Success      200      {object}  models.Response               "更新资源成功"
// @Failure      400      {object}  models.Response               "请求参数错误"
// @Failure      500      {object}  models.Response               "更新资源失败"
// @Router       /v1/cluster/resource/update [post]
// @Security     ApiKeyAuth
func (c *ClusterController) UpdateResource(ctx *gin.Context) {
	var req struct {
		Cluster      string `json:"cluster" binding:"required"`
		Namespace    string `json:"namespace" binding:"required"`
		ResourceType string `json:"resourceType" binding:"required"`
		Name         string `json:"name" binding:"required"`
		YamlData     string `json:"yamlData" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, utils.Response{
			Code:    http.StatusBadRequest,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	resource, err := kube.UpdateResource(req.Cluster, req.Namespace, req.ResourceType, req.Name, req.YamlData)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, utils.Response{
			Code:    http.StatusInternalServerError,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	ctx.JSON(http.StatusOK, utils.Response{
		Code:    http.StatusOK,
		Message: "success",
		Result:  "SUCCESS",
		Data:    resource,
	})
}

// DeleteResource 删除资源
// @Summary      删除资源
// @Description  删除Kubernetes资源
// @Tags         集群管理
// @Accept       json
// @Produce      json
// @Param        request  body      models.DeleteResourceRequest  true  "删除资源请求参数"
// @Success      200      {object}  models.Response               "删除资源成功"
// @Failure      400      {object}  models.Response               "请求参数错误"
// @Failure      500      {object}  models.Response               "删除资源失败"
// @Router       /v1/cluster/resource/delete [post]
// @Security     ApiKeyAuth
func (c *ClusterController) DeleteResource(ctx *gin.Context) {
	var req struct {
		Cluster      string `json:"cluster" binding:"required"`
		Namespace    string `json:"namespace" binding:"required"`
		ResourceType string `json:"resourceType" binding:"required"`
		Name         string `json:"name" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, utils.Response{
			Code:    http.StatusBadRequest,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	err := kube.DeleteResource(req.Cluster, req.Namespace, req.ResourceType, req.Name)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, utils.Response{
			Code:    http.StatusInternalServerError,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	ctx.JSON(http.StatusOK, utils.Response{
		Code:    http.StatusOK,
		Message: "success",
		Result:  "SUCCESS",
	})
}
