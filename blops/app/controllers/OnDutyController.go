package controllers

import (
	"blops/app/co"
	"blops/app/models/po"
	"blops/app/services/impl"
	"blops/enums/http"
	"blops/utils"
	"github.com/gin-gonic/gin"
)

type OnDutyController struct{}

var (
	OnDutyCtrl *OnDutyController
	svc        *impl.OnDutyService
)

func init() {
	OnDutyCtrl = &OnDutyController{}
	svc = &impl.OnDutyService{}
}

func (ctrl *OnDutyController) NewOnDutyUser(c *gin.Context) {
	var onDutyCO co.OnDutyCO
	if utils.CheckParams[co.OnDutyCO](c, &onDutyCO) != nil {
		return
	}
	dutyPO := &po.OnDutyPO{
		UserType: onDutyCO.UserType,
		UserId:   onDutyCO.UserId,
		Status:   "off",
		Start:    onDutyCO.Start,
		End:      onDutyCO.End,
	}
	err := svc.New(dutyPO)
	if err != nil {
		c.JSON(http.ServerError().WithMsg(err.Error()))
		return
	}
	c.J<PERSON>(http.Success(dutyPO.ID))
}

func (ctrl *OnDutyController) CustomSet(c *gin.Context) {
	var onDutyCO co.OnDutyCustomSetCO
	if utils.CheckParams[co.OnDutyCustomSetCO](c, &onDutyCO) != nil {
		return
	}
	err := svc.Modify(onDutyCO)
	if err != nil {
		c.JSON(http.ServerError().WithMsg(err.Error()))
		return
	}
	c.JSON(http.Success(onDutyCO.ID))
}
