package controllers

import (
	"blops/app/models/dto"
	. "blops/app/services"
	. "blops/app/services/impl"
	. "blops/enums/http"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// MiddlewareController 中间件控制器
type MiddlewareController struct {
	middlewareSvc MiddlewareServiceInf
}

// 全局中间件控制器实例
var MiddlewareCtrl *MiddlewareController

func init() {
	MiddlewareCtrl = &MiddlewareController{
		middlewareSvc: MiddlewareSvc,
	}
}

// =============== 环境相关接口 ===============

// GetEnv 获取环境详情
// @Summary      获取环境详情
// @Description  根据ID获取环境详情
// @Tags         中间件管理
// @Accept       json
// @Produce      json
// @Param        id   path      int  true  "环境ID"
// @Success      200  {object}  models.Response{data=dto.MiddlewareEnvResponse}
// @Failure      400  {object}  models.Response
// @Router       /v1/middleware/env/{id} [get]
// @Security     ApiKeyAuth
func (ctrl *MiddlewareController) GetEnv(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的环境ID"}))
		return
	}

	env, err := ctrl.middlewareSvc.GetEnv(id)
	if err != nil {
		c.JSON(http.StatusNotFound, NotFound(Options{Msg: "环境不存在"}))
		return
	}

	statusCode, response := Success(dto.ToMiddlewareEnvResponse(env))
	c.JSON(statusCode, response)
}

// ListEnvs 获取环境列表
// @Summary      获取环境列表
// @Description  获取所有环境列表
// @Tags         中间件管理
// @Accept       json
// @Produce      json
// @Success      200  {object}  models.Response{data=[]dto.MiddlewareEnvResponse}
// @Failure      500  {object}  models.Response
// @Router       /v1/middleware/env [get]
// @Security     ApiKeyAuth
func (ctrl *MiddlewareController) ListEnvs(c *gin.Context) {
	envs, err := ctrl.middlewareSvc.ListEnvs()
	if err != nil {
		c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: "获取环境列表失败"}))
		return
	}

	// 转换为响应DTO
	result := make([]*dto.MiddlewareEnvResponse, 0, len(envs))
	for _, env := range envs {
		result = append(result, dto.ToMiddlewareEnvResponse(env))
	}

	statusCode, response := Success(result)
	c.JSON(statusCode, response)
}

// CreateEnv 创建环境
// @Summary      创建环境
// @Description  创建新的环境
// @Tags         中间件管理
// @Accept       json
// @Produce      json
// @Param        request  body      dto.MiddlewareEnvCreateRequest  true  "创建环境请求"
// @Success      200      {object}  models.Response{data=dto.MiddlewareEnvResponse}
// @Failure      400      {object}  models.Response
// @Failure      500      {object}  models.Response
// @Router       /v1/middleware/env [post]
// @Security     ApiKeyAuth
func (ctrl *MiddlewareController) CreateEnv(c *gin.Context) {
	var req dto.MiddlewareEnvCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的请求参数"}))
		return
	}

	env := req.ToMiddlewareEnvPO()
	if err := ctrl.middlewareSvc.CreateEnv(env); err != nil {
		c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: "创建环境失败"}))
		return
	}

	statusCode, response := Success(dto.ToMiddlewareEnvResponse(env))
	c.JSON(statusCode, response)
}

// UpdateEnv 更新环境
// @Summary      更新环境
// @Description  更新现有环境
// @Tags         中间件管理
// @Accept       json
// @Produce      json
// @Param        request  body      dto.MiddlewareEnvUpdateRequest  true  "更新环境请求"
// @Success      200      {object}  models.Response
// @Failure      400      {object}  models.Response
// @Failure      404      {object}  models.Response
// @Failure      500      {object}  models.Response
// @Router       /v1/middleware/env [put]
// @Security     ApiKeyAuth
func (ctrl *MiddlewareController) UpdateEnv(c *gin.Context) {
	var req dto.MiddlewareEnvUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的请求参数"}))
		return
	}

	// 检查环境是否存在
	_, err := ctrl.middlewareSvc.GetEnv(req.ID)
	if err != nil {
		c.JSON(http.StatusNotFound, NotFound(Options{Msg: "环境不存在"}))
		return
	}

	env := req.ToMiddlewareEnvPO()
	if err := ctrl.middlewareSvc.UpdateEnv(env); err != nil {
		c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: "更新环境失败"}))
		return
	}

	statusCode, response := Success(nil)
	c.JSON(statusCode, response)
}

// DeleteEnv 删除环境
// @Summary      删除环境
// @Description  删除指定环境
// @Tags         中间件管理
// @Accept       json
// @Produce      json
// @Param        id   path      int  true  "环境ID"
// @Success      200  {object}  models.Response
// @Failure      400  {object}  models.Response
// @Failure      404  {object}  models.Response
// @Failure      500  {object}  models.Response
// @Router       /v1/middleware/env/{id} [delete]
// @Security     ApiKeyAuth
func (ctrl *MiddlewareController) DeleteEnv(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的环境ID"}))
		return
	}

	// 检查环境是否存在
	_, err = ctrl.middlewareSvc.GetEnv(id)
	if err != nil {
		c.JSON(http.StatusNotFound, NotFound(Options{Msg: "环境不存在"}))
		return
	}

	if err := ctrl.middlewareSvc.DeleteEnv(id); err != nil {
		c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: "删除环境失败"}))
		return
	}

	statusCode, response := Success(nil)
	c.JSON(statusCode, response)
}

// =============== 链接相关接口 ===============

// GetLink 获取链接详情
// @Summary      获取链接详情
// @Description  根据ID获取链接详情
// @Tags         中间件管理
// @Accept       json
// @Produce      json
// @Param        id   path      int  true  "链接ID"
// @Success      200  {object}  models.Response{data=dto.MiddlewareLinkResponse}
// @Failure      400  {object}  models.Response
// @Router       /v1/middleware/link/{id} [get]
// @Security     ApiKeyAuth
func (ctrl *MiddlewareController) GetLink(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的链接ID"}))
		return
	}

	link, err := ctrl.middlewareSvc.GetLink(id)
	if err != nil {
		c.JSON(http.StatusNotFound, NotFound(Options{Msg: "链接不存在"}))
		return
	}

	statusCode, response := Success(dto.ToMiddlewareLinkResponse(link))
	c.JSON(statusCode, response)
}

// ListLinks 获取链接列表
// @Summary      获取链接列表
// @Description  获取所有链接列表
// @Tags         中间件管理
// @Accept       json
// @Produce      json
// @Param        with_env_info  query     bool    false  "是否包含环境信息"
// @Success      200            {object}  models.Response{data=[]dto.MiddlewareLinkResponse}
// @Failure      500            {object}  models.Response
// @Router       /v1/middleware/link [get]
// @Security     ApiKeyAuth
func (ctrl *MiddlewareController) ListLinks(c *gin.Context) {
	withEnvInfo := c.Query("with_env_info") == "true"

	var links []*dto.MiddlewareLinkResponse

	if withEnvInfo {
		linksData, err := ctrl.middlewareSvc.ListLinksWithEnvInfo()
		if err != nil {
			c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: "获取链接列表失败"}))
			return
		}

		links = make([]*dto.MiddlewareLinkResponse, 0, len(linksData))
		for _, link := range linksData {
			links = append(links, dto.ToMiddlewareLinkResponse(link))
		}
	} else {
		linksData, err := ctrl.middlewareSvc.ListLinks()
		if err != nil {
			c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: "获取链接列表失败"}))
			return
		}

		links = make([]*dto.MiddlewareLinkResponse, 0, len(linksData))
		for _, link := range linksData {
			links = append(links, dto.ToMiddlewareLinkResponse(link))
		}
	}

	statusCode, response := Success(links)
	c.JSON(statusCode, response)
}

// ListLinksByType 按类型获取链接
// @Summary      按类型获取链接
// @Description  根据类型获取链接列表
// @Tags         中间件管理
// @Accept       json
// @Produce      json
// @Param        request      body      dto.MiddlewareLinkTypeFilterRequest  true   "类型过滤请求"
// @Param        with_env_info  query     bool                                false  "是否包含环境信息"
// @Success      200          {object}  models.Response{data=[]dto.MiddlewareLinkResponse}
// @Failure      400          {object}  models.Response
// @Failure      500          {object}  models.Response
// @Router       /v1/middleware/link/by-type [post]
// @Security     ApiKeyAuth
func (ctrl *MiddlewareController) ListLinksByType(c *gin.Context) {
	var req dto.MiddlewareLinkTypeFilterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的请求参数"}))
		return
	}

	withEnvInfo := c.Query("with_env_info") == "true"

	var links []*dto.MiddlewareLinkResponse

	if withEnvInfo {
		linksData, err := ctrl.middlewareSvc.ListLinksByTypeWithEnvInfo(req.Type)
		if err != nil {
			c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: "获取链接列表失败"}))
			return
		}

		links = make([]*dto.MiddlewareLinkResponse, 0, len(linksData))
		for _, link := range linksData {
			links = append(links, dto.ToMiddlewareLinkResponse(link))
		}
	} else {
		linksData, err := ctrl.middlewareSvc.ListLinksByType(req.Type)
		if err != nil {
			c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: "获取链接列表失败"}))
			return
		}

		links = make([]*dto.MiddlewareLinkResponse, 0, len(linksData))
		for _, link := range linksData {
			links = append(links, dto.ToMiddlewareLinkResponse(link))
		}
	}

	statusCode, response := Success(links)
	c.JSON(statusCode, response)
}

// ListLinksByEnv 按环境获取链接
// @Summary      按环境获取链接
// @Description  根据环境ID获取链接列表
// @Tags         中间件管理
// @Accept       json
// @Produce      json
// @Param        request  body      dto.MiddlewareLinkEnvFilterRequest  true  "环境过滤请求"
// @Success      200      {object}  models.Response{data=[]dto.MiddlewareLinkResponse}
// @Failure      400      {object}  models.Response
// @Failure      500      {object}  models.Response
// @Router       /v1/middleware/link/by-env [post]
// @Security     ApiKeyAuth
func (ctrl *MiddlewareController) ListLinksByEnv(c *gin.Context) {
	var req dto.MiddlewareLinkEnvFilterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的请求参数"}))
		return
	}

	linksData, err := ctrl.middlewareSvc.ListLinksByEnv(req.Env)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: "获取链接列表失败"}))
		return
	}

	links := make([]*dto.MiddlewareLinkResponse, 0, len(linksData))
	for _, link := range linksData {
		links = append(links, dto.ToMiddlewareLinkResponse(link))
	}

	statusCode, response := Success(links)
	c.JSON(statusCode, response)
}

// ListLinksByTypeAndEnv 按类型和环境获取链接
// @Summary      按类型和环境获取链接
// @Description  根据类型和环境ID获取链接列表
// @Tags         中间件管理
// @Accept       json
// @Produce      json
// @Param        request  body      dto.MiddlewareLinkTypeAndEnvFilterRequest  true  "类型和环境过滤请求"
// @Success      200      {object}  models.Response{data=[]dto.MiddlewareLinkResponse}
// @Failure      400      {object}  models.Response
// @Failure      500      {object}  models.Response
// @Router       /v1/middleware/link/by-type-env [post]
// @Security     ApiKeyAuth
func (ctrl *MiddlewareController) ListLinksByTypeAndEnv(c *gin.Context) {
	var req dto.MiddlewareLinkTypeAndEnvFilterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的请求参数"}))
		return
	}

	linksData, err := ctrl.middlewareSvc.ListLinksByTypeAndEnv(req.Type, req.Env)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: "获取链接列表失败"}))
		return
	}

	links := make([]*dto.MiddlewareLinkResponse, 0, len(linksData))
	for _, link := range linksData {
		links = append(links, dto.ToMiddlewareLinkResponse(link))
	}

	statusCode, response := Success(links)
	c.JSON(statusCode, response)
}

// CreateLink 创建链接
// @Summary      创建链接
// @Description  创建新的链接
// @Tags         中间件管理
// @Accept       json
// @Produce      json
// @Param        request  body      dto.MiddlewareLinkCreateRequest  true  "创建链接请求"
// @Success      200      {object}  models.Response{data=dto.MiddlewareLinkResponse}
// @Failure      400      {object}  models.Response
// @Failure      500      {object}  models.Response
// @Router       /v1/middleware/link [post]
// @Security     ApiKeyAuth
func (ctrl *MiddlewareController) CreateLink(c *gin.Context) {
	var req dto.MiddlewareLinkCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的请求参数"}))
		return
	}

	// 检查环境是否存在
	_, err := ctrl.middlewareSvc.GetEnv(req.Env)
	if err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "所选环境不存在"}))
		return
	}

	link := req.ToMiddlewareLinkPO()
	if err := ctrl.middlewareSvc.CreateLink(link); err != nil {
		c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: "创建链接失败"}))
		return
	}

	statusCode, response := Success(dto.ToMiddlewareLinkResponse(link))
	c.JSON(statusCode, response)
}

// UpdateLink 更新链接
// @Summary      更新链接
// @Description  更新现有链接
// @Tags         中间件管理
// @Accept       json
// @Produce      json
// @Param        request  body      dto.MiddlewareLinkUpdateRequest  true  "更新链接请求"
// @Success      200      {object}  models.Response
// @Failure      400      {object}  models.Response
// @Failure      404      {object}  models.Response
// @Failure      500      {object}  models.Response
// @Router       /v1/middleware/link [put]
// @Security     ApiKeyAuth
func (ctrl *MiddlewareController) UpdateLink(c *gin.Context) {
	var req dto.MiddlewareLinkUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的请求参数"}))
		return
	}

	// 检查链接是否存在
	_, err := ctrl.middlewareSvc.GetLink(req.ID)
	if err != nil {
		c.JSON(http.StatusNotFound, NotFound(Options{Msg: "链接不存在"}))
		return
	}

	// 检查环境是否存在
	_, err = ctrl.middlewareSvc.GetEnv(req.Env)
	if err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "所选环境不存在"}))
		return
	}

	link := req.ToMiddlewareLinkPO()
	if err := ctrl.middlewareSvc.UpdateLink(link); err != nil {
		c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: "更新链接失败"}))
		return
	}

	statusCode, response := Success(nil)
	c.JSON(statusCode, response)
}

// DeleteLink 删除链接
// @Summary      删除链接
// @Description  删除指定链接
// @Tags         中间件管理
// @Accept       json
// @Produce      json
// @Param        id   path      int  true  "链接ID"
// @Success      200  {object}  models.Response
// @Failure      400  {object}  models.Response
// @Failure      404  {object}  models.Response
// @Failure      500  {object}  models.Response
// @Router       /v1/middleware/link/{id} [delete]
// @Security     ApiKeyAuth
func (ctrl *MiddlewareController) DeleteLink(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, BadRequest(Options{Msg: "无效的链接ID"}))
		return
	}

	// 检查链接是否存在
	_, err = ctrl.middlewareSvc.GetLink(id)
	if err != nil {
		c.JSON(http.StatusNotFound, NotFound(Options{Msg: "链接不存在"}))
		return
	}

	if err := ctrl.middlewareSvc.DeleteLink(id); err != nil {
		c.JSON(http.StatusInternalServerError, ServerError(Options{Msg: "删除链接失败"}))
		return
	}

	statusCode, response := Success(nil)
	c.JSON(statusCode, response)
}
