package controllers

import (
	"blops/app/dtos"
	"blops/app/services"
	"blops/app/services/impl"
	. "blops/enums/http"
	"blops/logger"

	"github.com/gin-gonic/gin"
)

type userController struct {
	userService services.UserService
}

var UserCtrl *userController

func init() {
	UserCtrl = &userController{
		userService: impl.UserSvcImpl,
	}
}

// Login 用户登录
// @Summary      用户登录
// @Description  用户登录并获取认证令牌
// @Tags         用户管理
// @Accept       json
// @Produce      json
// @Param        request  body      models.LoginRequest  true  "登录请求参数"
// @Success      200      {object}  models.Response      "登录成功"
// @Failure      400      {object}  models.Response      "请求参数错误"
// @Failure      401      {object}  models.Response      "认证失败"
// @Failure      500      {object}  models.Response      "登录失败"
// @Router       /v1/user/_login [post]
func (ctrl *userController) Login(c *gin.Context) {
	var userLoginReq *dtos.UserLoginReqDTO
	err := c.ShouldBindJSON(&userLoginReq)
	if err != nil || userLoginReq == nil {
		logger.Error(err)
		c.JSON(BadRequest().Error())
		return
	}
	userPO, lErr := ctrl.userService.Login(userLoginReq.Username, userLoginReq.Password)
	if lErr != nil {
		c.JSON(lErr.Error())
		return
	}
	c.JSON(Success(userPO))
}
