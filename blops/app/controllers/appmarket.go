package controllers

import (
	"time"

	"blops/app/models"
	"blops/app/services"
	"blops/logger"
	"strconv"

	"github.com/gin-gonic/gin"
)

// AppMarketController 应用市场控制器
type AppMarketController struct {
	appMarketService services.AppMarketService
}

// NewAppMarketController 创建应用市场控制器
func NewAppMarketController() *AppMarketController {
	return &AppMarketController{
		appMarketService: services.NewAppMarketService(),
	}
}

// ListAppTemplates 获取应用模板列表
// @Summary 获取应用模板列表
// @Description 获取所有可用的应用模板
// @Tags 应用市场
// @Accept json
// @Produce json
// @Param request body models.AppTemplateListRequest true "列表请求参数"
// @Success 200 {object} models.Response
// @Router /v1/app-market/templates/_list [post]
func (c *AppMarketController) ListAppTemplates(ctx *gin.Context) {
	var req models.AppTemplateListRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Errorf("解析请求参数失败: %v", err)
		RespondWithError(ctx, CodeInvalidParams, "解析请求参数失败")
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 获取应用模板列表
	response, err := c.appMarketService.GetAppTemplates(&req)
	if err != nil {
		logger.Errorf("获取应用模板列表失败: %v", err)
		RespondWithError(ctx, CodeInternalError, "获取应用模板列表失败")
		return
	}

	RespondWithSuccess(ctx, response)
}

// GetAppTemplateDetail 获取应用模板详情
// @Summary 获取应用模板详情
// @Description 获取指定应用模板的详细信息
// @Tags 应用市场
// @Accept json
// @Produce json
// @Param name path string true "模板名称"
// @Success 200 {object} models.Response
// @Router /v1/app-market/templates/{name} [get]
func (c *AppMarketController) GetAppTemplateDetail(ctx *gin.Context) {
	templateName := ctx.Param("templateName")
	if templateName == "" {
		RespondWithError(ctx, CodeInvalidParams, "模板名称不能为空")
		return
	}

	// 获取应用模板详情
	template, err := c.appMarketService.GetAppTemplateDetail(templateName)
	if err != nil {
		logger.Errorf("获取应用模板详情失败: %v", err)
		RespondWithError(ctx, CodeInternalError, "获取应用模板详情失败")
		return
	}

	RespondWithSuccess(ctx, template)
}

// AddAppTemplate 添加应用模板
// @Summary 添加应用模板
// @Description 添加新的应用模板
// @Tags 应用市场
// @Accept json
// @Produce json
// @Param request body models.AppTemplateRequest true "模板请求参数"
// @Success 200 {object} models.Response
// @Router /v1/app-market/templates [post]
func (c *AppMarketController) AddAppTemplate(ctx *gin.Context) {
	var req models.AppTemplateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Errorf("解析请求参数失败: %v", err)
		RespondWithError(ctx, CodeInvalidParams, "解析请求参数失败")
		return
	}

	// 转换为DTO
	template := req.AppTemplateToDTO()

	// 添加应用模板
	if err := c.appMarketService.AddAppTemplate(template); err != nil {
		logger.Errorf("添加应用模板失败: %v", err)
		RespondWithError(ctx, CodeInternalError, "添加应用模板失败")
		return
	}

	RespondWithSuccess(ctx, nil)
}

// UpdateAppTemplate 更新应用模板
// @Summary 更新应用模板
// @Description 更新指定的应用模板
// @Tags 应用市场
// @Accept json
// @Produce json
// @Param name path string true "模板名称"
// @Param request body models.AppTemplateRequest true "模板请求参数"
// @Success 200 {object} models.Response
// @Router /v1/app-market/templates/{name} [put]
func (c *AppMarketController) UpdateAppTemplate(ctx *gin.Context) {
	name := ctx.Param("name")
	if name == "" {
		RespondWithError(ctx, CodeInvalidParams, "模板名称不能为空")
		return
	}

	var req models.AppTemplateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Errorf("解析请求参数失败: %v", err)
		RespondWithError(ctx, CodeInvalidParams, "解析请求参数失败")
		return
	}

	// 确保名称一致
	req.Name = name

	// 转换为DTO
	template := req.AppTemplateToDTO()

	// 更新应用模板
	if err := c.appMarketService.UpdateAppTemplate(template); err != nil {
		logger.Errorf("更新应用模板失败: %v", err)
		RespondWithError(ctx, CodeInternalError, "更新应用模板失败")
		return
	}

	RespondWithSuccess(ctx, nil)
}

// DeleteAppTemplate 删除应用模板
// @Summary 删除应用模板
// @Description 删除指定的应用模板
// @Tags 应用市场
// @Accept json
// @Produce json
// @Param name path string true "模板名称"
// @Success 200 {object} models.Response
// @Router /v1/app-market/templates/{name} [delete]
func (c *AppMarketController) DeleteAppTemplate(ctx *gin.Context) {
	name := ctx.Param("name")
	if name == "" {
		RespondWithError(ctx, CodeInvalidParams, "模板名称不能为空")
		return
	}

	// 删除应用模板
	if err := c.appMarketService.DeleteAppTemplate(name); err != nil {
		logger.Errorf("删除应用模板失败: %v", err)
		RespondWithError(ctx, CodeInternalError, "删除应用模板失败")
		return
	}

	RespondWithSuccess(ctx, nil)
}

// DeployApp 部署应用
// @Summary 部署应用
// @Description 部署应用到Kubernetes集群
// @Tags 应用市场
// @Accept json
// @Produce json
// @Param request body models.AppDeploymentRequest true "部署请求参数"
// @Success 200 {object} models.Response
// @Router /v1/app-market/deploy [post]
func (c *AppMarketController) DeployApp(ctx *gin.Context) {
	var req models.AppDeploymentRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		logger.Errorf("解析请求参数失败: %v", err)
		RespondWithError(ctx, CodeInvalidParams, "解析请求参数失败")
		return
	}

	// 转换为DTO
	deployment := req.AppDeploymentToDTO()

	// 部署应用
	yaml, err := c.appMarketService.DeployApp(deployment)
	if err != nil {
		logger.Errorf("部署应用失败: %v", err)
		RespondWithError(ctx, CodeInternalError, "部署应用失败")
		return
	}

	RespondWithSuccess(ctx, gin.H{
		"yaml": yaml,
	})
}

// ListDeployedApps 获取已部署应用列表
func (c *AppMarketController) ListDeployedApps(ctx *gin.Context) {
	apps, err := c.appMarketService.GetDeployedApps()
	if err != nil {
		RespondWithError(ctx, CodeInternalError, err.Error())
		return
	}
	RespondWithSuccess(ctx, apps)
}

// GetDeployedApp 获取单个已部署应用详情
func (c *AppMarketController) GetDeployedApp(ctx *gin.Context) {
	id, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		RespondWithError(ctx, CodeInvalidParams, "invalid id")
		return
	}
	app, err := c.appMarketService.GetDeployedAppByID(uint(id))
	if err != nil {
		RespondWithError(ctx, CodeNotFound, err.Error())
		return
	}
	RespondWithSuccess(ctx, app)
}

// CreateDeployedApp 新增已部署应用
func (c *AppMarketController) CreateDeployedApp(ctx *gin.Context) {
	// 1) 解析入参
	var req models.AppDeploymentRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		RespondWithError(ctx, CodeInvalidParams, err.Error())
		return
	}

	// 2) 在 k8s 集群中 deploy
	dto := req.AppDeploymentToDTO()
	createdResources, err := c.appMarketService.DeployApp(dto)
	if err != nil {
		logger.Errorf("部署失败: %v", err)
		RespondWithError(ctx, CodeInternalError, "部署应用失败: "+err.Error())
		return
	}
	// 优先选择Deployment作为主资源，否则取第一个作为名称标识
	primaryName := ""
	for _, r := range createdResources {
		if r.Kind == "Deployment" {
			primaryName = r.ResourceName
			break
		}
	}
	if primaryName == "" {
		primaryName = createdResources[0].ResourceName
	}

	// 3) 写入数据库 - 首先写入主部署记录
	record := models.DeployedApp{
		Name:      primaryName,
		Template:  req.TemplateName,
		Cluster:   req.ClusterName,
		Namespace: req.Namespace,
		Status:    "Running",
		CreatedAt: time.Now(),
	}
	if err := c.appMarketService.CreateDeployedApp(&record); err != nil {
		logger.Errorf("保存记录失败: %v", err)
		RespondWithError(ctx, CodeInternalError, "保存部署记录失败: "+err.Error())
		return
	}

	// 4) 插入所有部署的资源记录到数据库
	for _, r := range createdResources {
		resourceRecord := models.AppResource{
			AppID:        record.ID,
			Kind:         r.Kind,
			ResourceName: r.ResourceName,
		}
		if err := c.appMarketService.CreateAppResource(&resourceRecord); err != nil {
			logger.Errorf("保存资源记录失败: %v", err)
			// 根据业务需求，可以选择是否返回错误，此处忽略
		}
	}

	RespondWithSuccess(ctx, record)
}

// UpdateDeployedApp 更新已部署应用
func (c *AppMarketController) UpdateDeployedApp(ctx *gin.Context) {
	id, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		RespondWithError(ctx, CodeInvalidParams, "invalid id")
		return
	}
	var app models.DeployedApp
	if err := ctx.ShouldBindJSON(&app); err != nil {
		RespondWithError(ctx, CodeInvalidParams, err.Error())
		return
	}
	app.ID = uint(id)
	if err := c.appMarketService.UpdateDeployedApp(&app); err != nil {
		RespondWithError(ctx, CodeInternalError, err.Error())
		return
	}
	RespondWithSuccess(ctx, app)
}

// DeleteDeployedApp 删除已部署应用及其 Kubernetes 资源
func (c *AppMarketController) DeleteDeployedApp(ctx *gin.Context) {
	// 1) 解析 ID
	id, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		RespondWithError(ctx, CodeInvalidParams, "invalid id")
		return
	}

	// 2) 获取部署记录
	deployedApp, err := c.appMarketService.GetDeployedAppByID(uint(id))
	if err != nil {
		logger.Errorf("查询部署记录失败: %v", err)
		RespondWithError(ctx, CodeInternalError, "查询部署记录失败: "+err.Error())
		return
	}

	// 3) 删除 Kubernetes 资源
	if err := c.appMarketService.UndeployApp(deployedApp); err != nil {
		RespondWithError(ctx, CodeInternalError, "删除 Kubernetes 资源失败: "+err.Error())
		return
	}

	// 4) 删除数据库记录
	if err := c.appMarketService.DeleteDeployedApp(uint(id)); err != nil {
		RespondWithError(ctx, CodeInternalError, "删除数据库记录失败: "+err.Error())
		return
	}

	RespondWithSuccess(ctx, nil)
}
