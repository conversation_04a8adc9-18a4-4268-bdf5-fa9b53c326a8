package controllers

import (
	"blops/app/dtos"
	. "blops/app/services"
	. "blops/app/services/impl"
	. "blops/base"
	"blops/config"
	feishu2 "blops/enums/feishu"
	http2 "blops/enums/http"
	"blops/logger"
	"blops/rest/feishu"
	"blops/utils"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

type alertController struct {
	alertDefaultChat string
	alertSvc         AlertServiceInf
}

var AlertCtrl *alertController

func init() {
	AlertCtrl = &alertController{
		alertDefaultChat: config.Conf.Alert.Platform.Lark.Chat.Default,
		alertSvc:         AlertSvc,
	}
}

// AddAlert 添加告警
// @Summary      添加告警
// @Description  添加新的告警
// @Tags         告警管理
// @Accept       json
// @Produce      json
// @Param        request  body      models.AlertRequest  true  "告警请求参数"
// @Success      200      {object}  models.Response      "添加告警成功"
// @Failure      400      {object}  models.Response      "请求参数错误"
// @Failure      500      {object}  models.Response      "添加告警失败"
// @Router       /v1/alert/all [post]
// @Security     ApiKeyAuth
func (a *alertController) AddAlert(c *gin.Context) {
	//alertList := a.alertSvc.SaveAlert()
	//body := c.BindJSON()
	var alert AlertData
	err := c.ShouldBindJSON(&alert)
	if err != nil {
		c.String(http.StatusNotFound, "绑定uri失败")
		return
	}

	e := a.alertSvc.AddAlert(alert.Alerts)
	if e != nil {
		log.Fatal(e)
		return
	}

	c.JSON(http.StatusOK, ApiRes{Data: "alertList up ok"})
	//c.String(200, "any")
	return
}

// SaveAlert 保存告警
// @Summary      保存告警
// @Description  保存告警配置
// @Tags         告警管理
// @Accept       json
// @Produce      json
// @Param        request  body      models.AlertSaveRequest  true  "告警保存请求参数"
// @Success      200      {object}  models.Response          "保存告警成功"
// @Failure      400      {object}  models.Response          "请求参数错误"
// @Failure      500      {object}  models.Response          "保存告警失败"
// @Router       /v1/alert/list [post]
// @Security     ApiKeyAuth
func (a *alertController) SaveAlert(c *gin.Context) {
	var alertList AlertList
	if utils.CheckParams[AlertList](c, &alertList) != nil {
		return
	}

	hostname := alertList.Hostname
	status := alertList.Status
	level := alertList.Level
	alertName := alertList.AlertName
	alertId := alertList.AlertId
	page := alertList.Page
	cluster := alertList.Cluster
	message := alertList.Message
	pageSize := alertList.PageSize
	hours := alertList.H
	e, total, err := a.alertSvc.GetAlert(hostname, status, level, alertId, alertName, cluster, message, page, pageSize, hours)
	if err != nil {
		c.JSON(http2.ServerError().WithExtra(err.Error()))
		return
	}
	c.JSON(200, gin.H{
		"result":    "SUCCESS",
		"code":      http.StatusOK,
		"message":   "success",
		"data":      e,
		"page":      page,
		"page_size": pageSize,
		"total":     total,
	})
	return
}

// AlertHandler 告警处理
// @Summary      告警处理
// @Description  处理接收到的告警
// @Tags         告警管理
// @Accept       json
// @Produce      json
// @Param        request  body      models.AlertHandlerRequest  true  "告警处理请求参数"
// @Success      200      {object}  models.Response             "处理告警成功"
// @Failure      400      {object}  models.Response             "请求参数错误"
// @Failure      500      {object}  models.Response             "处理告警失败"
// @Router       /v1/alert/handler [post]
// @Security     ApiKeyAuth
func (a *alertController) AlertHandler(c *gin.Context) {
	var alertDTO *dtos.AlertDTO
	err := c.ShouldBindJSON(alertDTO)
	if err != nil {
		logger.Error(err)
		c.JSON(http2.BadRequest().WithExtra(err.Error()))
		card := feishu.GenCard(feishu2.Red, "告警请求参数解析失败", "发生时间："+time.Now().Format("2006-01-02 15:04:05"))
		_, _ = feishu.SendCard(a.alertDefaultChat, feishu2.CHAT, card)
		return
	}
	a.alertSvc.AlertHandler(alertDTO)
	c.JSON(http2.Success(""))
}
