package controllers

import (
	. "blops/app/services"
	. "blops/app/services/impl"
	. "blops/enums/http"

	"github.com/gin-gonic/gin"
)

type ResourceController struct {
	resourceSvc ResourceServiceInf
}

var ResourceCtrl *ResourceController

func init() {
	ResourceCtrl = &ResourceController{
		resourceSvc: ResourceSvc,
	}
}

// ListHosts 获取主机列表
// @Summary      获取主机列表
// @Description  获取所有可用的主机列表
// @Tags         资源管理
// @Accept       json
// @Produce      json
// @Param        request  body      models.HostListRequest  true  "主机列表请求参数"
// @Success      200      {object}  models.Response         "获取主机列表成功"
// @Failure      400      {object}  models.Response         "请求参数错误"
// @Failure      500      {object}  models.Response         "获取主机列表失败"
// @Router       /v1/resource/host/_list [post]
// @Security     ApiKeyAuth
func (ctrl *ResourceController) ListHosts(c *gin.Context) {
	hostList := ctrl.resourceSvc.ListHost()
	c.<PERSON>(Success(hostList))
	return
}
