package controllers

import (
	"blops/kube"
	"blops/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

type CronJobController struct{}

var CronJobCtrl = &CronJobController{}

// ListCronJobs 获取定时任务列表
// @Summary      获取定时任务列表
// @Description  获取指定集群和命名空间中的所有CronJob
// @Tags         定时任务
// @Accept       json
// @Produce      json
// @Param        request  body      models.NamespaceRequest  true  "命名空间请求参数"
// @Success      200      {object}  models.Response          "获取定时任务列表成功"
// @Failure      400      {object}  models.Response          "请求参数错误"
// @Failure      500      {object}  models.Response          "获取定时任务列表失败"
// @Router       /v1/cronjob/list [post]
// @Security     ApiKeyAuth
func (c *CronJobController) ListCronJobs(ctx *gin.Context) {
	var req struct {
		Cluster   string `json:"cluster" binding:"required"`
		Namespace string `json:"namespace" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, utils.Response{
			Code:    http.StatusBadRequest,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	cronJobs, err := kube.ListCronJobs(req.Cluster, req.Namespace)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, utils.Response{
			Code:    http.StatusInternalServerError,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	ctx.JSON(http.StatusOK, utils.Response{
		Code:    http.StatusOK,
		Message: "success",
		Result:  "SUCCESS",
		Data:    cronJobs,
	})
}

// GetCronJob 获取定时任务详情
// @Summary      获取定时任务详情
// @Description  获取指定定时任务的详细信息
// @Tags         定时任务
// @Accept       json
// @Produce      json
// @Param        request  body      models.ResourceRequest  true  "资源请求参数"
// @Success      200      {object}  models.Response         "获取定时任务详情成功"
// @Failure      400      {object}  models.Response         "请求参数错误"
// @Failure      500      {object}  models.Response         "获取定时任务详情失败"
// @Router       /v1/cronjob/get [post]
// @Security     ApiKeyAuth
func (c *CronJobController) GetCronJob(ctx *gin.Context) {
	var req struct {
		Cluster   string `json:"cluster" binding:"required"`
		Namespace string `json:"namespace" binding:"required"`
		Name      string `json:"name" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, utils.Response{
			Code:    http.StatusBadRequest,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	cronJob, err := kube.GetCronJob(req.Cluster, req.Namespace, req.Name)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, utils.Response{
			Code:    http.StatusInternalServerError,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	ctx.JSON(http.StatusOK, utils.Response{
		Code:    http.StatusOK,
		Message: "success",
		Result:  "SUCCESS",
		Data:    cronJob,
	})
}

// CreateCronJob 创建定时任务
// @Summary      创建定时任务
// @Description  创建新的定时任务
// @Tags         定时任务
// @Accept       json
// @Produce      json
// @Param        request  body      models.CreateCronJobRequest  true  "创建定时任务请求参数"
// @Success      200      {object}  models.Response              "创建定时任务成功"
// @Failure      400      {object}  models.Response              "请求参数错误"
// @Failure      500      {object}  models.Response              "创建定时任务失败"
// @Router       /v1/cronjob/create [post]
// @Security     ApiKeyAuth
func (c *CronJobController) CreateCronJob(ctx *gin.Context) {
	var req struct {
		Cluster   string `json:"cluster" binding:"required"`
		Namespace string `json:"namespace" binding:"required"`
		YamlData  string `json:"yamlData" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, utils.Response{
			Code:    http.StatusBadRequest,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	cronJob, err := kube.CreateCronJob(req.Cluster, req.Namespace, req.YamlData)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, utils.Response{
			Code:    http.StatusInternalServerError,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	ctx.JSON(http.StatusOK, utils.Response{
		Code:    http.StatusOK,
		Message: "success",
		Result:  "SUCCESS",
		Data:    cronJob,
	})
}

// UpdateCronJob 更新定时任务
// @Summary      更新定时任务
// @Description  更新现有的定时任务
// @Tags         定时任务
// @Accept       json
// @Produce      json
// @Param        request  body      models.UpdateCronJobRequest  true  "更新定时任务请求参数"
// @Success      200      {object}  models.Response              "更新定时任务成功"
// @Failure      400      {object}  models.Response              "请求参数错误"
// @Failure      500      {object}  models.Response              "更新定时任务失败"
// @Router       /v1/cronjob/update [post]
// @Security     ApiKeyAuth
func (c *CronJobController) UpdateCronJob(ctx *gin.Context) {
	var req struct {
		Cluster   string `json:"cluster" binding:"required"`
		Namespace string `json:"namespace" binding:"required"`
		Name      string `json:"name" binding:"required"`
		YamlData  string `json:"yamlData" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, utils.Response{
			Code:    http.StatusBadRequest,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	cronJob, err := kube.UpdateCronJob(req.Cluster, req.Namespace, req.Name, req.YamlData)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, utils.Response{
			Code:    http.StatusInternalServerError,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	ctx.JSON(http.StatusOK, utils.Response{
		Code:    http.StatusOK,
		Message: "success",
		Result:  "SUCCESS",
		Data:    cronJob,
	})
}

// DeleteCronJob 删除定时任务
// @Summary      删除定时任务
// @Description  删除指定的定时任务
// @Tags         定时任务
// @Accept       json
// @Produce      json
// @Param        request  body      models.ResourceRequest  true  "资源请求参数"
// @Success      200      {object}  models.Response         "删除定时任务成功"
// @Failure      400      {object}  models.Response         "请求参数错误"
// @Failure      500      {object}  models.Response         "删除定时任务失败"
// @Router       /v1/cronjob/delete [post]
// @Security     ApiKeyAuth
func (c *CronJobController) DeleteCronJob(ctx *gin.Context) {
	var req struct {
		Cluster   string `json:"cluster" binding:"required"`
		Namespace string `json:"namespace" binding:"required"`
		Name      string `json:"name" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, utils.Response{
			Code:    http.StatusBadRequest,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	err := kube.DeleteCronJob(req.Cluster, req.Namespace, req.Name)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, utils.Response{
			Code:    http.StatusInternalServerError,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	ctx.JSON(http.StatusOK, utils.Response{
		Code:    http.StatusOK,
		Message: "success",
		Result:  "SUCCESS",
	})
}

// GetCronJobJobs 获取CronJob创建的Jobs
// @Summary      获取CronJob创建的Jobs
// @Description  获取指定CronJob创建的所有Job
// @Tags         定时任务
// @Accept       json
// @Produce      json
// @Param        request  body      models.ResourceRequest  true  "资源请求参数"
// @Success      200      {object}  models.Response         "获取Job列表成功"
// @Failure      400      {object}  models.Response         "请求参数错误"
// @Failure      500      {object}  models.Response         "获取Job列表失败"
// @Router       /v1/cronjob/jobs [post]
// @Security     ApiKeyAuth
func (c *CronJobController) GetCronJobJobs(ctx *gin.Context) {
	var req struct {
		Cluster   string `json:"cluster" binding:"required"`
		Namespace string `json:"namespace" binding:"required"`
		Name      string `json:"name" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, utils.Response{
			Code:    http.StatusBadRequest,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	jobs, err := kube.GetCronJobJobs(req.Cluster, req.Namespace, req.Name)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, utils.Response{
			Code:    http.StatusInternalServerError,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	ctx.JSON(http.StatusOK, utils.Response{
		Code:    http.StatusOK,
		Message: "success",
		Result:  "SUCCESS",
		Data:    jobs,
	})
}

// GetPodLogs 获取Pod日志
// @Summary      获取Pod日志
// @Description  获取指定Pod的日志
// @Tags         Pod
// @Accept       json
// @Produce      json
// @Param        request  body      models.PodLogRequest  true  "Pod日志请求参数"
// @Success      200      {object}  models.Response       "获取Pod日志成功"
// @Failure      400      {object}  models.Response       "请求参数错误"
// @Failure      500      {object}  models.Response       "获取Pod日志失败"
// @Router       /v1/pod/logs [post]
// @Security     ApiKeyAuth
func (c *CronJobController) GetPodLogs(ctx *gin.Context) {
	var req struct {
		Cluster       string `json:"cluster" binding:"required"`
		Namespace     string `json:"namespace" binding:"required"`
		PodName       string `json:"podName" binding:"required"`
		ContainerName string `json:"containerName"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, utils.Response{
			Code:    http.StatusBadRequest,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	logs, err := kube.GetPodLogs(req.Cluster, req.Namespace, req.PodName, req.ContainerName)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, utils.Response{
			Code:    http.StatusInternalServerError,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	ctx.JSON(http.StatusOK, utils.Response{
		Code:    http.StatusOK,
		Message: "success",
		Result:  "SUCCESS",
		Data:    logs,
	})
}

// TriggerCronJob 触发定时任务执行
// @Summary      触发定时任务执行
// @Description  立即触发一个定时任务执行，不等待调度时间
// @Tags         定时任务
// @Accept       json
// @Produce      json
// @Param        request  body      models.ResourceRequest  true  "资源请求参数"
// @Success      200      {object}  models.Response         "触发执行成功"
// @Failure      400      {object}  models.Response         "请求参数错误"
// @Failure      500      {object}  models.Response         "触发执行失败"
// @Router       /v1/cronjob/trigger [post]
// @Security     ApiKeyAuth
func (c *CronJobController) TriggerCronJob(ctx *gin.Context) {
	var req struct {
		Cluster   string `json:"cluster" binding:"required"`
		Namespace string `json:"namespace" binding:"required"`
		Name      string `json:"name" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, utils.Response{
			Code:    http.StatusBadRequest,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	job, err := kube.TriggerCronJob(req.Cluster, req.Namespace, req.Name)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, utils.Response{
			Code:    http.StatusInternalServerError,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	ctx.JSON(http.StatusOK, utils.Response{
		Code:    http.StatusOK,
		Message: "success",
		Result:  "SUCCESS",
		Data:    job,
	})
}

// DeleteCronJobJobs 删除与CronJob相关的所有Job
// @Summary      删除与CronJob相关的所有Job
// @Description  删除由指定CronJob创建的所有Job
// @Tags         定时任务
// @Accept       json
// @Produce      json
// @Param        request  body      models.DeleteCronJobJobsRequest  true  "删除CronJob相关Job请求参数"
// @Success      200      {object}  models.Response                 "删除CronJob相关Job成功"
// @Failure      400      {object}  models.Response                 "请求参数错误"
// @Failure      500      {object}  models.Response                 "删除CronJob相关Job失败"
// @Router       /v1/cronjob/delete-jobs [post]
// @Security     ApiKeyAuth
func (c *CronJobController) DeleteCronJobJobs(ctx *gin.Context) {
	var req struct {
		Cluster     string `json:"cluster" binding:"required"`
		Namespace   string `json:"namespace" binding:"required"`
		CronjobName string `json:"cronjobName" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, utils.Response{
			Code:    http.StatusBadRequest,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	err := kube.DeleteCronJobJobs(req.Cluster, req.Namespace, req.CronjobName)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, utils.Response{
			Code:    http.StatusInternalServerError,
			Message: err.Error(),
			Result:  "FAILED",
		})
		return
	}

	ctx.JSON(http.StatusOK, utils.Response{
		Code:    http.StatusOK,
		Message: "success",
		Result:  "SUCCESS",
	})
}
