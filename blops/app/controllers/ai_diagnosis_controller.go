package controllers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"
)

// AIDiagnosisRequest 定义请求结构
type AIDiagnosisRequest struct {
	Cluster   string        `json:"cluster"`
	Namespace string        `json:"namespace"`
	Events    []interface{} `json:"events"`
}

// GeminiRequest 定义Gemini API请求结构
type GeminiRequest struct {
	Contents []GeminiContent `json:"contents"`
}

// GeminiContent 定义Gemini内容结构
type GeminiContent struct {
	Parts []GeminiPart `json:"parts"`
}

// GeminiPart 定义Gemini部分结构
type GeminiPart struct {
	Text string `json:"text"`
}

// GeminiResponse 定义Gemini API响应结构
type GeminiResponse struct {
	Candidates []struct {
		Content struct {
			Parts []struct {
				Text string `json:"text"`
			} `json:"parts"`
		} `json:"content"`
	} `json:"candidates"`
	UsageMetadata struct {
		PromptTokenCount     int `json:"promptTokenCount"`
		CandidatesTokenCount int `json:"candidatesTokenCount"`
		TotalTokenCount      int `json:"totalTokenCount"`
	} `json:"usageMetadata,omitempty"`
}

// AIAnalyzeRequest 定义通用AI分析请求结构
type AIAnalyzeRequest struct {
	Content string `json:"content"` // 需要分析的内容
	Prompt  string `json:"prompt"`  // 自定义提示词，可选
	Type    string `json:"type"`    // 内容类型，可选，用于预设提示词
	Model   string `json:"model"`   // 指定使用的模型，可选
}

// AIChatRequest 定义AI聊天请求结构
type AIChatRequest struct {
	Message   string `json:"message"`   // 用户消息
	SessionID string `json:"sessionId"` // 会话ID，可选
}

// AIChatResponse 定义AI聊天响应结构
type AIChatResponse struct {
	Reply     string `json:"reply"`     // AI回复
	SessionID string `json:"sessionId"` // 会话ID
	Timestamp int64  `json:"timestamp"` // 时间戳
}

// ChatHistory 定义聊天历史结构
type ChatHistory struct {
	SessionID string        `json:"sessionId"`
	Messages  []ChatMessage `json:"messages"`
	CreatedAt int64         `json:"createdAt"`
	UpdatedAt int64         `json:"updatedAt"`
}

// ChatMessage 定义聊天消息结构
type ChatMessage struct {
	Role      string `json:"role"`      // "user" 或 "assistant"
	Content   string `json:"content"`   // 消息内容
	Timestamp int64  `json:"timestamp"` // 时间戳
}

// QuickTemplate 定义快速问题模板结构
type QuickTemplate struct {
	ID          string `json:"id"`
	Title       string `json:"title"`
	Description string `json:"description"`
	Content     string `json:"content"`
	Category    string `json:"category"`
}

// 调试模式控制
var debugLogEnabled = true

// 内存中存储聊天历史（实际应用中应使用数据库）
var chatHistories = make(map[string]*ChatHistory)
var chatMutex sync.RWMutex

// 添加一个集群配置缓存
var clusterConfigs = map[string]string{
	"ali-test":   "/Users/<USER>/kube/ali-test",
	"ali-prod":   "/Users/<USER>/kube/ali-prod",
	"ali-guotai": "/Users/<USER>/kube/ali-guotai",
	"hwyx-prod":  "/Users/<USER>/kube/hwyx-prod",
	"ali-v3":     "/Users/<USER>/kube/ali-v3",
}

// getKubernetesClient 获取Kubernetes客户端
func getKubernetesClient(clusterID string) (*kubernetes.Clientset, error) {
	// 获取集群配置文件路径
	configPath, ok := clusterConfigs[clusterID]
	if !ok {
		return nil, fmt.Errorf("未找到集群 %s 的配置", clusterID)
	}

	// 加载kubeconfig
	config, err := clientcmd.BuildConfigFromFlags("", configPath)
	if err != nil {
		return nil, fmt.Errorf("加载kubeconfig失败: %v", err)
	}

	// 创建clientset
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		return nil, fmt.Errorf("创建Kubernetes客户端失败: %v", err)
	}

	return clientset, nil
}

// AIDiagnosis 处理AI诊断请求
// @Summary      AI诊断分析
// @Description  分析Kubernetes集群中的事件并提供诊断结果
// @Tags         AI诊断
// @Accept       json
// @Produce      json
// @Param        model      query     string                    false  "AI模型 (gemini 或 deepseek)"
// @Param        request    body      models.AIDiagnosisRequest true   "诊断请求参数"
// @Success      200        {object}  models.Response           "诊断成功"
// @Failure      400        {object}  models.Response           "无效的请求参数"
// @Failure      500        {object}  models.Response           "AI分析失败"
// @Router       /ai/diagnosis [post]
// @Security     ApiKeyAuth
func AIDiagnosis(c *gin.Context) {
	var req AIDiagnosisRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的请求参数",
			"data":    nil,
		})
		return
	}

	// 移除模型参数获取，统一使用 gemini
	// 格式化事件数据
	var eventsText strings.Builder
	eventsText.WriteString("以下是Kubernetes集群中的事件日志：\n\n")

	for i, event := range req.Events {
		eventMap, ok := event.(map[string]interface{})
		if !ok {
			continue
		}

		// 提取事件信息
		var reason, eventType, message, objectKind, objectName, timestamp string

		if r, ok := eventMap["reason"].(string); ok {
			reason = r
		}
		if t, ok := eventMap["type"].(string); ok {
			eventType = t
		}
		if m, ok := eventMap["message"].(string); ok {
			message = m
		}

		if involvedObject, ok := eventMap["involvedObject"].(map[string]interface{}); ok {
			if k, ok := involvedObject["kind"].(string); ok {
				objectKind = k
			}
			if n, ok := involvedObject["name"].(string); ok {
				objectName = n
			}
		}

		if ts, ok := eventMap["lastTimestamp"].(string); ok {
			timestamp = ts
		}

		eventsText.WriteString(fmt.Sprintf("事件 %d:\n", i+1))
		eventsText.WriteString(fmt.Sprintf("- 类型: %s\n", eventType))
		eventsText.WriteString(fmt.Sprintf("- 原因: %s\n", reason))
		eventsText.WriteString(fmt.Sprintf("- 对象: %s/%s\n", objectKind, objectName))
		eventsText.WriteString(fmt.Sprintf("- 时间: %s\n", timestamp))
		eventsText.WriteString(fmt.Sprintf("- 消息: %s\n\n", message))
	}

	// 调用AI模型API，不再传递model参数
	analysis, err := callAIModel(eventsText.String(), "")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "AI分析失败: " + err.Error(),
			"data":    nil,
		})
		return
	}

	// 在返回结果前调用 markdownToHTML 函数
	analysis = markdownToHTML(analysis)

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "AI诊断成功",
		"data":    analysis,
	})
}

// callAIModel 根据指定的模型调用相应的API
func callAIModel(text string, model string) (string, error) {
	// 统一使用 Gemini 模型
	// Gemini API配置
	apiKey := "AIzaSyByTYU8l_0Qbqq6AiCQpZOgu-rJ3PfgfLM" // 注意：实际应用中应从配置文件或环境变量获取
	apiURL := "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent"

	// 添加系统提示词
	systemPrompt := "首先按时间顺序整理好这些日志条目概要，然后深入分析一下这些日志信息，使用中文回答。"
	text = systemPrompt + "\n\n" + text

	if debugLogEnabled {
		log.Printf("[DEBUG] 准备调用AI模型API，提示词长度: %d", len(text))
	}

	// 构建请求
	geminiReq := GeminiRequest{
		Contents: []GeminiContent{
			{
				Parts: []GeminiPart{
					{
						Text: text,
					},
				},
			},
		},
	}

	reqBody, err := json.Marshal(geminiReq)
	if err != nil {
		return "", fmt.Errorf("JSON编码失败: %v", err)
	}

	// 发送请求
	url := fmt.Sprintf("%s?key=%s", apiURL, apiKey)

	if debugLogEnabled {
		log.Printf("[DEBUG] 发送请求到: %s", apiURL)
	}

	resp, err := http.Post(url, "application/json", bytes.NewBuffer(reqBody))
	if err != nil {
		return "", fmt.Errorf("API请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	if debugLogEnabled {
		log.Printf("[DEBUG] 收到API响应, 状态码: %d, 响应长度: %d", resp.StatusCode, len(respBody))
	}

	// 解析响应
	var geminiResp GeminiResponse
	if err := json.Unmarshal(respBody, &geminiResp); err != nil {
		if debugLogEnabled {
			log.Printf("[ERROR] JSON解码失败, 响应内容: %s", string(respBody))
		}
		return "", fmt.Errorf("JSON解码失败: %v", err)
	}

	// 记录token使用情况
	if debugLogEnabled && geminiResp.UsageMetadata.TotalTokenCount > 0 {
		log.Printf("[DEBUG] Token使用: 提示词=%d, 响应=%d, 总计=%d",
			geminiResp.UsageMetadata.PromptTokenCount,
			geminiResp.UsageMetadata.CandidatesTokenCount,
			geminiResp.UsageMetadata.TotalTokenCount)
	}

	// 提取分析结果
	if len(geminiResp.Candidates) > 0 && len(geminiResp.Candidates[0].Content.Parts) > 0 {
		result := geminiResp.Candidates[0].Content.Parts[0].Text
		if debugLogEnabled {
			log.Printf("[DEBUG] AI分析结果长度: %d", len(result))
		}
		return result, nil
	}

	if debugLogEnabled {
		log.Printf("[ERROR] 未获取到有效结果, 响应内容: %s", string(respBody))
	}
	return "", fmt.Errorf("未获取到有效的分析结果")
}

// GetNamespaceEvents 获取命名空间事件
// @Summary      获取命名空间事件
// @Description  获取指定集群和命名空间中的所有事件
// @Tags         集群管理
// @Accept       json
// @Produce      json
// @Param        cluster_id  path      string          true  "集群ID"
// @Param        namespace   path      string          true  "命名空间"
// @Success      200         {object}  models.Response  "获取事件列表成功"
// @Failure      400         {object}  models.Response  "参数错误"
// @Failure      500         {object}  models.Response  "获取事件列表失败"
// @Router       /cluster/{cluster_id}/namespace/{namespace}/events [get]
// @Security     ApiKeyAuth
func GetNamespaceEvents(c *gin.Context) {
	clusterID := c.Param("cluster_id")
	namespace := c.Param("namespace")

	if clusterID == "" || namespace == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "集群ID和命名空间不能为空",
			"data":    nil,
		})
		return
	}

	// 这里应该调用Kubernetes API获取事件列表
	// 由于我们不知道现有的Kubernetes客户端实现，这里使用模拟数据
	events, err := getKubernetesEvents(clusterID, namespace)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取事件列表失败: " + err.Error(),
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取事件列表成功",
		"data":    events,
	})
}

// getKubernetesEvents 获取Kubernetes事件列表
func getKubernetesEvents(clusterID, namespace string) ([]map[string]interface{}, error) {
	if debugLogEnabled {
		log.Printf("[DEBUG] 获取集群 %s 命名空间 %s 的事件", clusterID, namespace)
	}

	// 获取Kubernetes客户端
	clientset, err := getKubernetesClient(clusterID)
	if err != nil {
		return nil, err
	}

	// 获取事件列表
	events, err := clientset.CoreV1().Events(namespace).List(context.Background(), metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("获取事件列表失败: %v", err)
	}

	// 转换为前端需要的格式
	var result []map[string]interface{}
	for _, event := range events.Items {
		eventMap := map[string]interface{}{
			"metadata": map[string]interface{}{
				"name":      event.Name,
				"namespace": event.Namespace,
				"uid":       string(event.UID),
			},
			"type":    event.Type,
			"reason":  event.Reason,
			"message": event.Message,
			"count":   event.Count,
			"involvedObject": map[string]interface{}{
				"kind":      event.InvolvedObject.Kind,
				"name":      event.InvolvedObject.Name,
				"namespace": event.InvolvedObject.Namespace,
			},
			"source": map[string]interface{}{
				"component": event.Source.Component,
				"host":      event.Source.Host,
			},
			"firstTimestamp": event.FirstTimestamp.Time.Format(time.RFC3339),
			"lastTimestamp":  event.LastTimestamp.Time.Format(time.RFC3339),
		}
		result = append(result, eventMap)
	}

	return result, nil
}

// GetClusters 获取集群列表
// @Summary      获取集群列表
// @Description  获取所有可用的Kubernetes集群列表
// @Tags         集群管理
// @Accept       json
// @Produce      json
// @Success      200  {object}  models.Response  "获取集群列表成功"
// @Router       /cluster/list [get]
// @Security     ApiKeyAuth
func GetClusters(c *gin.Context) {
	// 写死的集群列表
	clusters := []map[string]interface{}{
		{
			"id":   "ali-test",
			"name": "ali-test",
		},
		{
			"id":   "ali-prod",
			"name": "ali-prod",
		},
		{
			"id":   "ali-guotai",
			"name": "ali-guotai",
		},
		{
			"id":   "hwyx-prod",
			"name": "hwyx-prod",
		},
		{
			"id":   "ali-v3",
			"name": "ali-v3",
		},
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取集群列表成功",
		"data":    clusters,
	})
}

// GetNamespaces 获取命名空间列表
// @Summary      获取命名空间列表
// @Description  获取指定集群中的所有命名空间
// @Tags         集群管理
// @Accept       json
// @Produce      json
// @Param        cluster_id  path      string          true  "集群ID"
// @Success      200         {object}  models.Response  "获取命名空间列表成功"
// @Failure      400         {object}  models.Response  "集群ID不能为空"
// @Failure      500         {object}  models.Response  "获取命名空间列表失败"
// @Router       /cluster/{cluster_id}/namespaces [get]
// @Security     ApiKeyAuth
func GetNamespaces(c *gin.Context) {
	clusterID := c.Param("cluster_id")

	if clusterID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "集群ID不能为空",
			"data":    nil,
		})
		return
	}

	// 获取Kubernetes客户端
	clientset, err := getKubernetesClient(clusterID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取Kubernetes客户端失败: " + err.Error(),
			"data":    nil,
		})
		return
	}

	// 获取命名空间列表
	namespaceList, err := clientset.CoreV1().Namespaces().List(context.Background(), metav1.ListOptions{})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取命名空间列表失败: " + err.Error(),
			"data":    nil,
		})
		return
	}

	// 提取命名空间名称
	var namespaces []string
	for _, ns := range namespaceList.Items {
		namespaces = append(namespaces, ns.Name)
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取命名空间列表成功",
		"data":    namespaces,
	})
}

// markdownToHTML 将 Markdown 转换为 HTML
func markdownToHTML(markdown string) string {
	// 简单的 Markdown 转 HTML 实现
	// 处理标题
	re := regexp.MustCompile(`(?m)^(#{1,6})\s+(.+)$`)
	markdown = re.ReplaceAllStringFunc(markdown, func(match string) string {
		submatches := re.FindStringSubmatch(match)
		level := len(submatches[1])
		return fmt.Sprintf("<h%d>%s</h%d>", level, submatches[2], level)
	})

	// 处理粗体
	re = regexp.MustCompile(`\*\*(.+?)\*\*`)
	markdown = re.ReplaceAllString(markdown, "<strong>$1</strong>")

	// 处理斜体
	re = regexp.MustCompile(`\*(.+?)\*`)
	markdown = re.ReplaceAllString(markdown, "<em>$1</em>")

	// 处理代码块
	re = regexp.MustCompile("```([\\s\\S]+?)```")
	markdown = re.ReplaceAllString(markdown, "<pre><code>$1</code></pre>")

	// 处理行内代码
	re = regexp.MustCompile("`([^`]+)`")
	markdown = re.ReplaceAllString(markdown, "<code>$1</code>")

	// 处理无序列表
	re = regexp.MustCompile(`(?m)^[\*\-]\s+(.+)$`)
	markdown = re.ReplaceAllString(markdown, "<li>$1</li>")
	// 将连续的 <li> 包装在 <ul> 中
	markdown = strings.ReplaceAll(markdown, "</li>\n<li>", "</li><li>")
	re = regexp.MustCompile(`(?s)<li>(.+?)</li>`)
	markdown = re.ReplaceAllString(markdown, "<ul><li>$1</li></ul>")

	// 处理段落
	lines := strings.Split(markdown, "\n")
	var result strings.Builder
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" && !strings.HasPrefix(line, "<") {
			result.WriteString("<p>" + line + "</p>\n")
		} else {
			result.WriteString(line + "\n")
		}
	}

	return result.String()
}

// AIAnalyze 处理通用AI分析请求
// @Summary      通用AI分析
// @Description  使用指定AI模型分析任何内容
// @Tags         AI分析
// @Accept       json
// @Produce      json
// @Param        model   query     string             false "AI模型 (gemini 或 deepseek)"
// @Param        request body      AIAnalyzeRequest   true  "分析请求参数"
// @Success      200    {object}   models.Response    "分析成功"
// @Failure      400    {object}   models.Response    "无效的请求参数"
// @Failure      500    {object}   models.Response    "AI分析失败"
// @Router       /ai/analyze [post]
func AIAnalyze(c *gin.Context) {
	var req AIAnalyzeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的请求参数",
			"data":    nil,
		})
		return
	}

	if debugLogEnabled {
		log.Printf("[DEBUG] 接收AI分析请求, 内容类型: %s, 内容长度: %d", req.Type, len(req.Content))
	}

	// 移除模型参数获取，统一使用 gemini
	// 构建提示词
	prompt := req.Prompt
	if prompt == "" {
		// 根据内容类型提供默认提示词
		switch req.Type {
		case "thread_dump":
			prompt = "你是java专家，jvm性能优化专家，请分析这个Java线程转储信息，识别潜在的死锁、高CPU使用率线程、阻塞线程等问题，并提供可能的解决方案，使用中文回答。"
		case "log":
			prompt = "你是java专家，jvm性能优化专家，请分析这段日志内容，识别异常、错误和警告信息，解释它们的原因，并提供可能的解决方案，使用中文回答。"
		case "top":
			prompt = "你是linux专家，请分析这个top命令输出，识别CPU、内存使用率高的进程，并解释系统资源使用情况，使用中文回答。"
		default:
			prompt = "你是云原生专家，请分析以下内容并提供见解，使用中文回答。"
		}

		if debugLogEnabled {
			log.Printf("[DEBUG] 使用内容类型 %s 的默认提示词", req.Type)
		}
	}

	// 准备完整的分析文本
	analysisContent := fmt.Sprintf("%s\n\n%s", prompt, req.Content)

	// 调用AI模型API，不再传递model参数
	analysis, err := callAIModel(analysisContent, "")
	if err != nil {
		if debugLogEnabled {
			log.Printf("[ERROR] AI分析失败: %v", err)
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "AI分析失败: " + err.Error(),
			"data":    nil,
		})
		return
	}

	// 在返回结果前调用 markdownToHTML 函数
	analysis = markdownToHTML(analysis)

	if debugLogEnabled {
		log.Printf("[DEBUG] AI分析成功完成，响应HTML长度: %d", len(analysis))
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "AI分析成功",
		"data":    analysis,
	})
}

// AIChat 处理AI聊天请求
// @Summary      AI聊天问答
// @Description  与AI助手进行对话，支持上下文记忆
// @Tags         AI助手
// @Accept       json
// @Produce      json
// @Param        request body      AIChatRequest true "聊天请求参数"
// @Success      200    {object}   models.Response{data=AIChatResponse} "聊天成功"
// @Failure      400    {object}   models.Response "无效的请求参数"
// @Failure      500    {object}   models.Response "AI聊天失败"
// @Router       /ai/chat [post]
// @Security     ApiKeyAuth
func AIChat(c *gin.Context) {
	var req AIChatRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的请求参数",
			"data":    nil,
		})
		return
	}

	if req.Message == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "消息内容不能为空",
			"data":    nil,
		})
		return
	}

	// 生成会话ID（如果没有提供）
	sessionID := req.SessionID
	if sessionID == "" {
		sessionID = fmt.Sprintf("session_%d", time.Now().UnixNano())
	}

	if debugLogEnabled {
		log.Printf("[DEBUG] 接收AI聊天请求, 会话ID: %s, 消息长度: %d", sessionID, len(req.Message))
	}

	// 获取或创建聊天历史
	chatMutex.Lock()
	history, exists := chatHistories[sessionID]
	if !exists {
		history = &ChatHistory{
			SessionID: sessionID,
			Messages:  []ChatMessage{},
			CreatedAt: time.Now().Unix(),
			UpdatedAt: time.Now().Unix(),
		}
		chatHistories[sessionID] = history
	}
	chatMutex.Unlock()

	// 添加用户消息到历史
	userMessage := ChatMessage{
		Role:      "user",
		Content:   req.Message,
		Timestamp: time.Now().Unix(),
	}

	chatMutex.Lock()
	history.Messages = append(history.Messages, userMessage)
	history.UpdatedAt = time.Now().Unix()
	chatMutex.Unlock()

	// 构建上下文提示词
	contextPrompt := buildChatContext(history.Messages)

	// 调用AI模型
	reply, err := callAIModel(contextPrompt, "")
	if err != nil {
		if debugLogEnabled {
			log.Printf("[ERROR] AI聊天失败: %v", err)
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "AI聊天失败: " + err.Error(),
			"data":    nil,
		})
		return
	}

	// 添加AI回复到历史
	assistantMessage := ChatMessage{
		Role:      "assistant",
		Content:   reply,
		Timestamp: time.Now().Unix(),
	}

	chatMutex.Lock()
	history.Messages = append(history.Messages, assistantMessage)
	history.UpdatedAt = time.Now().Unix()

	// 限制历史消息数量（保留最近20条消息）
	if len(history.Messages) > 20 {
		history.Messages = history.Messages[len(history.Messages)-20:]
	}
	chatMutex.Unlock()

	// 转换Markdown为HTML
	reply = markdownToHTML(reply)

	response := AIChatResponse{
		Reply:     reply,
		SessionID: sessionID,
		Timestamp: time.Now().Unix(),
	}

	if debugLogEnabled {
		log.Printf("[DEBUG] AI聊天成功完成，会话ID: %s, 回复长度: %d", sessionID, len(reply))
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "AI聊天成功",
		"data":    response,
	})
}

// buildChatContext 构建聊天上下文提示词
func buildChatContext(messages []ChatMessage) string {
	var contextBuilder strings.Builder

	// 系统提示词
	contextBuilder.WriteString("你是Blops平台的AI助手，专门帮助用户解决Kubernetes集群管理和运维相关问题。")
	contextBuilder.WriteString("你具备以下专业知识：\n")
	contextBuilder.WriteString("1. Kubernetes集群管理和故障诊断\n")
	contextBuilder.WriteString("2. 容器化应用部署和运维\n")
	contextBuilder.WriteString("3. 云原生技术栈和最佳实践\n")
	contextBuilder.WriteString("4. Blops平台功能使用指导\n")
	contextBuilder.WriteString("5. 监控告警和性能优化\n\n")
	contextBuilder.WriteString("请用中文回答，提供准确、实用的建议。如果涉及具体操作，请给出详细步骤。\n\n")

	// 添加历史对话
	contextBuilder.WriteString("对话历史：\n")
	for _, msg := range messages {
		if msg.Role == "user" {
			contextBuilder.WriteString(fmt.Sprintf("用户: %s\n", msg.Content))
		} else {
			contextBuilder.WriteString(fmt.Sprintf("助手: %s\n", msg.Content))
		}
	}

	return contextBuilder.String()
}

// GetChatHistory 获取聊天历史
// @Summary      获取聊天历史
// @Description  获取指定会话的聊天历史记录
// @Tags         AI助手
// @Accept       json
// @Produce      json
// @Param        sessionId query string false "会话ID"
// @Success      200 {object} models.Response{data=ChatHistory} "获取成功"
// @Router       /ai/chat/history [get]
// @Security     ApiKeyAuth
func GetChatHistory(c *gin.Context) {
	sessionID := c.Query("sessionId")

	if sessionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "会话ID不能为空",
			"data":    nil,
		})
		return
	}

	chatMutex.RLock()
	history, exists := chatHistories[sessionID]
	chatMutex.RUnlock()

	if !exists {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "会话不存在",
			"data":    nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取聊天历史成功",
		"data":    history,
	})
}

// ClearChatHistory 清空聊天历史
// @Summary      清空聊天历史
// @Description  清空指定会话或所有会话的聊天历史记录
// @Tags         AI助手
// @Accept       json
// @Produce      json
// @Param        sessionId query string false "会话ID，不提供则清空所有会话"
// @Success      200 {object} models.Response "清空成功"
// @Router       /ai/chat/history [delete]
// @Security     ApiKeyAuth
func ClearChatHistory(c *gin.Context) {
	sessionID := c.Query("sessionId")

	chatMutex.Lock()
	defer chatMutex.Unlock()

	if sessionID == "" {
		// 清空所有会话
		chatHistories = make(map[string]*ChatHistory)
		c.JSON(http.StatusOK, gin.H{
			"code":    200,
			"message": "已清空所有聊天历史",
			"data":    nil,
		})
	} else {
		// 清空指定会话
		if _, exists := chatHistories[sessionID]; exists {
			delete(chatHistories, sessionID)
			c.JSON(http.StatusOK, gin.H{
				"code":    200,
				"message": "已清空指定会话的聊天历史",
				"data":    nil,
			})
		} else {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "会话不存在",
				"data":    nil,
			})
		}
	}
}

// GetQuickTemplates 获取快速问题模板
// @Summary      获取快速问题模板
// @Description  获取预定义的快速问题模板列表
// @Tags         AI助手
// @Accept       json
// @Produce      json
// @Success      200 {object} models.Response{data=[]QuickTemplate} "获取成功"
// @Router       /ai/templates [get]
// @Security     ApiKeyAuth
func GetQuickTemplates(c *gin.Context) {
	templates := []QuickTemplate{
		{
			ID:          "k8s-pod-troubleshoot",
			Title:       "Pod故障排查",
			Description: "帮助排查Pod启动失败、崩溃等问题",
			Content:     "我的Pod出现了问题，状态是{状态}，错误信息是{错误信息}，请帮我分析原因并提供解决方案。",
			Category:    "故障排查",
		},
		{
			ID:          "k8s-resource-optimization",
			Title:       "资源优化建议",
			Description: "获取集群资源使用优化建议",
			Content:     "请分析我的集群资源使用情况，CPU使用率{CPU使用率}%，内存使用率{内存使用率}%，请提供优化建议。",
			Category:    "性能优化",
		},
		{
			ID:          "k8s-deployment-guide",
			Title:       "应用部署指导",
			Description: "获取应用部署的最佳实践指导",
			Content:     "我想部署一个{应用类型}应用到Kubernetes，请提供详细的部署步骤和最佳实践建议。",
			Category:    "部署指导",
		},
		{
			ID:          "k8s-monitoring-setup",
			Title:       "监控告警配置",
			Description: "配置集群监控和告警规则",
			Content:     "请帮我配置{监控对象}的监控告警，我希望在{触发条件}时收到通知。",
			Category:    "监控告警",
		},
		{
			ID:          "k8s-security-check",
			Title:       "安全检查建议",
			Description: "获取集群安全配置检查建议",
			Content:     "请帮我检查Kubernetes集群的安全配置，包括RBAC、网络策略、镜像安全等方面。",
			Category:    "安全检查",
		},
		{
			ID:          "blops-platform-usage",
			Title:       "Blops平台使用",
			Description: "了解Blops平台功能使用方法",
			Content:     "我想了解Blops平台的{功能模块}功能，请介绍使用方法和注意事项。",
			Category:    "平台使用",
		},
		{
			ID:          "k8s-network-troubleshoot",
			Title:       "网络连接问题",
			Description: "排查Pod间网络连接问题",
			Content:     "我的应用无法连接到{目标服务}，网络连接出现问题，请帮我排查网络配置。",
			Category:    "故障排查",
		},
		{
			ID:          "k8s-storage-issue",
			Title:       "存储卷问题",
			Description: "解决PV/PVC存储相关问题",
			Content:     "我的Pod挂载存储卷失败，错误信息是{错误信息}，请帮我解决存储问题。",
			Category:    "故障排查",
		},
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取快速模板成功",
		"data":    templates,
	})
}
