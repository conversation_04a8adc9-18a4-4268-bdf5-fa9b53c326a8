package app

import (
	"blops/app/job"
	"blops/config"
	"blops/logger"
	"blops/router"
	"blops/utils"
	"github.com/gin-gonic/gin"
	"log"
)

func MiddleWare() gin.HandlerFunc {
	return func(c *gin.Context) {
		ua := c.Request.Header.Get("X-AUTH")
		if ua == "" {
			logger.Info("用户未登录")
		} else {
			u := utils.ParseToken(ua)
			logger.Infof(">>>>>> 解析X-AUTH用户名：%s", u.Username)
			// 设置变量到Context的key中，可以通过Get()取
			c.Set("users", u.Username)
		}
	}
}

func Run() {
	go job.Start()
	r := gin.Default()
	r.Use(MiddleWare())
	router.Generate(r)
	err := r.Run(":" + config.Conf.SERVER.Port)
	if err != nil {
		log.Fatal(err)
		return
	}
}
