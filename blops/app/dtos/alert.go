package dtos

import (
	"blops/enums/feishu"
	"time"
)

type Alert struct {
	Status string `json:"status,omitempty"`
	Labels struct {
		Alertname           string `json:"alertname,omitempty"`
		Appid               string `json:"appid,omitempty"`
		Cloud               string `json:"cloud,omitempty"`
		HttpRoute           string `json:"http_route,omitempty"`
		KubernetesNamespace string `json:"kubernetes_namespace,omitempty"`
		Level               string `json:"level,omitempty"`
		Prometheus          string `json:"prometheus,omitempty"`
		Receiver            string `json:"receiver,omitempty"`
		Severity            string `json:"severity,omitempty"`
		User                string `json:"user,omitempty"`
		Instance            string `json:"instance,omitempty"`
		Hostname            string `json:"hostname,omitempty"`
		Webhook             string `json:"webhook,omitempty"`
		ENV                 string `json:"env,omitempty"`
	} `json:"labels,omitempty"`
	Annotations struct {
		Message string `json:"message,omitempty"`
	} `json:"annotations,omitempty"`
	StartsAt     *time.Time `json:"startsAt,omitempty"`
	EndsAt       *time.Time `json:"endsAt,omitempty"`
	GeneratorURL string     `json:"generatorURL,omitempty"`
	Fingerprint  string     `json:"fingerprint,omitempty"`
}

type AlertDTO struct {
	Receiver    string  `json:"receiver,omitempty"`
	Status      string  `json:"status,omitempty"`
	Alerts      []Alert `json:"alerts,omitempty"`
	GroupLabels struct {
		Alertname string `json:"alertname,omitempty"`
		Appid     string `json:"appid,omitempty"`
		Level     string `json:"level,omitempty"`
	} `json:"groupLabels,omitempty"`
	CommonLabels struct {
		Alertname           string `json:"alertname,omitempty"`
		Appid               string `json:"appid,omitempty"`
		Cloud               string `json:"cloud,omitempty"`
		KubernetesNamespace string `json:"kubernetes_namespace,omitempty"`
		Level               string `json:"level,omitempty"`
		Prometheus          string `json:"prometheus,omitempty"`
		Receiver            string `json:"receiver,omitempty"`
		Severity            string `json:"severity,omitempty"`
		User                string `json:"user,omitempty"`
		Webhook             string `json:"webhook,omitempty"`
	} `json:"commonLabels,omitempty"`
	CommonAnnotations struct {
	} `json:"commonAnnotations,omitempty"`
	ExternalURL     string `json:"externalURL,omitempty"`
	Version         string `json:"version,omitempty"`
	GroupKey        string `json:"groupKey,omitempty"`
	TruncatedAlerts int    `json:"truncatedAlerts,omitempty"`
}

type AlertMsg struct {
	AlertName   string `json:"alert_name,omitempty"`
	Cluster     string `json:"cluster,omitempty"`
	Receiver    string `json:"receiver,omitempty"`
	Level       string `json:"level,omitempty"`
	Instance    string `json:"instance,omitempty"`
	Hostname    string `json:"hostname,omitempty"`
	Message     string `json:"message,omitempty"`
	StartsAt    string `json:"startsAt,omitempty"`
	EndsAt      string `json:"endsAt,omitempty"`
	Fingerprint string `json:"fingerprint,omitempty"`
	// 值班人员openid
	OnDutyUserId        string                   `json:"on_duty_user_id,omitempty"`
	Webhook             string                   `json:"webhook,omitempty"`
	Title               string                   `json:"title,omitempty"`
	MsgType             feishu.CardTitleTemplate `json:"msg_type,omitempty"`
	AppID               string                   `json:"appid,omitempty"`
	KubernetesNamespace string                   `json:"kubernetes_namespace,omitempty"`
	Env                 string                   `json:"env,omitempty"`
}
