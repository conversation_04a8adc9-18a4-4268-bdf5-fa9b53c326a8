package services

import (
	"blops/app/co"
	"blops/app/models/po"
	"blops/app/vo"
)

type RuleCO struct {
	Cluster    string     `form:"cluster" json:"cluster" uri:"cluster"`
	TemplateId int64      `json:"template_id" gorm:"column:template_id"`
	Types      string     `form:"types" json:"types" uri:"types"`
	AlertName  string     `json:"alert_name"`
	Message    string     `json:"message"`
	Expr       string     `json:"expr"`
	PerchList  []po.Perch `json:"perchList"`
	Level      string     `json:"level"`
	Period     string     `json:"period"`
	Receiver   string     `json:"receiver"`
	Webhook    string     `form:"webhook" json:"webhook" uri:"webhook"`
	Labels     string     `json:"labels"`
}

type RuleGet struct {
	Types   string `form:"types" json:"types" uri:"types"`
	Cluster string `form:"cluster" json:"cluster" uri:"cluster"`
}

type RuleServiceInf interface {
	GetRule(keys string, cls string) ([]*vo.<PERSON>, error)
	RuleHandler(a *RuleCO, opt string) error
}

type TemplateServiceInf interface {
	PullTemplate(alertCode string) []*vo.TemplateVO
	AddTemplate(a *po.TemplatePO) error
	GetById(id int64) (*po.TemplatePO, error)
	PerchHandler(perchList []po.Perch) (string, error)
	UpdateTemplate(templateCO *co.TemplateCO) error
}

type TreeServiceInf interface {
	GetTree() []*po.TreeD
}
