package services

import (
	"blops/app/models/po"
)

// MiddlewareServiceInf 中间件服务接口
type MiddlewareServiceInf interface {
	// 环境相关接口
	GetEnv(id int64) (*po.MiddlewareEnv, error)
	ListEnvs() ([]*po.MiddlewareEnv, error)
	CreateEnv(env *po.MiddlewareEnv) error
	UpdateEnv(env *po.MiddlewareEnv) error
	DeleteEnv(id int64) error

	// 链接相关接口
	GetLink(id int64) (*po.MiddlewareLink, error)
	ListLinks() ([]*po.MiddlewareLink, error)
	ListLinksByType(linkType string) ([]*po.MiddlewareLink, error)
	ListLinksByEnv(envID int64) ([]*po.MiddlewareLink, error)
	ListLinksByTypeAndEnv(linkType string, envID int64) ([]*po.MiddlewareLink, error)
	ListLinksWithEnvInfo() ([]*po.MiddlewareLink, error)
	ListLinksByTypeWithEnvInfo(linkType string) ([]*po.MiddlewareLink, error)
	CreateLink(link *po.MiddlewareLink) error
	UpdateLink(link *po.MiddlewareLink) error
	DeleteLink(id int64) error
}
