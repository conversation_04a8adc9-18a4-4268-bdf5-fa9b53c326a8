package services

import (
	"blops/app/models"
	"blops/logger"
	"blops/tmpl"
	"blops/tmpl/k8s"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	yaml "k8s.io/apimachinery/pkg/util/yaml"
	"k8s.io/client-go/discovery"
	"k8s.io/client-go/discovery/cached/memory"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/restmapper"
	"k8s.io/client-go/tools/clientcmd"

	"blops/sql"
	"bytes"
	"io"
)

const (
	// 应用模板存储目录
	AppTemplateDir = "templates/apps"
)

// AppMarketService 应用市场服务接口
type AppMarketService interface {
	// GetAppTemplates 获取所有应用模板
	GetAppTemplates(req *models.AppTemplateListRequest) (*models.AppTemplateListResponse, error)

	// GetAppTemplateDetail 获取应用模板详情
	GetAppTemplateDetail(name string) (*k8s.AppTemplateDO, error)

	// AddAppTemplate 添加应用模板
	AddAppTemplate(template *k8s.AppTemplateDO) error

	// UpdateAppTemplate 更新应用模板
	UpdateAppTemplate(template *k8s.AppTemplateDO) error

	// DeleteAppTemplate 删除应用模板
	DeleteAppTemplate(name string) error

	// DeployApp 部署应用
	DeployApp(deployment *k8s.AppDeploymentDO) ([]models.AppResource, error)

	// Below: 已部署应用 CRUD
	GetDeployedApps() ([]models.DeployedApp, error)
	GetDeployedAppByID(id uint) (*models.DeployedApp, error)
	CreateDeployedApp(app *models.DeployedApp) error
	UpdateDeployedApp(app *models.DeployedApp) error
	DeleteDeployedApp(id uint) error
	UndeployApp(app *models.DeployedApp) error
	CreateAppResource(appResource *models.AppResource) error
}

// AppMarketServiceImpl 应用市场服务实现
type AppMarketServiceImpl struct {
	// 可以添加DAO依赖
}

// NewAppMarketService 创建应用市场服务
func NewAppMarketService() AppMarketService {
	// 确保模板目录存在
	os.MkdirAll(AppTemplateDir, os.ModePerm)
	return &AppMarketServiceImpl{}
}

// GetAppTemplates 获取所有应用模板
func (s *AppMarketServiceImpl) GetAppTemplates(req *models.AppTemplateListRequest) (*models.AppTemplateListResponse, error) {
	templates := make([]*k8s.AppTemplateDO, 0)

	// 读取模板目录下的所有文件
	files, err := ioutil.ReadDir(AppTemplateDir)
	if err != nil {
		logger.Errorf("读取应用模板目录失败: %v", err)
		return nil, err
	}

	for _, file := range files {
		if file.IsDir() || !strings.HasSuffix(file.Name(), ".json") {
			continue
		}

		// 读取模板文件
		filePath := filepath.Join(AppTemplateDir, file.Name())
		jsonData, err := ioutil.ReadFile(filePath)
		if err != nil {
			logger.Errorf("读取应用模板文件失败: %v", err)
			continue
		}

		// 解析模板
		template := &k8s.AppTemplateDO{}
		if err := json.Unmarshal(jsonData, template); err != nil {
			logger.Errorf("解析应用模板失败: %v", err)
			continue
		}

		// 添加到结果
		templates = append(templates, template)
	}

	// 搜索过滤
	if req.Search != "" {
		filteredTemplates := make([]*k8s.AppTemplateDO, 0)
		for _, template := range templates {
			if strings.Contains(template.Name, req.Search) ||
				strings.Contains(template.Description, req.Search) ||
				strings.Contains(template.Category, req.Search) {
				filteredTemplates = append(filteredTemplates, template)
			}
		}
		templates = filteredTemplates
	}

	// 分页
	total := len(templates)
	start := (req.Page - 1) * req.PageSize
	end := start + req.PageSize
	if start > total {
		start = total
	}
	if end > total {
		end = total
	}

	// 返回结果
	return &models.AppTemplateListResponse{
		Total:     total,
		Templates: templates[start:end],
	}, nil
}

// GetAppTemplateDetail 获取应用模板详情
func (s *AppMarketServiceImpl) GetAppTemplateDetail(templateName string) (*k8s.AppTemplateDO, error) {
	// 新增模板名称检查，避免传入空值
	if templateName == "" {
		return nil, errors.New("模板名称不能为空")
	}

	// 读取模板文件
	fmt.Println("AppTemplateDir", AppTemplateDir)
	fmt.Println("templateName", templateName)
	filePath := filepath.Join(AppTemplateDir, templateName+".json")
	jsonData, err := ioutil.ReadFile(filePath)
	if err != nil {
		logger.Errorf("读取应用模板文件失败: %v", err)
		return nil, err
	}

	// 解析模板
	tmpl := &k8s.AppTemplateDO{}
	if err := json.Unmarshal(jsonData, tmpl); err != nil {
		logger.Errorf("解析应用模板失败: %v", err)
		return nil, err
	}

	return tmpl, nil
}

// AddAppTemplate 添加应用模板
func (s *AppMarketServiceImpl) AddAppTemplate(template *k8s.AppTemplateDO) error {
	// 检查模板是否已存在
	filePath := filepath.Join(AppTemplateDir, template.Name+".json")
	if _, err := os.Stat(filePath); err == nil {
		return errors.New("应用模板已存在")
	}

	// 序列化模板
	jsonData, err := json.MarshalIndent(template, "", "  ")
	if err != nil {
		logger.Errorf("序列化应用模板失败: %v", err)
		return err
	}

	// 保存到文件
	if err := ioutil.WriteFile(filePath, jsonData, 0644); err != nil {
		logger.Errorf("保存应用模板文件失败: %v", err)
		return err
	}

	return nil
}

// UpdateAppTemplate 更新应用模板
func (s *AppMarketServiceImpl) UpdateAppTemplate(template *k8s.AppTemplateDO) error {
	// 检查模板是否存在
	fmt.Println("template.Name", template.Name)
	filePath := filepath.Join(AppTemplateDir, template.Name+".json")
	if _, err := os.Stat(filePath); err != nil {
		return errors.New("应用模板不存在")
	}

	// 序列化模板
	jsonData, err := json.MarshalIndent(template, "", "  ")
	if err != nil {
		logger.Errorf("序列化应用模板失败: %v", err)
		return err
	}

	// 保存到文件
	if err := ioutil.WriteFile(filePath, jsonData, 0644); err != nil {
		logger.Errorf("保存应用模板文件失败: %v", err)
		return err
	}

	return nil
}

// DeleteAppTemplate 删除应用模板
func (s *AppMarketServiceImpl) DeleteAppTemplate(name string) error {
	// 检查模板是否存在
	filePath := filepath.Join(AppTemplateDir, name+".json")
	if _, err := os.Stat(filePath); err != nil {
		return errors.New("应用模板不存在")
	}

	// 删除文件
	if err := os.Remove(filePath); err != nil {
		logger.Errorf("删除应用模板文件失败: %v", err)
		return err
	}

	return nil
}

// DeployApp 部署应用，返回所有成功创建的资源信息
func (s *AppMarketServiceImpl) DeployApp(deployment *k8s.AppDeploymentDO) ([]models.AppResource, error) {
	// 检查模板名称是否为空
	if deployment.TemplateName == "" {
		return nil, errors.New("模板名称不能为空")
	}

	// 根据模板名称获取应用模板详情
	template, err := s.GetAppTemplateDetail(deployment.TemplateName)
	if err != nil {
		return nil, fmt.Errorf("获取应用模板失败: %v", err)
	}

	// 使用模板引擎处理 YAML 模板
	yamlContent, err := tmpl.TemplateHandlerStrWithStr(
		template.Name,
		template.YamlTemplate,
		&deployment.Variables,
	)
	if err != nil {
		logger.Errorf("模板处理失败: %v", err)
		return nil, err
	}

	// 获取目标命名空间，若为空则使用 "default"
	ns := deployment.Namespace
	if ns == "" {
		ns = "default"
	}

	// 获取 Kubernetes 配置，优先使用 InClusterConfig，失败则使用 KUBECONFIG
	var config *rest.Config
	if deployment.ClusterName != "" {
		// 根据传入的集群名称拼接 kubeconfig 文件路径
		kubeconfigPath := filepath.Join("/Users/<USER>/kube", deployment.ClusterName)
		config, err = clientcmd.BuildConfigFromFlags("", kubeconfigPath)
		if err != nil {
			logger.Errorf("基于集群 [%s] 的 kubeconfig 构建配置失败: %v", deployment.ClusterName, err)
			return nil, err
		}
	} else {
		// 默认逻辑：优先使用 InClusterConfig，失败则使用 KUBECONFIG环境变量
		config, err = rest.InClusterConfig()
		if err != nil {
			logger.Infof("获取 InClusterConfig 失败，尝试使用 KUBECONFIG: %v", err)
			kubeconfigPath := os.Getenv("KUBECONFIG")
			if kubeconfigPath == "" {
				kubeconfigPath = filepath.Join(os.Getenv("HOME"), ".kube", "config")
			}
			config, err = clientcmd.BuildConfigFromFlags("", kubeconfigPath)
			if err != nil {
				logger.Errorf("获取 Kubernetes 配置失败: %v", err)
				return nil, err
			}
		}
	}

	// 创建动态客户端
	dynamicClient, err := dynamic.NewForConfig(config)
	if err != nil {
		logger.Errorf("创建动态客户端失败: %v", err)
		return nil, err
	}

	// 创建 DiscoveryClient 和 RESTMapper（使用内存缓存）
	dc, err := discovery.NewDiscoveryClientForConfig(config)
	if err != nil {
		logger.Errorf("创建 DiscoveryClient 失败: %v", err)
		return nil, err
	}
	rm := restmapper.NewDeferredDiscoveryRESTMapper(memory.NewMemCacheClient(dc))

	// 使用 YAMLOrJSONDecoder 支持多文档 YAML（文档间以 --- 分隔）
	reader := bytes.NewReader([]byte(yamlContent))
	decoder := yaml.NewYAMLOrJSONDecoder(reader, 4096)

	var createdResources []models.AppResource
	// 循环解析每个 YAML 文档
	for {
		var rawObj map[string]interface{}
		err = decoder.Decode(&rawObj)
		if err != nil {
			if err == io.EOF {
				break
			}
			logger.Errorf("解码 YAML 失败: %v", err)
			return nil, err
		}
		// 跳过空文档
		if rawObj == nil || len(rawObj) == 0 {
			continue
		}

		// 构造 Unstructured 对象
		obj := &unstructured.Unstructured{Object: rawObj}
		gvk := obj.GroupVersionKind()
		logger.Infof("解码到的资源: Group=%s, Version=%s, Kind=%s", gvk.Group, gvk.Version, gvk.Kind)

		// 根据 GVK 获取 RESTMapping
		mapping, err := rm.RESTMapping(gvk.GroupKind(), gvk.Version)
		if err != nil {
			logger.Errorf("获取 RESTMapping 失败: %v", err)
			return nil, err
		}

		// 设置对象命名空间
		obj.SetNamespace(ns)
		resourceClient := dynamicClient.Resource(mapping.Resource).Namespace(ns)
		result, err := resourceClient.Create(context.Background(), obj, metav1.CreateOptions{})
		if err != nil {
			logger.Errorf("创建资源失败: Group=%s, Kind=%s: %v", gvk.Group, gvk.Kind, err)
			return nil, err
		}
		logger.Infof("成功创建资源: Group=%s, Kind=%s, Name=%s", gvk.Group, gvk.Kind, result.GetName())

		createdResources = append(createdResources, models.AppResource{
			Kind:         gvk.Kind,
			ResourceName: result.GetName(),
		})
	}

	// 返回所有成功创建的资源信息
	if len(createdResources) == 0 {
		return nil, errors.New("未创建任何资源")
	}
	return createdResources, nil
}

// GetDeployedApps 返回所有已部署应用（含其 Resources）
func (s *AppMarketServiceImpl) GetDeployedApps() ([]models.DeployedApp, error) {
	var apps []models.DeployedApp
	err := sql.GDB.Preload("Resources").Find(&apps).Error
	return apps, err
}

// GetDeployedAppByID 根据主键查单个已部署应用
func (s *AppMarketServiceImpl) GetDeployedAppByID(id uint) (*models.DeployedApp, error) {
	var app models.DeployedApp
	err := sql.GDB.Preload("Resources").First(&app, id).Error
	return &app, err
}

// CreateDeployedApp 创建一条已部署应用记录
func (s *AppMarketServiceImpl) CreateDeployedApp(app *models.DeployedApp) error {
	return sql.GDB.Create(app).Error
}

// UpdateDeployedApp 更新一条已部署应用
func (s *AppMarketServiceImpl) UpdateDeployedApp(app *models.DeployedApp) error {
	return sql.GDB.
		Model(&models.DeployedApp{}).
		Where("id = ?", app.ID).
		Updates(app).
		Error
}

// DeleteDeployedApp 删除一条已部署应用
func (s *AppMarketServiceImpl) DeleteDeployedApp(id uint) error {
	return sql.GDB.Delete(&models.DeployedApp{}, id).Error
}

// UndeployApp 先删除 Kubernetes 资源，再删除数据库记录
func (s *AppMarketServiceImpl) UndeployApp(app *models.DeployedApp) error {
	// 1) 获取Kubernetes配置，优先使用InClusterConfig
	config, err := rest.InClusterConfig()
	if err != nil {
		logger.Infof("获取 InClusterConfig 失败，尝试使用 KUBECONFIG: %v", err)
		kubeconfigPath := filepath.Join("/Users/<USER>/kube", app.Cluster)
		if kubeconfigPath == "" {
			kubeconfigPath = filepath.Join(os.Getenv("HOME"), ".kube", "config")
		}
		config, err = clientcmd.BuildConfigFromFlags("", kubeconfigPath)
		if err != nil {
			logger.Errorf("获取 Kubernetes 配置失败: %v", err)
			return err
		}
	}

	// 2) 创建动态客户端和 RESTMapper
	dynClient, err := dynamic.NewForConfig(config)
	if err != nil {
		logger.Errorf("创建动态客户端失败: %v", err)
		return err
	}

	discoveryClient, err := discovery.NewDiscoveryClientForConfig(config)
	if err != nil {
		logger.Errorf("创建 DiscoveryClient 失败: %v", err)
		return err
	}

	mapper := restmapper.NewDeferredDiscoveryRESTMapper(memory.NewMemCacheClient(discoveryClient))

	// 3) 如果没有关联资源，直接返回
	if len(app.Resources) == 0 {
		logger.Infof("应用 id %d 没有关联资源，无需删除 Kubernetes 资源", app.ID)
		return nil
	}

	var deletionErr error
	// 4) 循环删除每个关联的 Kubernetes 资源
	for _, res := range app.Resources {
		var group, version string
		switch res.Kind {
		case "Deployment":
			group = "apps"
			version = "v1"
		case "Service":
			group = ""
			version = "v1"
		case "Ingress":
			group = "networking.k8s.io"
			version = "v1"
		case "Pod":
			group = ""
			version = "v1"
		case "ConfigMap":
			group = ""
			version = "v1"
		default:
			group = ""
			version = "v1"
		}
		logger.Infof("尝试删除 Kubernetes 资源: Group=%s, Kind=%s, ResourceName=%s, Namespace=%s", group, res.Kind, res.ResourceName, app.Namespace)
		mapping, err := mapper.RESTMapping(schema.GroupKind{Group: group, Kind: res.Kind}, version)
		if err != nil {
			logger.Errorf("获取 RESTMapping 失败 (Group: %s, Kind: %s, Version: %s): %v", group, res.Kind, version, err)
			deletionErr = err
			continue
		}
		resourceClient := dynClient.Resource(mapping.Resource).Namespace(app.Namespace)
		err = resourceClient.Delete(context.TODO(), res.ResourceName, metav1.DeleteOptions{})
		if err != nil {
			logger.Errorf("删除资源失败: Group=%s, Kind=%s, ResourceName=%s: %v", group, res.Kind, res.ResourceName, err)
			deletionErr = err
		} else {
			logger.Infof("成功删除资源: Group=%s, Kind=%s, ResourceName=%s", group, res.Kind, res.ResourceName)
		}
	}
	return deletionErr
}

// CreateAppResource 创建资源记录
func (s *AppMarketServiceImpl) CreateAppResource(appResource *models.AppResource) error {
	return sql.GDB.Create(appResource).Error
}
