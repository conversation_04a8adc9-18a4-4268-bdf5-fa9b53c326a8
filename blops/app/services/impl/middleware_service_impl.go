package impl

import (
	"blops/app/models"
	"blops/app/models/po"
	"time"
)

// MiddlewareService 中间件服务实现
type MiddlewareService struct {
	envModel  *models.MiddlewareEnvMgr
	linkModel *models.MiddlewareLinkMgr
}

// 确保 MiddlewareService 实现了 MiddlewareServiceInf 接口
var MiddlewareSvc *MiddlewareService

func init() {
	MiddlewareSvc = &MiddlewareService{
		envModel:  models.NewMiddlewareEnvMgr(),
		linkModel: models.NewMiddlewareLinkMgr(),
	}
}

// =============== 环境相关方法 ===============

// GetEnv 获取单个环境
func (svc *MiddlewareService) GetEnv(id int64) (*po.MiddlewareEnv, error) {
	env, err := svc.envModel.Get(id)
	if err != nil {
		return nil, err
	}
	return &env, nil
}

// ListEnvs 获取所有环境
func (svc *MiddlewareService) ListEnvs() ([]*po.MiddlewareEnv, error) {
	return svc.envModel.Gets()
}

// CreateEnv 创建环境
func (svc *MiddlewareService) CreateEnv(env *po.MiddlewareEnv) error {
	env.CreatedAt = time.Now()
	return svc.envModel.Create(env)
}

// UpdateEnv 更新环境
func (svc *MiddlewareService) UpdateEnv(env *po.MiddlewareEnv) error {
	return svc.envModel.Update(env)
}

// DeleteEnv 删除环境
func (svc *MiddlewareService) DeleteEnv(id int64) error {
	return svc.envModel.Delete(id)
}

// =============== 链接相关方法 ===============

// GetLink 获取单个链接
func (svc *MiddlewareService) GetLink(id int64) (*po.MiddlewareLink, error) {
	link, err := svc.linkModel.Get(id)
	if err != nil {
		return nil, err
	}
	return &link, nil
}

// ListLinks 获取所有链接
func (svc *MiddlewareService) ListLinks() ([]*po.MiddlewareLink, error) {
	return svc.linkModel.Gets()
}

// ListLinksByType 根据类型获取链接
func (svc *MiddlewareService) ListLinksByType(linkType string) ([]*po.MiddlewareLink, error) {
	return svc.linkModel.GetsByType(linkType)
}

// ListLinksByEnv 根据环境ID获取链接
func (svc *MiddlewareService) ListLinksByEnv(envID int64) ([]*po.MiddlewareLink, error) {
	return svc.linkModel.GetsByEnv(envID)
}

// ListLinksByTypeAndEnv 根据类型和环境ID获取链接
func (svc *MiddlewareService) ListLinksByTypeAndEnv(linkType string, envID int64) ([]*po.MiddlewareLink, error) {
	return svc.linkModel.GetsByTypeAndEnv(linkType, envID)
}

// ListLinksWithEnvInfo 获取所有链接并包含环境信息
func (svc *MiddlewareService) ListLinksWithEnvInfo() ([]*po.MiddlewareLink, error) {
	return svc.linkModel.GetsWithEnvInfo()
}

// ListLinksByTypeWithEnvInfo 根据类型获取链接并包含环境信息
func (svc *MiddlewareService) ListLinksByTypeWithEnvInfo(linkType string) ([]*po.MiddlewareLink, error) {
	return svc.linkModel.GetsByTypeWithEnvInfo(linkType)
}

// CreateLink 创建链接
func (svc *MiddlewareService) CreateLink(link *po.MiddlewareLink) error {
	link.CreatedAt = time.Now()
	return svc.linkModel.Create(link)
}

// UpdateLink 更新链接
func (svc *MiddlewareService) UpdateLink(link *po.MiddlewareLink) error {
	return svc.linkModel.Update(link)
}

// DeleteLink 删除链接
func (svc *MiddlewareService) DeleteLink(id int64) error {
	return svc.linkModel.Delete(id)
}
