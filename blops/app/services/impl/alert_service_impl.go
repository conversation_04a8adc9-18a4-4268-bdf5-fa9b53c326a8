package impl

import (
	"blops/app/dtos"
	. "blops/app/models"
	"blops/app/models/po"
	"blops/app/services"
	"blops/config"
	alert2 "blops/enums/alert"
	"blops/enums/feishu"
	"blops/logger"
	feishu2 "blops/rest/feishu"
	"blops/tmpl"
	"blops/utils/condexpr"
	"log"
	"strconv"
	"strings"
	"time"
)

var (
	pDefaultChat = ""
	msgGroup     = &config.LarkMsgGroup{
		Size: 5,
	}
)

type AlertService struct {
	alertModel    *AlertMgr
	baseChatModel *BaseAlertChatModel
	appChatModel  *AppAlertChatModel
}

var AlertSvc *AlertService

func init() {
	AlertSvc = &AlertService{
		alertModel:    NewAlertMgr(),
		baseChatModel: NewBaseAlertChatModel(),
		appChatModel:  NewAppAlertChatModel(),
	}
	pDefaultChat = config.Conf.Alert.Platform.Lark.Chat.Default
	msgGroup = &config.Conf.Alert.Platform.Lark.Group
}

func (svc *AlertService) GetAlert(host string, status string, level string, alertId string, alertName string, cluster string, message string, page int, pageSize int, hours int) (alerts []*po.Alert, total int64, err error) {
	// 先获取不含 cluster 和 message 筛选的结果
	alerts, total, err = svc.alertModel.Get(host, status, level, alertId, alertName, cluster, message, page, pageSize, hours)
	if err != nil {
		return nil, 0, err
	}

	// 如果没有 cluster 和 message 参数，直接返回
	if cluster == "" && message == "" {
		return alerts, total, nil
	}

	// 手动过滤结果
	var filteredAlerts []*po.Alert
	for _, alert := range alerts {
		// 如果指定了 cluster 且不匹配，跳过该记录
		if cluster != "" && alert.Cluster != cluster {
			continue
		}

		// 如果指定了 message 且不包含，跳过该记录
		if message != "" && !strings.Contains(alert.Message, message) {
			continue
		}

		// 通过所有筛选条件，添加到结果中
		filteredAlerts = append(filteredAlerts, alert)
	}

	// 更新 total 数量
	total = int64(len(filteredAlerts))

	return filteredAlerts, total, nil
}

func (svc *AlertService) SaveAlert() []*po.Alert {
	alerts, err := svc.alertModel.Gets()
	//fmt.Println(alerts)
	//for k, v := range alerts {
	//	fmt.Println(k, v)
	//}
	if err != nil {
		log.Fatal(err)
		return nil
	}
	return alerts
}

func (svc *AlertService) AddAlert(a []services.Alerts) error {
	for _, h := range a {
		var alert = po.Alert{Level: h.Labels.Level, Status: h.Status, Message: h.Annotations.Message,
			Hostname: h.Labels.Hostname, Instance: h.Labels.Instance, Receiver: h.Labels.Receiver,
			Cluster: h.Labels.Cloud, Alertname: h.Labels.Alertname, Finger: h.Finger, StartedAt: h.StartsAt, EndedAt: h.EndsAt}

		err := svc.alertModel.Set(&alert)
		if err != nil {
			log.Fatal(err)
			return err
		}
	}
	return nil
}

func (svc *AlertService) BulkAdd(alertList []*po.Alert) error {
	if alertList == nil {
		return nil
	}
	return svc.alertModel.BatchInsert(alertList)
}

func (svc *AlertService) alertAppChatHandler(appId, ns, env string) string {
	if appId == "" || ns == "" || env == "" {
		return ""
	}
	var app = strings.ReplaceAll(appId, "-"+ns, "")
	var appChatPO = &po.AppAlertChatPO{
		Env:   env,
		AppId: app,
	}
	// todo: 优化此处从数据库读取频率：如内存缓存
	existChat, err := svc.appChatModel.GetWithName(appChatPO)
	if err != nil {
		logger.Errorf("Get chat by name: %s-%s, error: %v", appChatPO.AppId, appChatPO.Env, err)
		return ""
	}
	var chatId = ""
	if existChat == nil {
		chatId, err = feishu2.CreateChat(feishu2.CreateChatRequest{
			Name:     "【" + app + "】" + env + " 告警",
			ChatType: "public",
		})
		if err != nil {
			logger.Errorf("create chat error: %v", err)
			return ""
		}
		appChatPO.ChatId = chatId
		err := svc.appChatModel.Insert(appChatPO)
		if err != nil {
			logger.Errorf("insert new app chat error: %v", err)
			return chatId
		}
	} else {
		chatId = existChat.ChatId
	}
	if chatId != "" {
		// TODO: 添加群成员,获取应用负责人，同步barren-admin应用信息
		_, _ = feishu2.AddChatMembers(chatId, feishu2.ChatMembersInviteRequest{IdList: []string{"xxx"}})
	}
	return chatId
}

func (svc *AlertService) alertBaseChatHandler(env, level, receiver string) string {
	if env == "" || level == "" || receiver == "" {
		return ""
	}
	var baseChatPO = &po.BaseAlertChatPO{
		Env:      env,
		Receiver: receiver,
		Level:    level,
	}
	// todo: 优化此处从数据库读取频率：如内存缓存
	existChat, err := svc.baseChatModel.GetWithName(baseChatPO)
	if err != nil {
		logger.Errorf("Get chat by name: %s-%s-%s, error: %v", baseChatPO.Env, baseChatPO.Level, baseChatPO.Receiver, err)
		return ""
	}
	var chatId = ""
	if existChat == nil {
		chatId, err = feishu2.CreateChat(feishu2.CreateChatRequest{
			Name:     "【" + baseChatPO.Env + "告警】" + baseChatPO.Env + "-" + baseChatPO.Level,
			ChatType: "public",
		})
		if err != nil {
			logger.Errorf("create chat error: %v", err)
			return ""
		}
		baseChatPO.ChatId = chatId
		err := svc.baseChatModel.Insert(baseChatPO)
		if err != nil {
			logger.Errorf("insert base chat error: %v", err)
			return chatId
		}
	} else {
		chatId = existChat.ChatId
	}
	if chatId != "" {
		// TODO: 添加群成员,获取当前值班
		_, _ = feishu2.AddChatMembers(chatId, feishu2.ChatMembersInviteRequest{IdList: []string{"xxx"}})
	}
	return chatId
}

func alertGroupHandler(alertDTO *dtos.AlertDTO) {
	var newAlerts []dtos.Alert
	var firing = false
	var resolved = false
	for _, alert := range alertDTO.Alerts {
		if alert.Status == "firing" && !firing {
			newAlerts = append(newAlerts, alert)
			firing = true
			continue
		}
		if alert.Status == "resolved" && !resolved {
			newAlerts = append(newAlerts, alert)
			resolved = true
			continue
		}
	}
	alertDTO.Alerts = newAlerts
}

func alertDTOAnalysis(alertList []*po.Alert, alertMsgList []*dtos.AlertMsg, dto *dtos.AlertDTO) {
	var groupEnable = false
	var groupLen = len(dto.Alerts)
	if len(dto.Alerts) > msgGroup.Size {
		alertGroupHandler(dto)
		groupEnable = true
	}
	for _, alert := range dto.Alerts {
		labels := alert.Labels
		var alertMsg = &dtos.AlertMsg{
			AlertName:           labels.Alertname,
			Cluster:             labels.Cloud,
			Receiver:            condexpr.Str(labels.Receiver != "", labels.Receiver, "ops"),
			Level:               condexpr.Str(labels.Level != "", labels.Level, "NONE"),
			Instance:            labels.Instance,
			Hostname:            labels.Hostname,
			Fingerprint:         alert.Fingerprint,
			Webhook:             labels.Webhook,
			MsgType:             feishu.NULL,
			AppID:               labels.Appid,
			KubernetesNamespace: labels.KubernetesNamespace,
			Env:                 labels.ENV,
		}
		if alert.StartsAt != nil {
			alertMsg.EndsAt = alert.EndsAt.Format("2006-01-02 15:04:05")
		}
		var title = "【AlertManager】"
		switch alert.Status {
		case "firing":
			{
				title = "⚠️️" + title + " 问题告警"
				if alertMsg.Level == alert2.AlertLevelEnum[alert2.P1] {
					alertMsg.MsgType = feishu.Red
				}
				break
			}
		case "resolved":
			{
				title = "✅" + title + " 告警恢复"
				if alert.EndsAt != nil {
					alertMsg.EndsAt = alert.EndsAt.Format("2006-01-02 15:04:05")
				}
				break
			}
		}
		alertMsg.Title = title
		if groupEnable {
			alertMsg.Title = title + "（已聚合-" + strconv.Itoa(groupLen) + "条）"
		}
		if alertMsg.Level == alert2.AlertLevelEnum[alert2.P1] || alertMsg.Level == alert2.AlertLevelEnum[alert2.P2] {
			alertList = append(alertList, &po.Alert{
				Level:     alertMsg.Level,
				Status:    alert.Status,
				Message:   alertMsg.Message,
				Hostname:  alertMsg.Hostname,
				Instance:  alertMsg.Instance,
				Receiver:  alertMsg.Receiver,
				Cluster:   alertMsg.Cluster,
				Alertname: alertMsg.AlertName,
				Finger:    alertMsg.Fingerprint,
				StartedAt: alertMsg.StartsAt,
				EndedAt:   alertMsg.EndsAt,
			})
		}
		alertMsgList = append(alertMsgList, alertMsg)
	}
}

func (svc *AlertService) sendMsgHandler(alertMsgList []*dtos.AlertMsg) {
	if alertMsgList != nil {
		for _, alertMsg := range alertMsgList {
			var chatId = ""
			var webhook = ""
			if alertMsg.Receiver == "custom" {
				webhook = alertMsg.Webhook
			} else if alertMsg.Receiver == "appid" {
				chatId = svc.alertAppChatHandler(alertMsg.AppID, alertMsg.KubernetesNamespace, alertMsg.Env)
			} else {
				chatId = svc.alertBaseChatHandler(alertMsg.Env, alertMsg.Level, alertMsg.Receiver)
				//msgContent += getAtOnDutyUser(chatId, level, receiver)
			}
			if chatId != "" && webhook != "" {
				chatId = pDefaultChat
			}
			msgContent, err := tmpl.TemplateHandlerStrWithFiles[dtos.AlertMsg]("alert.tmpl", alertMsg)
			if err != nil {
				logger.Errorf("alert msg template handler error: %v", err)
				continue
			}
			logger.Info(msgContent)
			cardContent := feishu2.GenCard(alertMsg.MsgType, alertMsg.Title, msgContent)
			msgId, err := feishu2.SendCard(chatId, feishu.CHAT, cardContent)
			if err != nil {
				logger.Errorf("send lark card alert msg error: %v", err)
			}
			if msgId != "" {
				logger.Infof("send card msg success, [%s]", msgId)
			}
			time.Sleep(1000 * time.Microsecond)
		}
	}
}

func (svc *AlertService) AlertHandler(alertDTO *dtos.AlertDTO) {
	logger.Infof("alert manage handler: %v", alertDTO)
	var alertList []*po.Alert
	var alertMsgList []*dtos.AlertMsg
	alertDTOAnalysis(alertList, alertMsgList, alertDTO)
	err := svc.BulkAdd(alertList)
	if err != nil {
		logger.Errorf("batch insert alert msg error: %v", err)
		return
	}
	go svc.sendMsgHandler(alertMsgList)
	return
}
