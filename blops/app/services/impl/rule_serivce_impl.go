package impl

import (
	"blops/app/co"
	"blops/app/converter"
	. "blops/app/models"
	"blops/app/models/po"
	"blops/app/services"
	vo "blops/app/vo"
	"blops/kube"
	"blops/logger"
	"blops/tmpl/k8s"
	"blops/utils/array"
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"time"
)

type RuleService struct {
	ruleModel     *RuleMgr
	TemplateModel *TemplateMgr
}

type TemplateService struct {
	TemplateModel *TemplateMgr
}

type TreeService struct {
	treeModel *TreeMgr
}

var RuleSvc *RuleService

var TemplateSvc *TemplateService

var TreeSvc *TreeService

func init() {
	RuleSvc = &RuleService{
		ruleModel: NewRuleMgr(),
	}
	TemplateSvc = &TemplateService{
		TemplateModel: NewTemplateMgr(),
	}
	TreeSvc = &TreeService{
		treeModel: NewTreeMgr(),
	}
}

func (svc *RuleService) GetRule(types string, cls string) (ruleList []*vo.RuleVO, err error) {
	rulePOList, err := svc.ruleModel.Get(types, cls)
	if err != nil {
		return
	}
	return converter.RulePO2VOList(rulePOList)
}

func (svc *RuleService) RuleHandler(rule *services.RuleCO, opt string) error {
	logger.Infof("rule handler: %s", opt)
	templatePO, err := TemplateSvc.GetById(rule.TemplateId)
	if err != nil {
		logger.Error(err)
		return err
	}
	if templatePO == nil {
		return errors.New("未找到模版信息")
	}
	message := rule.Message
	expr := rule.Expr
	// go tmpl 获取消息message
	for _, perch := range rule.PerchList {
		var exp = `\{\{\s*\$` + perch.Key + `\s*\}\}`
		compile, _ := regexp.Compile(exp)
		if compile != nil {
			message = compile.ReplaceAllString(message, perch.Value)
			expr = compile.ReplaceAllString(expr, perch.Value)
		}
	}
	perch, err := array.ToString[po.Perch](rule.PerchList)
	if err != nil {
		return err
	}
	if rule.Cluster == "" {
		logger.Infof("*************rule.Cluster is nil")
		rule.Cluster = "ali-test"
	}
	message = strings.Replace(message, "cloud_env", rule.Cluster, -1)
	if rule.Webhook == "" {
		rule.Webhook = rule.Receiver
	}
	var rulePO = &po.Rule{AlertName: rule.AlertName, TemplateId: rule.TemplateId, Message: message,
		Expr: expr, Perch: perch, Roles: "admin", Receiver: rule.Receiver,
		Cluster: rule.Cluster, Level: rule.Level, Webhook: rule.Webhook, Period: rule.Period}

	// 处理自定义标签
	if rule.Labels != "" {
		rulePO.Labels = rule.Labels
	} else {
		// Set default labels if none provided
		rulePO.Labels = `{"user":"prometheus"}`
	}

	logger.Infof("%v", *rulePO)
	types := rule.Types
	t := time.Now().Unix()
	ruleName := fmt.Sprintf("%v-%v", types, t)
	groupName := fmt.Sprintf("%v-%v", types, t)
	if opt == "create" {
		rulePO.Types = ruleName
		err = svc.ruleModel.Set(rulePO)
	} else {
		ruleName = types
		groupName = types
		rulePO.Types = types
		err = svc.ruleModel.Put(rulePO)
	}
	if err != nil {
		logger.Errorf("create or update rule error: %v", err)
		return err
	}

	pAlertRuleDO := &k8s.PrometheusAlertRuleDO{
		Cluster: rule.Cluster,
	}
	if strings.HasPrefix(types, "log.") {
		logger.Infof("loki alert add")
		pAlertRuleDO.Name = "loki-alert-alerting-rules"
		pAlertRuleDO.Namespace = "ops"
		rulePOS, err := svc.ruleModel.Get("log.", rule.Cluster)
		if err != nil {
			logger.Errorf("list log. rules error: %v", err)
			return err
		}
		var tmplAlertList = make([]k8s.AlertRuleDO, 0)
		if len(rulePOS) > 0 {
			for _, r := range rulePOS {
				var labels map[string]string
				// 解析自定义标签
				if r.Labels != "" {
					if err := json.Unmarshal([]byte(r.Labels), &labels); err != nil {
						logger.Errorf("parse labels error: %v", err)
					}
				}

				tmplAlertList = append(tmplAlertList, k8s.AlertRuleDO{
					AlertName: r.AlertName,
					Message:   r.Message,
					Expr:      r.Expr,
					Period:    r.Period,
					Level:     r.Level,
					Receiver:  r.Receiver,
					Webhook:   r.Webhook,
					Labels:    labels,
				})
			}
		}
		if len(tmplAlertList) > 0 {
			pAlertRuleDO.AlertGroupList = []k8s.AlertGroupDO{
				{
					"should_fire",
					tmplAlertList,
				},
			}
			_, _ = kube.SetConfigmap(pAlertRuleDO, "update")
		}
	} else {
		pAlertRuleDO.Name = ruleName
		var labels map[string]string
		// 解析自定义标签
		if rulePO.Labels != "" {
			if err := json.Unmarshal([]byte(rulePO.Labels), &labels); err != nil {
				logger.Errorf("parse labels error: %v", err)
			}
		}

		var alertRuleDO = k8s.AlertRuleDO{
			AlertName: rulePO.AlertName,
			Message:   rulePO.Message,
			Expr:      rulePO.Expr,
			Period:    rulePO.Period,
			Level:     rulePO.Level,
			Receiver:  rulePO.Receiver,
			Webhook:   rulePO.Webhook,
			Labels:    labels,
		}
		pAlertRuleDO.AlertGroupList = []k8s.AlertGroupDO{
			{
				groupName,
				[]k8s.AlertRuleDO{alertRuleDO},
			},
		}
		if rule.Cluster == "ali-v3" {
			pAlertRuleDO.Namespace = "ops"
			_, err = kube.SetRule(pAlertRuleDO, opt)
		} else {
			pAlertRuleDO.Namespace = "kubesphere-monitoring-system"
			_, err = kube.SetRule(pAlertRuleDO, opt)
		}
		if err != nil {
			logger.Errorf("set rule error: %v", err)
			return err
		}
	}
	return nil
}

func (svc *TemplateService) AddTemplate(a *po.TemplatePO) error {
	err := svc.TemplateModel.Set(a)
	if err != nil {
		logger.Error(err)
		return err
	}
	return nil
}

func (svc *TemplateService) UpdateTemplate(templateCO *co.TemplateCO) (err error) {
	exist, err := svc.TemplateModel.GetWithId(templateCO.ID)
	if err != nil {
		return
	}
	if exist == nil {
		return errors.New("未找到模版信息")
	}
	//ruleList, err := RuleSvc.ruleModel.Gets([]int64{a.ID})
	//if err != nil {
	//	return
	//}
	//if ruleList
	existCode, err := svc.TemplateModel.GetWithCode(templateCO.AlertCode)
	if err != nil {
		return
	}
	if existCode != nil && existCode.ID != templateCO.ID {
		return errors.New("模版标识已存在！！！")
	}

	perch, err := array.ToString[po.Perch](templateCO.PerchList)
	if err != nil {
		return
	}
	exist.AlertName = templateCO.AlertName
	exist.AlertCode = templateCO.AlertCode
	exist.Perch = perch
	exist.Message = templateCO.Message
	exist.Expr = templateCO.Expr
	err = svc.TemplateModel.Update(exist)
	return
}

func (svc *TemplateService) PullTemplate(alertCode string) []*vo.TemplateVO {
	templatePOList, err := svc.TemplateModel.Pull(alertCode)
	list, err := converter.TemplatePO2VOList(templatePOList)
	if err != nil {
		return nil
	}
	return list
}

func (svc *TemplateService) GetById(id int64) (*po.TemplatePO, error) {
	return svc.TemplateModel.GetWithId(id)
}

func (svc *TemplateService) PerchHandler(perchList []po.Perch) (string, error) {
	var perch = ""
	if perchList != nil {
		if array.Repeat(perchList, "Key") {
			return "", errors.New("占位符不能重复")
		}
		return array.ToString[po.Perch](perchList)
	}
	return perch, nil
}

func (svc *TreeService) GetTree() []*po.TreeD {
	trees, err := svc.treeModel.Gets()

	if err != nil {
		logger.Error(err)
		return nil
	}
	return trees
}
