package impl

import (
	. "blops/app/models"
	"blops/app/models/po"
	"fmt"
	"log"
)

type ResourceService struct {
	hostModel *HostMgr
}

var ResourceSvc *ResourceService

func init() {
	ResourceSvc = &ResourceService{
		hostModel: NewHostMgr(),
	}
}

func (svc *ResourceService) ListHost() []*po.Host {
	hosts, err := svc.hostModel.Gets()
	fmt.Println(hosts)
	for k, v := range hosts {
		fmt.Println(k, v)
	}
	if err != nil {
		log.Fatal(err)
		return nil
	}
	return hosts
}
