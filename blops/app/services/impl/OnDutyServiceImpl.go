package impl

import (
	"blops/app/co"
	"blops/app/dtos"
	"blops/app/models"
	"blops/app/models/po"
	"blops/config"
	"blops/logger"
	"errors"
	"sort"
	"time"
)

type OnDutyService struct {
}

var (
	model *models.OnDutyMgr
)

func init() {
	model = models.NewOnDutyMgr()
}

func (svc *OnDutyService) New(po *po.OnDutyPO) error {
	exists, err := model.Get(po.UserType, po.UserId)
	if err != nil {
		return err
	}
	if exists != nil {
		logger.Error("had existed!!!")
		return errors.New("existed")
	}
	return model.Insert(po)
}

func (svc *OnDutyService) Get(id int64) (po *po.OnDutyPO, err error) {
	return model.GetById(id)
}

func (svc *OnDutyService) Modify(onDutyCO co.OnDutyCustomSetCO) error {
	dutyPO, err := model.GetById(onDutyCO.ID)
	if err != nil {
		logger.Error(err)
		return err
	}
	if dutyPO == nil {
		return errors.New("not found")
	}
	dutyPO.Start = onDutyCO.Start
	dutyPO.End = onDutyCO.End
	err = model.Update(dutyPO)
	if err != nil {
		logger.Error(err)
		return err
	}
	return nil
}

func dutyExchangeHandler(dutyUserList []*po.OnDutyPO) {
	sort.Slice(dutyUserList, func(i, j int) bool {
		return dutyUserList[i].ID < dutyUserList[j].ID
	})
	dutyLen := len(dutyUserList)
	var preDuty *po.OnDutyPO
	var nextDuty *po.OnDutyPO
	for i := 0; i < dutyLen-1; i++ {
		duty := dutyUserList[i]
		if duty.Status == "on" {
			duty.Status = "off"
			preDuty = duty
			i++
			if i > dutyLen-1 {
				i = 0
			}
			dutyUserList[i].Status = "on"
			nextDuty = dutyUserList[i]
			break
		}
	}
	if preDuty != nil && nextDuty != nil {
		err := model.Update(preDuty)
		if err != nil {
			logger.Error(err)
			return
		}
		err = model.Update(nextDuty)
		if err != nil {
			logger.Error(err)
			return
		}
	} else {
		logger.Error("duty change error...")
	}
}

func (svc *OnDutyService) CheckDutyOn() {
	if config.Conf.Duty.Mode == "custom" {
		return
	}
	nowTime := time.Now().UnixMilli()
	logger.Infof("time now: %d", nowTime)
	// 计算当前值班并更新
	dutyUserList, err := model.List(&dtos.OnDutyListDTO{})
	if err != nil {
		logger.Error(err)
		return
	}
	if len(dutyUserList) == 0 || dutyUserList == nil {
		return
	}
	// 分类处理
	typeSet := make(map[string][]*po.OnDutyPO)
	for _, dutyPO := range dutyUserList {
		if typeSet[dutyPO.UserType] == nil {
			typeSet[dutyPO.UserType] = []*po.OnDutyPO{}
		}
		typeSet[dutyPO.UserType] = append(typeSet[dutyPO.UserType], dutyPO)
	}
	for _, pos := range typeSet {
		dutyExchangeHandler(pos)
	}
}
