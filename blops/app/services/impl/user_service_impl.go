package impl

import (
	"blops/app/models"
	"blops/app/models/po"
	. "blops/base"
	. "blops/enums/http"
	"blops/rest/ldap"
	"blops/utils"
	"errors"
	"log"
)

type userServiceImpl struct {
	*models.UserModel
}

var UserSvcImpl *userServiceImpl

func init() {
	UserSvcImpl = &userServiceImpl{
		models.NewUserModel(),
	}
}

func (svc *userServiceImpl) GetUserByName(username string) (*po.UserPO, error) {
	return svc.GetFromName(username)
}

func (svc *userServiceImpl) GetUserByEmail(email string) (*po.UserPO, error) {
	if email == "" {
		return nil, errors.New("email cannot be empty")
	}
	return svc.GetFromEmail(email)
}

func (svc *userServiceImpl) ListAll() ([]*po.UserPO, error) {
	return svc.List()
}

func (svc *userServiceImpl) ListNoOpenId() ([]*po.UserPO, error) {
	return svc.ListWithNoOpenId()
}

func (svc *userServiceImpl) BulkUpdate(userPOS []*po.UserPO) (err error) {
	for _, userPO := range userPOS {
		err = svc.Update(userPO)
		if err != nil {
			return
		}
	}
	return
}

func (svc *userServiceImpl) Login(username string, password string) (*po.UserPO, *ApiRes) {
	//userPO, uErr := svc.GetUserByName(username)

	displayName, uErr := ldap.AuthLdap(username, password)
	if uErr != nil {
		//log.Println(uErr)
		return nil, Unauthorized(Options{Msg: "用户名密码错误"})
	}

	token, sErr := utils.SignJwtToken(utils.MyCustomClaims{
		Username:    username,
		DisplayName: displayName,
	})
	if sErr != nil {
		log.Println(sErr)
		return nil, ServerError(Options{Msg: sErr.Error()})
	}

	userPO := &po.UserPO{
		Username:    username,
		Password:    password,
		Token:       token,
		DisplayName: displayName,
	}

	return userPO, nil
}
