package services

import (
	"blops/app/dtos"
	"blops/app/models/po"
)

type AlertServiceInf interface {
	SaveAlert() []*po.Alert
	<PERSON>lert(host string, status string, level string, alertId string, alertName string, cluster string, message string, page int, pageSize int, hours int) ([]*po.Alert, int64, error)
	AddAlert(a []Alerts) error
	<PERSON><PERSON><PERSON>and<PERSON>(alertDTO *dtos.AlertDTO)
}

type AlertData struct {
	Alerts []Alerts `form:"alerts" json:"alerts" uri:"alerts"`
}

type AlertList struct {
	Hostname  string `form:"hostname" json:"hostname" uri:"hostname"`
	Status    string `form:"status" json:"status" uri:"status"`
	Level     string `form:"level" json:"level" uri:"level"`
	AlertId   string `form:"alert_id" json:"alert_id" uri:"alert_id"`
	AlertName string `form:"alert_name" json:"alert_name" uri:"alert_name"`
	Page      int    `form:"page" json:"page" uri:"page"`
	PageSize  int    `form:"page_size" json:"page_size" uri:"page_size"`
	Cluster   string `form:"cluster" json:"cluster" uri:"cluster"`
	Message   string `form:"message" json:"message" uri:"message"`
	H         int    `form:"h" json:"h" uri:"h"`
}

type Alerts struct {
	Finger      string      `form:"fingerprint" json:"fingerprint" uri:"fingerprint"`
	StartsAt    string      `form:"startsAt" json:"startsAt" uri:"startsAt"`
	EndsAt      string      `form:"endsAt" json:"endsAt" uri:"endsAt"`
	Status      string      `form:"status" json:"status" uri:"status"`
	Labels      Labels      `form:"labels" json:"labels" uri:"labels"`
	Annotations Annotations `form:"annotations" json:"annotations" uri:"annotations"`
}

type Annotations struct {
	Message string `form:"message" json:"message" uri:"message"`
}

type Labels struct {
	Alertname string `form:"alertname" json:"alertname" uri:"alertname"`
	Hostname  string `form:"hostname" json:"hostname" uri:"hostname"`
	Instance  string `form:"instance" json:"instance" uri:"instance"`
	Level     string `form:"level" json:"level" uri:"level"`
	Receiver  string `form:"receiver" json:"receiver" uri:"receiver"`
	Cloud     string `form:"cloud" json:"cloud" uri:"cloud"`
}
