package converter

import (
	"blops/app/models/po"
	"blops/app/vo"
	"blops/logger"
	"encoding/json"
)

func TemplatePO2VO(template *po.TemplatePO) (*vo.TemplateVO, error) {
	TemplateVO := &vo.TemplateVO{
		ID:        template.ID,
		AlertName: template.AlertName,
		AlertCode: template.AlertCode,
		Message:   template.Message,
		Expr:      template.Expr,
		CreatedAt: template.CreatedAt.UnixMilli(),
		UpdatedAt: template.UpdatedAt.UnixMilli(),
	}
	var perchList []po.Perch
	if template.Perch != "" {
		err := json.Unmarshal([]byte(template.Perch), &perchList)
		if err != nil {
			logger.Error(err)
			return nil, err
		}
		TemplateVO.PerchList = perchList
	}
	return TemplateVO, nil
}

func TemplatePO2VOList(templatePOList []*po.TemplatePO) ([]*vo.TemplateVO, error) {
	TemplateVOList := make([]*vo.TemplateVO, 0)
	for _, TemplatePO := range templatePOList {
		TemplateVO, err := TemplatePO2VO(TemplatePO)
		if err != nil {
			logger.Errorf("unMarshal Template perch error: %v", err)
			return nil, err
		} else {
			TemplateVOList = append(TemplateVOList, TemplateVO)
		}
	}
	return TemplateVOList, nil
}

func RulePO2VO(rule *po.Rule) (*vo.RuleVO, error) {
	ruleVO := &vo.RuleVO{
		ID:          rule.ID,
		AlertName:   rule.AlertName,
		Types:       rule.Types,
		Message:     rule.Message,
		Expr:        rule.Expr,
		Roles:       rule.Roles,
		Receiver:    rule.Receiver,
		Period:      rule.Period,
		Cluster:     rule.Cluster,
		Webhook:     rule.Webhook,
		Level:       rule.Level,
		Labels:      rule.Labels,
		GrafanaLink: rule.GrafanaLink,
		GrafanaName: rule.GrafanaName,
		TemplateId:  rule.TemplateId,
		CreatedAt:   rule.CreatedAt.UnixMilli(),
		UpdatedAt:   rule.UpdatedAt.UnixMilli(),
	}
	var perchList []po.Perch
	if rule.Perch != "" {
		err := json.Unmarshal([]byte(rule.Perch), &perchList)
		if err != nil {
			logger.Error(err)
			return nil, err
		}
		ruleVO.PerchList = perchList
	}
	return ruleVO, nil
}

func RulePO2VOList(rulePOList []*po.Rule) ([]*vo.RuleVO, error) {
	ruleVOList := make([]*vo.RuleVO, 0)
	for _, rulePO := range rulePOList {
		ruleVO, err := RulePO2VO(rulePO)
		if err != nil {
			logger.Errorf("unMarshal rule perch error: %v", err)
			return nil, err
		} else {
			ruleVOList = append(ruleVOList, ruleVO)
		}
	}
	return ruleVOList, nil
}
