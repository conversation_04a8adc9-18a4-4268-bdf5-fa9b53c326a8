package vo

import (
	"blops/app/models/po"
)

type TemplateVO struct {
	ID        int64      `json:"id"`
	AlertName string     `json:"alert_name"`
	AlertCode string     `json:"alert_code"`
	Message   string     `json:"message"`
	Expr      string     `json:"expr"`
	PerchList []po.Perch `json:"perchList"`
	CreatedAt int64      `json:"created_at"`
	UpdatedAt int64      `json:"updated_at"`
}

type RuleVO struct {
	ID          int64      `json:"id"`
	Types       string     `json:"types"`
	AlertName   string     `json:"alert_name"`
	Message     string     `json:"message"`
	Expr        string     `json:"expr"`
	PerchList   []po.Perch `json:"perchList"`
	Webhook     string     `json:"webhook"`
	Period      string     `json:"period"`
	Roles       string     `json:"roles"`
	Receiver    string     `json:"receiver"`
	Cluster     string     `json:"cluster"`
	Level       string     `json:"level"`
	Labels      string     `json:"labels"`
	GrafanaName string     `json:"grafana_name"`
	GrafanaLink string     `json:"grafana_link"`
	TemplateId  int64      `json:"template_id"`
	CreatedAt   int64      `json:"createdAt"`
	UpdatedAt   int64      `json:"updatedAt"`
}
