package dto

import (
	"blops/app/models/po"
	"time"
)

// 环境相关DTO

// MiddlewareEnvCreateRequest 创建环境请求
type MiddlewareEnvCreateRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
}

// MiddlewareEnvUpdateRequest 更新环境请求
type MiddlewareEnvUpdateRequest struct {
	ID          int64  `json:"id" binding:"required"`
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
}

// MiddlewareEnvResponse 环境响应
type MiddlewareEnvResponse struct {
	ID          int64     `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

// 链接相关DTO

// MiddlewareLinkCreateRequest 创建链接请求
type MiddlewareLinkCreateRequest struct {
	Env         int64  `json:"env" binding:"required"`
	Name        string `json:"name" binding:"required"`
	Type        string `json:"type" binding:"required"`
	URL         string `json:"url" binding:"required"`
	Description string `json:"description"`
}

// MiddlewareLinkUpdateRequest 更新链接请求
type MiddlewareLinkUpdateRequest struct {
	ID          int64  `json:"id" binding:"required"`
	Env         int64  `json:"env" binding:"required"`
	Name        string `json:"name" binding:"required"`
	Type        string `json:"type" binding:"required"`
	URL         string `json:"url" binding:"required"`
	Description string `json:"description"`
}

// MiddlewareLinkResponse 链接响应
type MiddlewareLinkResponse struct {
	ID          int64                  `json:"id"`
	Env         int64                  `json:"env"`
	Name        string                 `json:"name"`
	Type        string                 `json:"type"`
	URL         string                 `json:"url"`
	Description string                 `json:"description"`
	CreatedAt   time.Time              `json:"createdAt"`
	UpdatedAt   time.Time              `json:"updatedAt"`
	EnvInfo     *MiddlewareEnvResponse `json:"envInfo,omitempty"`
}

// MiddlewareLinkTypeFilterRequest 按类型过滤链接请求
type MiddlewareLinkTypeFilterRequest struct {
	Type string `json:"type" binding:"required"`
}

// MiddlewareLinkEnvFilterRequest 按环境过滤链接请求
type MiddlewareLinkEnvFilterRequest struct {
	Env int64 `json:"env" binding:"required"`
}

// MiddlewareLinkTypeAndEnvFilterRequest 按类型和环境过滤链接请求
type MiddlewareLinkTypeAndEnvFilterRequest struct {
	Type string `json:"type" binding:"required"`
	Env  int64  `json:"env" binding:"required"`
}

// 转换函数

// ToMiddlewareEnvPO 将DTO转换为PO
func (req *MiddlewareEnvCreateRequest) ToMiddlewareEnvPO() *po.MiddlewareEnv {
	return &po.MiddlewareEnv{
		Name:        req.Name,
		Description: req.Description,
	}
}

// ToMiddlewareEnvPO 将DTO转换为PO
func (req *MiddlewareEnvUpdateRequest) ToMiddlewareEnvPO() *po.MiddlewareEnv {
	return &po.MiddlewareEnv{
		ID:          req.ID,
		Name:        req.Name,
		Description: req.Description,
	}
}

// ToMiddlewareEnvResponse 将PO转换为DTO
func ToMiddlewareEnvResponse(env *po.MiddlewareEnv) *MiddlewareEnvResponse {
	return &MiddlewareEnvResponse{
		ID:          env.ID,
		Name:        env.Name,
		Description: env.Description,
		CreatedAt:   env.CreatedAt,
		UpdatedAt:   env.UpdatedAt,
	}
}

// ToMiddlewareLinkPO 将DTO转换为PO
func (req *MiddlewareLinkCreateRequest) ToMiddlewareLinkPO() *po.MiddlewareLink {
	return &po.MiddlewareLink{
		Env:         req.Env,
		Name:        req.Name,
		Type:        req.Type,
		URL:         req.URL,
		Description: req.Description,
	}
}

// ToMiddlewareLinkPO 将DTO转换为PO
func (req *MiddlewareLinkUpdateRequest) ToMiddlewareLinkPO() *po.MiddlewareLink {
	return &po.MiddlewareLink{
		ID:          req.ID,
		Env:         req.Env,
		Name:        req.Name,
		Type:        req.Type,
		URL:         req.URL,
		Description: req.Description,
	}
}

// ToMiddlewareLinkResponse 将PO转换为DTO
func ToMiddlewareLinkResponse(link *po.MiddlewareLink) *MiddlewareLinkResponse {
	response := &MiddlewareLinkResponse{
		ID:          link.ID,
		Env:         link.Env,
		Name:        link.Name,
		Type:        link.Type,
		URL:         link.URL,
		Description: link.Description,
		CreatedAt:   link.CreatedAt,
		UpdatedAt:   link.UpdatedAt,
	}

	if link.EnvInfo != nil {
		response.EnvInfo = ToMiddlewareEnvResponse(link.EnvInfo)
	}

	return response
}
