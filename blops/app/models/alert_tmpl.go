package models

import (
	"blops/app/models/po"
	. "blops/sql"
	"context"
	"errors"
	"gorm.io/gorm"
)

type TemplateMgr struct {
	*BaseMgr
}

// NewTemplateMgr open func
func NewTemplateMgr() *TemplateMgr {
	ctx, cancel := context.WithCancel(context.Background())
	return &TemplateMgr{BaseMgr: &BaseMgr{DB: GDB.Table("t_expr"), IsRelated: GlobalIsRelated, Ctx: ctx, Cancel: cancel, Timeout: -1}}
}

// GetTableName get sql table name.获取数据库名字
func (obj *TemplateMgr) GetTableName() string {
	return "t_expr"
}

// Reset 重置gorm会话
func (obj *TemplateMgr) Reset() *TemplateMgr {
	obj.New()
	return obj
}

func (obj *TemplateMgr) GetWithId(id int64) (*po.TemplatePO, error) {
	var result *po.TemplatePO
	err := obj.DB.WithContext(obj.Ctx).Model(po.TemplatePO{}).Where("`id` = ?", id).First(&result).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return result, nil
}

func (obj *TemplateMgr) GetWithCode(alertCode string) (*po.TemplatePO, error) {
	var result *po.TemplatePO
	err := obj.DB.WithContext(obj.Ctx).Model(po.TemplatePO{}).Where("alert_code = ?", alertCode).First(&result).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return result, nil
}

// Set 插入
func (obj *TemplateMgr) Set(Template *po.TemplatePO) (err error) {
	exist, err := obj.GetWithCode(Template.AlertCode)
	if err != nil {
		return
	}
	if exist == nil {
		err = obj.DB.WithContext(obj.Ctx).Model(po.TemplatePO{}).Create(&Template).Error
		return
	}
	return errors.New("告警标识已存在！")
}

// Update 更新模版
func (obj *TemplateMgr) Update(Template *po.TemplatePO) (err error) {
	err = obj.DB.WithContext(obj.Ctx).Model(po.TemplatePO{}).Where("alert_code = ?", Template.AlertCode).Updates(&Template).Error
	return
}

func (obj *TemplateMgr) Pull(alertCode string) (result []*po.TemplatePO, err error) {
	if alertCode == "" {
		err = obj.DB.WithContext(obj.Ctx).Model(po.TemplatePO{}).Where("deleted_at = ?", "0").Find(&result).Error
		return
	}
	err = obj.DB.WithContext(obj.Ctx).Model(po.TemplatePO{}).Where("alert_code like ?", "%"+alertCode+"%").Find(&result).Error
	return
}
