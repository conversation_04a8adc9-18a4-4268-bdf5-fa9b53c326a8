package models

import (
	"blops/app/models/po"
	. "blops/sql"
	"context"
)

type RuleMgr struct {
	*BaseMgr
}

// NewRuleMgr open func
func NewRuleMgr() *RuleMgr {
	ctx, cancel := context.WithCancel(context.Background())
	return &RuleMgr{BaseMgr: &BaseMgr{DB: GDB.Table("t_rule"), IsRelated: GlobalIsRelated, Ctx: ctx, Cancel: cancel, Timeout: -1}}
}

// GetTableName get sql table name.获取数据库名字
func (obj *RuleMgr) GetTableName() string {
	return "t_rule"
}

// Reset 重置gorm会话
func (obj *RuleMgr) Reset() *RuleMgr {
	obj.New()
	return obj
}

// Set 插入
func (obj *RuleMgr) Set(rule *po.Rule) (err error) {
	err = obj.DB.WithContext(obj.Ctx).Model(po.Rule{}).Create(&rule).Error
	return
}

func (obj *RuleMgr) Put(rule *po.Rule) (err error) {
	err = obj.DB.WithContext(obj.Ctx).Model(po.Rule{}).Where("types = ?", rule.Types).Updates(&rule).Error
	return
}

// Get 获取
func (obj *RuleMgr) Get(types string, cls string) (result []*po.Rule, err error) {
	if types != "" {
		err = obj.DB.WithContext(obj.Ctx).Model(po.Rule{}).Where("cluster = ?", cls).Where("types like ?", "%"+types+"%").Find(&result).Error
		return
	}
	err = obj.DB.WithContext(obj.Ctx).Model(po.Rule{}).Where("cluster = ?", cls).Find(&result).Error
	return
}

// Gets 获取批量结果
func (obj *RuleMgr) Gets(templateIds []int64) (results []*po.Rule, err error) {
	if templateIds == nil {
		err = obj.DB.WithContext(obj.Ctx).Model(po.Rule{}).Find(&results).Error
		return
	}
	err = obj.DB.WithContext(obj.Ctx).Model(po.Rule{}).Where("template_id in (?)", templateIds).Find(&results).Error
	return
}
