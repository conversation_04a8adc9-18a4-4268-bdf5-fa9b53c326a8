package models

import (
	"time"
)

// Cluster 对应 cluster 表
// 存储集群信息
// gorm 始终使用单数表名
// UNIQUE(name)
// 自动维护 CreatedAt 和 UpdatedAt 字段
//
// CREATE TABLE cluster (
//   id INT AUTO_INCREMENT PRIMARY KEY,
//   name VARCHAR(128) NOT NULL UNIQUE,
//   description VARCHAR(256),
//   created_at DATETIME, updated_at DATETIME
// )
//
// JSON 字段请使用驼峰命名

type Cluster struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	Name        string    `gorm:"size:128;unique;not null" json:"name"`
	Description string    `gorm:"size:256" json:"description"`
	CreatedAt   time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updatedAt" gorm:"autoUpdateTime"`
}

// Namespace 对应 namespace 表
// CREATE TABLE namespace (
//   id INT AUTO_INCREMENT PRIMARY KEY,
//   cluster_id INT NOT NULL,
//   name VARCHAR(128) NOT NULL,
//   created_at DATETIME,
//   UNIQUE KEY (cluster_id, name)
// )

// 与 Cluster 外键关联, ON DELETE CASCADE

type Namespace struct {
	ID        uint      `gorm:"primaryKey" json:"id"`
	ClusterID uint      `gorm:"not null;index;uniqueIndex:uk_ns_cluster" json:"clusterId"`
	Name      string    `gorm:"size:128;not null;uniqueIndex:uk_ns_cluster" json:"name"`
	CreatedAt time.Time `json:"createdAt" gorm:"autoCreateTime"`
}

// AppTemplate 对应 app_template 表
// CREATE TABLE app_template (
//   id INT AUTO_INCREMENT PRIMARY KEY,
//   code VARCHAR(64) NOT NULL UNIQUE,
//   name VARCHAR(128) NOT NULL,
//   version VARCHAR(32),
//   description TEXT,
//   created_at DATETIME, updated_at DATETIME
// )

type AppTemplate struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	Code        string    `gorm:"size:64;unique;not null" json:"code"`
	Name        string    `gorm:"size:128;not null" json:"name"`
	Version     string    `gorm:"size:32" json:"version"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updatedAt" gorm:"autoUpdateTime"`
}
