package models

import (
	"blops/app/models/po"
	. "blops/sql"
	"context"
	"gorm.io/gorm"
)

type BaseAlertChatModel struct {
	*BaseMgr
}

func NewBaseAlertChatModel() *BaseAlertChatModel {
	ctx, cancel := context.WithCancel(context.Background())
	return &BaseAlertChatModel{BaseMgr: &BaseMgr{DB: GDB.Table("t_alert_base_chat"), IsRelated: false, Ctx: ctx, Cancel: cancel, Timeout: -1}}
}

func (obj *BaseAlertChatModel) Insert(chatPO *po.BaseAlertChatPO) error {
	return obj.DB.WithContext(obj.Ctx).Model(po.BaseAlertChatPO{}).Create(&chatPO).Error
}

func (obj *BaseAlertChatModel) GetWithName(chatPO *po.BaseAlertChatPO) (result *po.BaseAlertChatPO, err error) {
	err = obj.DB.WithContext(obj.Ctx).Model(po.BaseAlertChatPO{}).Where("`env` = ? and receiver = ? and `level` = ?", chatPO.Env, chatPO.Receiver, chatPO.Level).First(&result).Error
	if err == gorm.ErrRecordNotFound {
		err = nil
	}
	return
}
