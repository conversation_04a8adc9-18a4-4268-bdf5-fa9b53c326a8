package models

import (
	"blops/tmpl/k8s"
	"os"
	"path/filepath"
	"time"

	"github.com/sirupsen/logrus"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
)

var logger = logrus.New()

// DeployedApp 对应 deployed_app 表
type DeployedApp struct {
	ID        uint          `gorm:"primaryKey" json:"id"`
	Name      string        `json:"name"`
	Template  string        `json:"template"`  // 存 template.code 或 name
	Cluster   string        `json:"cluster"`   // 存 cluster.name
	Namespace string        `json:"namespace"` // 存 namespace.name
	Status    string        `json:"status"`
	CreatedAt time.Time     `json:"createdAt"`
	Resources []AppResource `gorm:"foreignKey:AppID" json:"resources"`
}

// AppResource 对应 app_resource 表
type AppResource struct {
	ID           uint      `gorm:"primaryKey"`
	AppID        uint      `json:"-"`
	Kind         string    `json:"kind"`
	ResourceName string    `json:"resourceName"`
	CreatedAt    time.Time `json:"-"`
}

// AppTemplateListRequest 应用模板列表请求
type AppTemplateListRequest struct {
	Page     int    `json:"page" example:"1"`
	PageSize int    `json:"page_size" example:"10"`
	Search   string `json:"search" example:""`
	Category string `json:"category" example:""`
}

// AppTemplateListResponse 应用模板列表响应
type AppTemplateListResponse struct {
	Total     int                  `json:"total"`
	Templates []*k8s.AppTemplateDO `json:"templates"`
}

// AppTemplateRequest 应用模板请求
type AppTemplateRequest struct {
	Name         string                    `json:"name" example:"nginx"`
	Description  string                    `json:"description" example:"Nginx Web Server"`
	Category     string                    `json:"category" example:"Web服务器"`
	Icon         string                    `json:"icon" example:"nginx-icon"`
	Version      string                    `json:"version" example:"1.0.0"`
	Maintainer   string                    `json:"maintainer" example:"admin"`
	YamlTemplate string                    `json:"yamlTemplate" example:"apiVersion: v1\nkind: Pod\n..."`
	Variables    []k8s.AppTemplateVariable `json:"variables"`
}

// AppDeploymentRequest 应用部署请求
type AppDeploymentRequest struct {
	TemplateName string                 `json:"templateName" example:"nginx"`
	Namespace    string                 `json:"namespace" example:"default"`
	ClusterName  string                 `json:"cluster" example:"test-cluster"`
	Variables    map[string]interface{} `json:"variables" example:"{\"replicas\": 3, \"image\": \"nginx:1.19\"}"`
}

// AppTemplateToDTO 将请求转换为DTO
func (req *AppTemplateRequest) AppTemplateToDTO() *k8s.AppTemplateDO {
	return &k8s.AppTemplateDO{
		Name:         req.Name,
		Description:  req.Description,
		Category:     req.Category,
		Icon:         req.Icon,
		Version:      req.Version,
		Maintainer:   req.Maintainer,
		YamlTemplate: req.YamlTemplate,
		Variables:    req.Variables,
	}
}

// AppDeploymentToDTO 将请求转换为DTO
func (req *AppDeploymentRequest) AppDeploymentToDTO() *k8s.AppDeploymentDO {
	return &k8s.AppDeploymentDO{
		TemplateName: req.TemplateName,
		Namespace:    req.Namespace,
		ClusterName:  req.ClusterName,
		Variables:    req.Variables,
	}
}

// GetKubernetesConfig 获取 Kubernetes 配置
func GetKubernetesConfig(deployment *AppDeploymentRequest) (*rest.Config, error) {
	// 获取 Kubernetes 配置，优先使用 InClusterConfig，失败则使用 KUBECONFIG
	var config *rest.Config
	if deployment.ClusterName != "" {
		// 根据传入的集群名称拼接 kubeconfig 文件路径
		kubeconfigPath := filepath.Join("/Users/<USER>/kube", deployment.ClusterName)
		logger.Infof("使用固定集群 [%s] 的 kubeconfig 文件路径: %s", deployment.ClusterName, kubeconfigPath)
		var err error
		config, err = clientcmd.BuildConfigFromFlags("", kubeconfigPath)
		if err != nil {
			logger.Errorf("基于集群 [%s] 的 kubeconfig 构建配置失败: %v", deployment.ClusterName, err)
			return nil, err
		}
	} else {
		// 默认逻辑：优先获取 InClusterConfig
		var err error
		config, err = rest.InClusterConfig()
		if err != nil {
			// 获取环境变量细节，便于排查
			host := os.Getenv("KUBERNETES_SERVICE_HOST")
			port := os.Getenv("KUBERNETES_SERVICE_PORT")
			logger.Errorf("获取 InClusterConfig 失败: %v. 环境变量 KUBERNETES_SERVICE_HOST=%s, KUBERNETES_SERVICE_PORT=%s", err, host, port)
			// 尝试使用 KUBECONFIG 环境变量构建配置
			kubeconfigPath := os.Getenv("KUBECONFIG")
			if kubeconfigPath == "" {
				kubeconfigPath = filepath.Join(os.Getenv("HOME"), ".kube", "config")
				logger.Infof("未设置 KUBECONFIG，将使用默认路径: %s", kubeconfigPath)
			} else {
				logger.Infof("使用环境变量 KUBECONFIG 指定的路径: %s", kubeconfigPath)
			}
			config, err = clientcmd.BuildConfigFromFlags("", kubeconfigPath)
			if err != nil {
				logger.Errorf("通过 kubeconfig 文件 [%s] 构建 Kubernetes 配置失败: %v", kubeconfigPath, err)
				return nil, err
			}
			logger.Infof("成功通过 kubeconfig 文件 [%s] 构建 Kubernetes 配置", kubeconfigPath)
		} else {
			logger.Infof("成功加载 InClusterConfig")
		}
	}
	return config, nil
}
