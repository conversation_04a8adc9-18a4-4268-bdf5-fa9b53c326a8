package models

import (
	"blops/app/models/po"
	. "blops/sql"
	"context"
)

// MiddlewareEnvMgr 环境管理器
type MiddlewareEnvMgr struct {
	*BaseMgr
}

// NewMiddlewareEnvMgr 创建环境管理器
func NewMiddlewareEnvMgr() *MiddlewareEnvMgr {
	ctx, cancel := context.WithCancel(context.Background())
	return &MiddlewareEnvMgr{BaseMgr: &BaseMgr{DB: GDB.Table("t_middleware_env"), IsRelated: GlobalIsRelated, Ctx: ctx, Cancel: cancel, Timeout: -1}}
}

// GetTableName 获取表名
func (obj *MiddlewareEnvMgr) GetTableName() string {
	return "t_middleware_env"
}

// Reset 重置gorm会话
func (obj *MiddlewareEnvMgr) Reset() *MiddlewareEnvMgr {
	obj.New()
	return obj
}

// Get 获取单个环境
func (obj *MiddlewareEnvMgr) Get(id int64) (result po.MiddlewareEnv, err error) {
	err = obj.DB.WithContext(obj.Ctx).Where("id = ?", id).First(&result).Error
	return
}

// Gets 获取所有环境
func (obj *MiddlewareEnvMgr) Gets() (results []*po.MiddlewareEnv, err error) {
	err = obj.DB.WithContext(obj.Ctx).Find(&results).Error
	return
}

// Create 创建环境
func (obj *MiddlewareEnvMgr) Create(env *po.MiddlewareEnv) error {
	return obj.DB.WithContext(obj.Ctx).Create(env).Error
}

// Update 更新环境
func (obj *MiddlewareEnvMgr) Update(env *po.MiddlewareEnv) error {
	return obj.DB.WithContext(obj.Ctx).Where("id = ?", env.ID).Updates(env).Error
}

// Delete 删除环境
func (obj *MiddlewareEnvMgr) Delete(id int64) error {
	return obj.DB.WithContext(obj.Ctx).Where("id = ?", id).Delete(&po.MiddlewareEnv{}).Error
}

// MiddlewareLinkMgr 链接管理器
type MiddlewareLinkMgr struct {
	*BaseMgr
}

// NewMiddlewareLinkMgr 创建链接管理器
func NewMiddlewareLinkMgr() *MiddlewareLinkMgr {
	ctx, cancel := context.WithCancel(context.Background())
	return &MiddlewareLinkMgr{BaseMgr: &BaseMgr{DB: GDB.Table("t_middleware_link"), IsRelated: GlobalIsRelated, Ctx: ctx, Cancel: cancel, Timeout: -1}}
}

// GetTableName 获取表名
func (obj *MiddlewareLinkMgr) GetTableName() string {
	return "t_middleware_link"
}

// Reset 重置gorm会话
func (obj *MiddlewareLinkMgr) Reset() *MiddlewareLinkMgr {
	obj.New()
	return obj
}

// Get 获取单个链接
func (obj *MiddlewareLinkMgr) Get(id int64) (result po.MiddlewareLink, err error) {
	err = obj.DB.WithContext(obj.Ctx).Where("id = ?", id).First(&result).Error
	return
}

// Gets 获取所有链接
func (obj *MiddlewareLinkMgr) Gets() (results []*po.MiddlewareLink, err error) {
	err = obj.DB.WithContext(obj.Ctx).Find(&results).Error
	return
}

// GetsByType 根据类型获取链接
func (obj *MiddlewareLinkMgr) GetsByType(linkType string) (results []*po.MiddlewareLink, err error) {
	err = obj.DB.WithContext(obj.Ctx).Where("type = ?", linkType).Find(&results).Error
	return
}

// GetsByEnv 根据环境ID获取链接
func (obj *MiddlewareLinkMgr) GetsByEnv(envID int64) (results []*po.MiddlewareLink, err error) {
	err = obj.DB.WithContext(obj.Ctx).Where("env = ?", envID).Find(&results).Error
	return
}

// GetsByTypeAndEnv 根据类型和环境ID获取链接
func (obj *MiddlewareLinkMgr) GetsByTypeAndEnv(linkType string, envID int64) (results []*po.MiddlewareLink, err error) {
	err = obj.DB.WithContext(obj.Ctx).Where("type = ? AND env = ?", linkType, envID).Find(&results).Error
	return
}

// GetsWithEnvInfo 获取所有链接并包含环境信息
func (obj *MiddlewareLinkMgr) GetsWithEnvInfo() (results []*po.MiddlewareLink, err error) {
	err = obj.DB.WithContext(obj.Ctx).Preload("EnvInfo").Find(&results).Error
	return
}

// GetsByTypeWithEnvInfo 根据类型获取链接并包含环境信息
func (obj *MiddlewareLinkMgr) GetsByTypeWithEnvInfo(linkType string) (results []*po.MiddlewareLink, err error) {
	err = obj.DB.WithContext(obj.Ctx).Where("type = ?", linkType).Preload("EnvInfo").Find(&results).Error
	return
}

// Create 创建链接
func (obj *MiddlewareLinkMgr) Create(link *po.MiddlewareLink) error {
	return obj.DB.WithContext(obj.Ctx).Create(link).Error
}

// Update 更新链接
func (obj *MiddlewareLinkMgr) Update(link *po.MiddlewareLink) error {
	return obj.DB.WithContext(obj.Ctx).Where("id = ?", link.ID).Updates(link).Error
}

// Delete 删除链接
func (obj *MiddlewareLinkMgr) Delete(id int64) error {
	return obj.DB.WithContext(obj.Ctx).Where("id = ?", id).Delete(&po.MiddlewareLink{}).Error
}
