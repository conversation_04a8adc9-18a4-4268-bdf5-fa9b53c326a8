package models

import (
	"blops/app/models/po"
	. "blops/sql"
	"context"
	"time"

	"gorm.io/gorm"
)

type AlertMgr struct {
	*BaseMgr
}

// NewAlertMgr alert gorm
func NewAlertMgr() *AlertMgr {
	ctx, cancel := context.WithCancel(context.Background())
	return &AlertMgr{BaseMgr: &BaseMgr{DB: GDB.Table("t_alert"), IsRelated: GlobalIsRelated, Ctx: ctx, Cancel: cancel, Timeout: -1}}
}

// GetTableName get sql table name.获取数据库名字
func (obj *AlertMgr) GetTableName() string {
	return "t_alert"
}

// Reset 重置gorm会话
func (obj *AlertMgr) Reset() *AlertMgr {
	obj.New()
	return obj
}

// Set 插入
func (obj *AlertMgr) Set(alert *po.Alert) (err error) {
	err = obj.DB.WithContext(obj.Ctx).Model(po.Alert{}).Create(&alert).Error

	return
}

func (obj *AlertMgr) BatchInsert(alertList []*po.Alert) error {
	return obj.DB.WithContext(obj.Ctx).Model(po.Alert{}).CreateInBatches(&alertList, 10).Error
}

// Get 获取
func (obj *AlertMgr) Get(host string, status string, level string, alertId string, alertName string, cluster string, message string, page int, pageSize int, hours int) (result []*po.Alert, total int64, err error) {
	if pageSize > 1000 {
		pageSize = 1000 // 限制最大分页大小
	}
	if pageSize == 0 {
		pageSize = 20
	}

	// 当使用message参数时，固定限制为50条记录
	if message != "" {
		pageSize = 50
		page = 1 // 强制从第一页开始
	}

	offset := (page - 1) * pageSize

	// 创建查询条件
	query := obj.DB.WithContext(obj.Ctx).Model(&po.Alert{})

	// 如果指定了小时过滤，查询最近 hours 小时的记录
	if hours > 0 {
		cutoff := time.Now().Add(-time.Duration(hours) * time.Hour)
		query = query.Where("created_at >= ?", cutoff)
	}

	// 如果有message关键词搜索且没有其他精确过滤条件，则限制只搜索最近的记录
	hasFilterCondition := host != "" || status != "" || level != "" || alertId != "" || alertName != "" || cluster != ""

	// 非空字段筛选 - 优先使用精确匹配字段
	if host != "" {
		query = query.Where("hostname = ?", host)
	}
	if status != "" {
		query = query.Where("status = ?", status)
	}
	if level != "" {
		query = query.Where("level = ?", level)
	}
	if alertId != "" {
		query = query.Where("id = ?", alertId)
	}
	if alertName != "" {
		query = query.Where("alert_name = ?", alertName)
	}
	if cluster != "" {
		query = query.Where("cluster = ?", cluster)
	}

	// 对message使用LIKE查询，但采用优化策略
	if message != "" {
		likePattern := "%" + message + "%"

		if !hasFilterCondition {
			// 如果只有message搜索条件，限制只查询最近的记录
			subQuery := obj.DB.WithContext(obj.Ctx).Model(&po.Alert{}).
				Order("created_at DESC").
				Limit(2000) // 在最近2000条中搜索，提高找到相关内容的概率

			query = obj.DB.WithContext(obj.Ctx).Table("(?) as recent_alerts", subQuery).
				Where("message LIKE ?", likePattern)
		} else {
			// 有其他过滤条件时，正常应用模糊匹配
			query = query.Where("message LIKE ?", likePattern)
		}
	}

	// 先计算总记录数，确保分页一致性
	// 克隆查询用于计数
	countQuery := query.Session(&gorm.Session{})
	var count int64

	// 对于message查询，不执行精确计数
	if message != "" {
		count = 1000 // 给个固定值表示有多页
	} else {
		// 其他查询计算总数
		err = countQuery.Count(&count).Error
		if err != nil {
			// 如果计数查询失败，使用默认值
			count = 1000
		}
	}

	// 应用排序获取结果
	queryResult := query.Order("created_at DESC")

	// 如果未指定小时过滤，则再应用分页或message限制
	if hours == 0 {
		if message != "" {
			// 对message查询限制为50条
			queryResult = queryResult.Limit(50)
		} else {
			// 正常分页
			queryResult = queryResult.Offset(offset).Limit(pageSize)
		}
	}

	// 获取结果集
	err = queryResult.Find(&result).Error
	if err != nil {
		return nil, 0, err
	}

	// 设置总数
	// 确保总数始终一致，不随页数变化
	total = count

	// 如果结果为空但总数不为0，说明可能是页数超出了范围
	if len(result) == 0 && total > 0 {
		// 将总数调整为最后一页的偏移量
		total = ((total-1)/int64(pageSize) + 1) * int64(pageSize)
	}

	return
}

// Gets 获取批量结果
func (obj *AlertMgr) Gets() (results []*po.Alert, err error) {
	err = obj.DB.WithContext(obj.Ctx).Model(po.Alert{}).Find(&results).Error

	return
}
