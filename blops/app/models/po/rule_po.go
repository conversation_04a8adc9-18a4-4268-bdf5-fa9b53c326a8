package po

import (
	"time"
)

type Rule struct {
	ID int64 `gorm:"primaryKey;column:id" json:"-"` // 自增ID
	// 模版id
	TemplateId  int64     `json:"template_id" gorm:"column:template_id"`
	Types       string    `gorm:"column:types" json:"types"`
	AlertName   string    `gorm:"column:alert_name" json:"alert_name"` // 主机状态：0停用 1暂停 2正常
	Message     string    `gorm:"column:message" json:"message"`       // 告警信息
	Expr        string    `gorm:"column:expr" json:"expr"`
	Perch       string    `gorm:"column:perch" json:"perch"`
	Webhook     string    `gorm:"column:webhook" json:"webhook"`
	Period      string    `gorm:"column:period" json:"period"`
	Roles       string    `gorm:"column:roles" json:"roles"`
	Receiver    string    `gorm:"column:receiver" json:"receiver"`
	Cluster     string    `gorm:"column:cluster" json:"cluster"`
	Level       string    `gorm:"column:level" json:"level"`
	Labels      string    `gorm:"column:labels" json:"labels"` // Custom labels in JSON format
	GrafanaName string    `gorm:"column:grafana_name" json:"grafana_name"`
	GrafanaLink string    `gorm:"column:grafana_link" json:"grafana_link"`
	CreatedAt   time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt   time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName get sql table name.获取数据库表名
func (m *Rule) TableName() string {
	return "t_rule"
}
