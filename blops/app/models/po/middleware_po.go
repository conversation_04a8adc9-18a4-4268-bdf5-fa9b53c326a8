package po

import (
	"time"
)

// MiddlewareEnv 中间件环境表
type MiddlewareEnv struct {
	ID          int64     `gorm:"primaryKey;column:id" json:"id"`        // 自增ID
	Name        string    `gorm:"column:name" json:"name"`               // 环境标签，如阿里云、华为云等
	Description string    `gorm:"column:description" json:"description"` // 描述信息
	CreatedAt   time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt   time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 获取数据库表名
func (m *MiddlewareEnv) TableName() string {
	return "t_middleware_env"
}

// MiddlewareEnvColumns 获取数据库列名
var MiddlewareEnvColumns = struct {
	ID          string
	Name        string
	Description string
	CreatedAt   string
	UpdatedAt   string
}{
	ID:          "id",
	Name:        "name",
	Description: "description",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
}

// MiddlewareLink 中间件链接表
type MiddlewareLink struct {
	ID          int64          `gorm:"primaryKey;column:id" json:"id"`        // 自增ID
	Env         int64          `gorm:"column:env" json:"env"`                 // 所属环境ID
	Name        string         `gorm:"column:name" json:"name"`               // 链接名称
	Type        string         `gorm:"column:type" json:"type"`               // 具体属性类型
	URL         string         `gorm:"column:url" json:"url"`                 // 链接地址
	Description string         `gorm:"column:description" json:"description"` // 描述信息
	CreatedAt   time.Time      `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt   time.Time      `gorm:"column:updated_at" json:"updatedAt"`
	EnvInfo     *MiddlewareEnv `gorm:"foreignKey:Env;references:ID" json:"envInfo,omitempty"` // 环境信息，通过关联查询获取
}

// TableName 获取数据库表名
func (m *MiddlewareLink) TableName() string {
	return "t_middleware_link"
}

// MiddlewareLinkColumns 获取数据库列名
var MiddlewareLinkColumns = struct {
	ID          string
	Env         string
	Name        string
	Type        string
	URL         string
	Description string
	CreatedAt   string
	UpdatedAt   string
}{
	ID:          "id",
	Env:         "env",
	Name:        "name",
	Type:        "type",
	URL:         "url",
	Description: "description",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
}
