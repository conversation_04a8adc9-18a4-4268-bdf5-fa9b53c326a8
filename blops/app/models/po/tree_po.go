package po

type TreeD struct {
	Id    int     `json:"id"    gorm:"type:int(11);autoIncrement;primaryKey;column:id;"`
	Key   string  `json:"key"   gorm:"column:key"`
	Value string  `json:"value" gorm:"column:value"`
	Title string  `json:"title" gorm:"column:title"`
	Leaf  []*Leaf `json:"children" gorm:"foreignKey:TreeId"`
}

type Leaf struct {
	Id     int    `json:"id"       gorm:"type:int(11);autoIncrement;primaryKey;column:id;"`
	Key    string `json:"key"      gorm:"column:key"`
	Value  string `json:"value"    gorm:"column:value"`
	Title  string `json:"title"    gorm:"column:title"`
	TreeId int    `json:"tree_id"  gorm:"column:tree_id"`
}

func (m *TreeD) TableName() string {
	return "t_tree"
}

func (m *Leaf) TableName() string {
	return "t_leaf"
}
