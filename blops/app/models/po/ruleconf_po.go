package po

type TemplatePO struct {
	BasePO
	AlertName string `gorm:"column:alert_name" json:"alert_name"`
	AlertCode string `gorm:"column:alert_code" json:"alert_code"`
	Message   string `gorm:"column:message" json:"message"` // 告警信息
	Expr      string `gorm:"column:expr" json:"expr"`
	Perch     string `gorm:"column:perch" json:"perch"`
}

func (m *TemplatePO) TableName() string {
	return "t_expr"
}
