package po

import (
	"time"
)

type Alert struct {
	ID        int64     `gorm:"primaryKey;column:id" json:"-"` // 自增ID
	Finger    string    `gorm:"column:finger_print" json:"finger_print"`
	Level     string    `gorm:"column:level" json:"level"`
	Status    string    `gorm:"column:status" json:"status"`     // 主机状态：0停用 1暂停 2正常
	Message   string    `gorm:"column:message" json:"message"`   // 告警信息
	Instance  string    `gorm:"column:instance" json:"instance"` // 主机名
	Hostname  string    `gorm:"column:hostname" json:"hostname"` // 主机名
	Receiver  string    `gorm:"column:receiver" json:"receiver"`
	Alertname string    `gorm:"column:alert_name" json:"alert_name"` // 告警名
	Cluster   string    `gorm:"column:cluster" json:"cluster"`
	StartedAt string    `gorm:"column:started_at" json:"started_at"`
	EndedAt   string    `gorm:"column:ended_at" json:"ended_at"`
	CreatedAt time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName get sql table name.获取数据库表名
func (m *Alert) TableName() string {
	return "t_alert"
}

// Perch 占位符
type Perch struct {
	// 标识
	Key string `form:"key" json:"key"`
	// 说明/值
	Value string `form:"value" json:"value"`
}
