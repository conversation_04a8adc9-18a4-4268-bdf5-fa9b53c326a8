package po

import (
	"time"
)

// Host [...]
type Host struct {
	ID        int64     `gorm:"primaryKey;column:id" json:"-"` // 自增ID
	Name      string    `gorm:"column:name" json:"name"`       // 主机名
	Status    int8      `gorm:"column:status" json:"status"`   // 主机状态：0停用 1暂停 2正常
	Os        string    `gorm:"column:os" json:"os"`           // 操作系统
	CreatedAt time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName get sql table name.获取数据库表名
func (m *Host) TableName() string {
	return "t_host"
}

// HostColumns get sql column name.获取数据库列名
var HostColumns = struct {
	ID        string
	Name      string
	Status    string
	Os        string
	CreatedAt string
	UpdatedAt string
}{
	ID:        "id",
	Name:      "name",
	Status:    "status",
	Os:        "os",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
}

// HostGroup [...]
type HostGroup struct {
	ID        int64     `gorm:"primaryKey;column:id" json:"-"` // 自增ID
	Name      int64     `gorm:"column:name" json:"name"`       // 分组名称
	CreatedAt time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName get sql table name.获取数据库表名
func (m *HostGroup) TableName() string {
	return "t_host_group"
}

// HostGroupColumns get sql column name.获取数据库列名
var HostGroupColumns = struct {
	ID        string
	Name      string
	CreatedAt string
	UpdatedAt string
}{
	ID:        "id",
	Name:      "name",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
}

// HostGroupRelation [...]
type HostGroupRelation struct {
	ID        int64     `gorm:"primaryKey;column:id" json:"-"`  // 自增ID
	GroupID   int64     `gorm:"column:group_id" json:"groupId"` // 主机分组Id
	HostID    int64     `gorm:"column:host_id" json:"hostId"`   // 主机Id
	CreatedAt time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName get sql table name.获取数据库表名
func (m *HostGroupRelation) TableName() string {
	return "t_host_group_relation"
}

// HostGroupRelationColumns get sql column name.获取数据库列名
var HostGroupRelationColumns = struct {
	ID        string
	GroupID   string
	HostID    string
	CreatedAt string
	UpdatedAt string
}{
	ID:        "id",
	GroupID:   "group_id",
	HostID:    "host_id",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
}

// HostIP [...]
type HostIP struct {
	ID        int64     `gorm:"primaryKey;column:id" json:"-"` // 自增ID
	IP        int64     `gorm:"column:ip" json:"ip"`           // 主机ip
	HostID    int64     `gorm:"column:host_id" json:"hostId"`  // 主机id
	Type      int8      `gorm:"column:type" json:"type"`       // ip类型：0内网 1外网
	CreatedAt time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName get sql table name.获取数据库表名
func (m *HostIP) TableName() string {
	return "t_host_ip"
}

// HostIPColumns get sql column name.获取数据库列名
var HostIPColumns = struct {
	ID        string
	IP        string
	HostID    string
	Type      string
	CreatedAt string
	UpdatedAt string
}{
	ID:        "id",
	IP:        "ip",
	HostID:    "host_id",
	Type:      "type",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
}
