package models

import (
	"blops/app/models/po"
	. "blops/sql"
	"context"
)

type HostMgr struct {
	*BaseMgr
}

// NewHostMgr open func
func NewHostMgr() *HostMgr {
	ctx, cancel := context.WithCancel(context.Background())
	return &HostMgr{BaseMgr: &BaseMgr{DB: GDB.Table("t_host"), IsRelated: GlobalIsRelated, Ctx: ctx, Cancel: cancel, Timeout: -1}}
}

// GetTableName get sql table name.获取数据库名字
func (obj *HostMgr) GetTableName() string {
	return "t_host"
}

// Reset 重置gorm会话
func (obj *HostMgr) Reset() *HostMgr {
	obj.New()
	return obj
}

// Get 获取
func (obj *HostMgr) Get() (result po.Host, err error) {
	err = obj.DB.WithContext(obj.Ctx).Model(po.Host{}).Find(&result).Error

	return
}

// Gets 获取批量结果
func (obj *HostMgr) Gets() (results []*po.Host, err error) {
	err = obj.DB.WithContext(obj.Ctx).Model(po.Host{}).Find(&results).Error

	return
}

//
//// Count //////////////////////////////// gorm replace /////////////////////////////////
//func (obj *HostMgr) Count(count *int64) (tx *gorm.DB) {
//	return obj.DB.WithContext(obj.Ctx).Model(po.Host{}).Count(count)
//}
//
////////////////////////////////////////////////////////////////////////////////////
//
////////////////////////////option case ////////////////////////////////////////////
//
//// WithID id获取 自增ID
//func (obj *HostMgr) WithID(id int64) Option {
//	return OptionFunc(func(o *Options) { o.Query["id"] = id })
//}
//
//// WithName name获取 主机名
//func (obj *HostMgr) WithName(name int64) Option {
//	return OptionFunc(func(o *Options) { o.Query["name"] = name })
//}
//
//// WithStatus status获取 主机状态：0停用 1暂停 2正常
//func (obj *HostMgr) WithStatus(status int8) Option {
//	return OptionFunc(func(o *Options) { o.Query["status"] = status })
//}
//
////////////////////////////enume case ////////////////////////////////////////////
//
//// GetFromID 通过id获取内容 自增ID
//func (obj *HostMgr) GetFromID(id int64) (result po.Host, err error) {
//	err = obj.DB.WithContext(obj.Ctx).Model(po.Host{}).Where("`id` = ?", id).Find(&result).Error
//
//	return
//}
//
//// GetBatchFromID 批量查找 自增ID
//func (obj *HostMgr) GetBatchFromID(ids []int64) (results []*po.Host, err error) {
//	err = obj.DB.WithContext(obj.Ctx).Model(po.Host{}).Where("`id` IN (?)", ids).Find(&results).Error
//
//	return
//}
//
//// GetFromStatus 通过status获取内容 主机状态：0停用 1暂停 2正常
//func (obj *HostMgr) GetFromStatus(status int8) (results []*po.Host, err error) {
//	err = obj.DB.WithContext(obj.Ctx).Model(po.Host{}).Where("`status` = ?", status).Find(&results).Error
//
//	return
//}
//
//// GetBatchFromStatus 批量查找 主机状态：0停用 1暂停 2正常
//func (obj *HostMgr) GetBatchFromStatus(status []int8) (results []*po.Host, err error) {
//	err = obj.DB.WithContext(obj.Ctx).Model(po.Host{}).Where("`status` IN (?)", status).Find(&results).Error
//
//	return
//}
//
//// GetBatchFromUpdatedAt 批量查找
//func (obj *HostMgr) GetBatchFromUpdatedAt(updatedAts []time.Time) (results []*po.Host, err error) {
//	err = obj.DB.WithContext(obj.Ctx).Model(po.Host{}).Where("`updated_at` IN (?)", updatedAts).Find(&results).Error
//
//	return
//}
