package models

import (
	"blops/app/models/po"
	. "blops/sql"
	"context"
)

type TreeMgr struct {
	*BaseMgr
}

func NewTreeMgr() *TreeMgr {
	ctx, cancel := context.WithCancel(context.Background())
	return &TreeMgr{BaseMgr: &BaseMgr{DB: GDB.Table("t_tree"), IsRelated: GlobalIsRelated, Ctx: ctx, Cancel: cancel, Timeout: -1}}
}

func (obj *TreeMgr) GetTableName() string {
	return "t_tree"
}

// Reset 重置gorm会话
func (obj *TreeMgr) Reset() *TreeMgr {
	obj.New()
	return obj
}

// Set 插入
func (obj *TreeMgr) Set(tree *po.TreeD) (err error) {
	err = obj.DB.WithContext(obj.Ctx).Model(po.TreeD{}).Create(&tree).Error
	return
}

// Gets 获取批量结果
func (obj *TreeMgr) Gets() (results []*po.TreeD, err error) {
	err = obj.DB.WithContext(obj.Ctx).Model(po.TreeD{}).Preload("Leaf").Find(&results).Error
	return
}
