package models

import (
	"blops/app/models/po"
	. "blops/sql"
	"context"
	"gorm.io/gorm"
)

type AppAlertChatModel struct {
	*BaseMgr
}

func NewAppAlertChatModel() *AppAlertChatModel {
	ctx, cancel := context.WithCancel(context.Background())
	return &AppAlertChatModel{BaseMgr: &BaseMgr{DB: GDB.Table("t_alert_app_chat"), IsRelated: false, Ctx: ctx, Cancel: cancel, Timeout: -1}}
}

func (obj *AppAlertChatModel) Insert(chatPO *po.AppAlertChatPO) error {
	return obj.DB.WithContext(obj.Ctx).Model(po.AppAlertChatPO{}).Create(&chatPO).Error
}

func (obj *AppAlertChatModel) GetWithName(chatPO *po.AppAlertChatPO) (result *po.AppAlertChatPO, err error) {
	err = obj.DB.WithContext(obj.Ctx).Model(po.AppAlertChatPO{}).Where("`env` = ? and appid = ?", chatPO.Env, chatPO.AppId).First(&result).Error
	if err == gorm.ErrRecordNotFound {
		err = nil
	}
	return
}
