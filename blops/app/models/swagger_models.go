package models

// 通用响应结构
type Response struct {
	Code    int         `json:"code" example:"200"`
	Message string      `json:"message" example:"操作成功"`
	Data    interface{} `json:"data"`
}

// 集群信息
type ClusterInfo struct {
	ID   string `json:"id" example:"ali-test"`
	Name string `json:"name" example:"阿里云测试集群"`
}

// 命名空间列表响应
type NamespaceListResponse struct {
	Code    int      `json:"code" example:"200"`
	Message string   `json:"message" example:"获取命名空间列表成功"`
	Data    []string `json:"data" example:"default,kube-system,monitoring"`
}

// 事件信息
type EventInfo struct {
	Metadata struct {
		Name      string `json:"name"`
		Namespace string `json:"namespace"`
		UID       string `json:"uid"`
	} `json:"metadata"`
	Type    string `json:"type" example:"Warning"`
	Reason  string `json:"reason" example:"Failed"`
	Message string `json:"message" example:"Pod启动失败"`
	// 其他字段...
}

// 集群请求
type ClusterRequest struct {
	ClusterID string `json:"cluster_id" example:"ali-test"`
}

// 命名空间请求
type NamespaceRequest struct {
	ClusterID string `json:"cluster_id" example:"ali-test"`
	Namespace string `json:"namespace" example:"default"`
}

// 部署请求
type DeploymentRequest struct {
	ClusterID  string `json:"cluster_id" example:"ali-test"`
	Namespace  string `json:"namespace" example:"default"`
	Deployment string `json:"deployment" example:"nginx-deployment"`
}

// 资源请求
type ResourceRequest struct {
	ClusterID string `json:"cluster_id" example:"ali-test"`
	Namespace string `json:"namespace" example:"default"`
	Kind      string `json:"kind" example:"Pod"`
	Name      string `json:"name" example:"nginx-pod"`
}

// 创建资源请求
type CreateResourceRequest struct {
	ClusterID string `json:"cluster_id" example:"ali-test"`
	YAML      string `json:"yaml" example:"apiVersion: v1\nkind: Pod\nmetadata:\n  name: nginx\nspec:\n  containers:\n  - name: nginx\n    image: nginx:latest"`
}

// 更新资源请求
type UpdateResourceRequest struct {
	ClusterID string `json:"cluster_id" example:"ali-test"`
	YAML      string `json:"yaml" example:"apiVersion: v1\nkind: Pod\nmetadata:\n  name: nginx\nspec:\n  containers:\n  - name: nginx\n    image: nginx:1.19"`
}

// 删除资源请求
type DeleteResourceRequest struct {
	ClusterID string `json:"cluster_id" example:"ali-test"`
	Namespace string `json:"namespace" example:"default"`
	Kind      string `json:"kind" example:"Pod"`
	Name      string `json:"name" example:"nginx-pod"`
}

// 主机列表请求
type HostListRequest struct {
	Page     int    `json:"page" example:"1"`
	PageSize int    `json:"page_size" example:"10"`
	Search   string `json:"search" example:""`
}

// 登录请求
type LoginRequest struct {
	Username string `json:"username" example:"admin"`
	Password string `json:"password" example:"password"`
}

// 告警请求
type AlertRequest struct {
	Name        string `json:"name" example:"CPU使用率过高"`
	Description string `json:"description" example:"CPU使用率超过80%"`
	Level       string `json:"level" example:"warning"`
	Rule        string `json:"rule" example:"cpu_usage > 80"`
}

// 告警保存请求
type AlertSaveRequest struct {
	ID          string `json:"id" example:"1"`
	Name        string `json:"name" example:"CPU使用率过高"`
	Description string `json:"description" example:"CPU使用率超过80%"`
	Level       string `json:"level" example:"warning"`
	Rule        string `json:"rule" example:"cpu_usage > 80"`
}

// 告警处理请求
type AlertHandlerRequest struct {
	AlertID string `json:"alert_id" example:"1"`
	Action  string `json:"action" example:"resolve"`
	Comment string `json:"comment" example:"已处理"`
}

// 规则请求
type RuleRequest struct {
	Name        string `json:"name" example:"CPU规则"`
	Description string `json:"description" example:"CPU相关规则"`
	Rules       string `json:"rules" example:"cpu_usage > 80"`
}

// 规则更新请求
type RuleUpdateRequest struct {
	ID          string `json:"id" example:"1"`
	Name        string `json:"name" example:"CPU规则"`
	Description string `json:"description" example:"CPU相关规则"`
	Rules       string `json:"rules" example:"cpu_usage > 80"`
}

// 规则列表请求
type RuleListRequest struct {
	Page     int    `json:"page" example:"1"`
	PageSize int    `json:"page_size" example:"10"`
	Search   string `json:"search" example:""`
}

// 模板请求
type TemplateRequest struct {
	Name        string `json:"name" example:"告警模板"`
	Description string `json:"description" example:"告警通知模板"`
	Content     string `json:"content" example:"服务器 {{.hostname}} CPU使用率为 {{.cpu_usage}}%"`
}

// 模板更新请求
type TemplateUpdateRequest struct {
	ID          string `json:"id" example:"1"`
	Name        string `json:"name" example:"告警模板"`
	Description string `json:"description" example:"告警通知模板"`
	Content     string `json:"content" example:"服务器 {{.hostname}} CPU使用率为 {{.cpu_usage}}%"`
}

// 模板列表请求
type TemplateListRequest struct {
	Page     int    `json:"page" example:"1"`
	PageSize int    `json:"page_size" example:"10"`
	Search   string `json:"search" example:""`
}

// AI诊断请求
type AIDiagnosisRequest struct {
	Cluster   string        `json:"cluster" example:"ali-test"`
	Namespace string        `json:"namespace" example:"default"`
	Events    []interface{} `json:"events"`
}

// AI诊断响应
type AIDiagnosisResponse struct {
	Code    int    `json:"code" example:"200"`
	Message string `json:"message" example:"AI诊断成功"`
	Data    string `json:"data" example:"<h1>诊断结果</h1><p>根据分析，集群中存在以下问题...</p>"`
}
