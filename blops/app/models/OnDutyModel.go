package models

import (
	"blops/app/dtos"
	"blops/app/models/po"
	. "blops/sql"
	"context"
	"gorm.io/gorm"
)

type OnDutyMgr struct {
	*BaseMgr
}

func NewOnDutyMgr() *OnDutyMgr {
	ctx, cancel := context.WithCancel(context.Background())
	return &OnDutyMgr{BaseMgr: &BaseMgr{DB: GDB.Table("on_duty"), IsRelated: GlobalIsRelated, Ctx: ctx, Cancel: cancel, Timeout: -1}}
}

func (obj *OnDutyMgr) Insert(dutyPO *po.OnDutyPO) error {
	return obj.DB.WithContext(obj.Ctx).Model(po.OnDutyPO{}).Create(&dutyPO).Error
}

func (obj *OnDutyMgr) Get(userType string, userId int64) (ondutyPO *po.OnDutyPO, err error) {
	err = obj.DB.WithContext(obj.Ctx).Model(po.OnDutyPO{}).Where("user_type = ? and user_id = ?", userType, userId).First(&ondutyPO).Error
	if err == gorm.ErrRecordNotFound {
		err = nil
	}
	return
}

func (obj *OnDutyMgr) GetById(id int64) (ondutyPO *po.OnDutyPO, err error) {
	err = obj.DB.WithContext(obj.Ctx).Model(po.OnDutyPO{}).Where("id = ?", id).First(&ondutyPO).Error
	if err == gorm.ErrRecordNotFound {
		err = nil
	}
	return
}

func (obj *OnDutyMgr) Update(dutyPO *po.OnDutyPO) error {
	return obj.DB.WithContext(obj.Ctx).Model(po.OnDutyPO{}).Updates(&dutyPO).Error
}

func (obj *OnDutyMgr) List(onDutyListDTO *dtos.OnDutyListDTO) (results []*po.OnDutyPO, err error) {
	tx := obj.DB.WithContext(obj.Ctx).Model(po.OnDutyPO{}).Find(&results).Where("deleted_at = ?", 0)
	if onDutyListDTO.Status != "" {
		err = tx.Where("status = ?", onDutyListDTO.Status).Error
		return
	}
	err = tx.Error
	return
}
