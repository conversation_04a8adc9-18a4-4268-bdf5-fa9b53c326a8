package models

import (
	"blops/app/models/po"
	. "blops/sql"
	"context"
	"gorm.io/gorm"
)

type UserModel struct {
	*BaseMgr
}

func NewUserModel() *UserModel {
	ctx, cancel := context.WithCancel(context.Background())
	return &UserModel{BaseMgr: &BaseMgr{DB: GDB.Table("t_user"), IsRelated: false, Ctx: ctx, Cancel: cancel, Timeout: -1}}
}

// GetFromName 通过name获取用户信息 username
func (obj *UserModel) GetFromName(username string) (result *po.UserPO, err error) {
	err = obj.DB.WithContext(obj.Ctx).Model(po.UserPO{}).Where("`username` = ?", username).First(&result).Error
	if err == gorm.ErrRecordNotFound {
		err = nil
	}
	return
}

// GetFromEmail 通过email获取用户信息 email
func (obj *UserModel) GetFromEmail(email string) (result *po.UserPO, err error) {
	err = obj.DB.WithContext(obj.Ctx).Model(po.UserPO{}).Where("`email` = ?", email).First(&result).Error
	if err == gorm.ErrRecordNotFound {
		err = nil
	}
	return
}

func (obj *UserModel) List() (list []*po.UserPO, err error) {
	err = obj.DB.WithContext(obj.Ctx).Model(po.UserPO{}).Where("is_active = ?", 1).Find(&list).Error
	return
}

func (obj *UserModel) ListWithNoOpenId() (list []*po.UserPO, err error) {
	err = obj.DB.WithContext(obj.Ctx).Model(po.UserPO{}).Where("`openid` = ?", "").Find(&list).Error
	if err == gorm.ErrRecordNotFound || err == gorm.ErrEmptySlice {
		err = nil
	}
	return
}

func (obj *UserModel) Update(userPO *po.UserPO) error {
	var values = make(map[string]interface{}, 0)
	values["openid"] = userPO.OpenID
	values["is_active"] = userPO.IsActive
	return obj.DB.WithContext(obj.Ctx).Model(po.UserPO{}).Where("id = ?", userPO.ID).Updates(values).Error
}
