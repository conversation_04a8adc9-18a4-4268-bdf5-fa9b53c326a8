package job

import (
	"blops/app/models/po"
	feishu2 "blops/enums/feishu"
	"blops/logger"
	"blops/rest/feishu"
	"github.com/robfig/cron/v3"
)

func (j *Job) syncLarkUser() {
	userList, err := j.userSvc.ListNoOpenId()
	if err != nil {
		logger.Errorf("sync lark user failed: %v", err)
		return
	}
	if userList == nil {
		return
	}
	var userEmails []string
	var emailUserPOS []*po.UserPO
	for _, user := range userList {
		if user.Email != "" {
			userEmails = append(userEmails, user.Email)
			emailUserPOS = append(emailUserPOS, user)
		}
	}
	if userEmails != nil {
		_, err = feishu.GenAccessToken()
		if err != nil {
			logger.Error(err)
			return
		}
		larkUserIdList, err := feishu.GetUserIdByPhoneOrEmail(feishu2.OpenId, feishu.ReqGetUserIdDTO{
			Emails: userEmails,
		})
		if err != nil {
			logger.Errorf("get lark user error: %v", err)
			return
		}
		if larkUserIdList == nil {
			return
		}
		var updateUserPOS []*po.UserPO
		for _, larkUser := range larkUserIdList {
			if larkUser.UserId == "" {
				continue
			}
			for _, user := range emailUserPOS {
				if user.Email == larkUser.Email {
					user.OpenID = larkUser.UserId
					updateUserPOS = append(updateUserPOS, user)
					continue
				}
			}
		}
		if updateUserPOS != nil {
			err := j.userSvc.BulkUpdate(updateUserPOS)
			if err != nil {
				logger.Errorf("bulk update user openid error: %v", err)
				return
			}
		}
	}
}

func larkJobs() {
	c := cron.New(cron.WithSeconds())
	logger.Info("lark jobs starting...")
	_, _ = c.AddFunc("59 59 23 * * *", func() {
		logger.Info("lark user sync job running...")
		J.syncLarkUser()
	})
	c.Start()
}
