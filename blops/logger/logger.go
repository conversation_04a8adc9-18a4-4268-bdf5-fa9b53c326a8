// Package logger logrus适配
package logger

import (
	"log"
	"runtime"
	"strconv"
)

func init() {
	log.SetFlags(log.Ldate | log.Ltime)
}

func genShortFileWithLine() string {
	_, file, line, ok := runtime.Caller(2)
	if ok {
		short := file
		for i := len(file) - 1; i > 0; i-- {
			if file[i] == '/' {
				short = file[i+1:]
				break
			}
		}
		file = short
		return file + ":" + strconv.Itoa(line) + " "
	}
	return " "
}

func Info(v ...any) {
	log.SetPrefix("[INFO] ")
	log.Println(genShortFileWithLine(), v)
}

func Infof(format string, v ...any) {
	log.SetPrefix("[INFO] ")
	log.Printf(genShortFileWithLine()+format, v)
}

func Error(v ...any) {
	log.SetPrefix("[ERROR] ")
	log.Println(genShortFileWithLine(), v)
}

func Errorf(format string, v ...any) {
	log.SetPrefix("[ERROR] ")
	log.Printf(genShortFileWithLine()+format, v)
}
