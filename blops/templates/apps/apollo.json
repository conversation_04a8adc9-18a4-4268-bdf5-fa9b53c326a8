{"name": "Apollo", "description": "配置中心", "category": "网络", "icon": "", "version": "1.0.0", "maintainer": "<PERSON><PERSON><PERSON><PERSON>", "yamlTemplate": "apiVersion: apps/v1\nkind: Deployment\nmetadata:\n  name: {{ .name }}-deployment\n  labels:\n    app: {{ .name }}\nspec:\n  replicas: {{ .replicas }}\n  selector:\n    matchLabels:\n      app: {{ .name }}\n  template:\n    metadata:\n      labels:\n        app: {{ .name }}\n    spec:\n      containers:\n      - name: {{ .name }}\n        image: nginx\n        ports:\n        - name: http\n          containerPort: 80\n          protocol: TCP\n---\napiVersion: v1\nkind: Service\nmetadata:\n  name: {{ .name }}-service\n  labels:\n    app: {{ .name }}\nspec:\n  type: ClusterIP\n  selector:\n    app: {{ .name }}\n  ports:\n    - name: http\n      protocol: TCP\n      port: 80\n      targetPort: http\n---\napiVersion: v1\nkind: ConfigMap\nmetadata:\n  name: {{ .name }}-configmap\n  labels:\n    app: {{ .name }}\ndata:\n  key1: \"value1\"\n  key2: \"value2\"", "variables": [{"name": "name", "label": "", "description": "", "default_value": "apollo", "required": true, "type": "string"}, {"name": "replicas", "label": "", "description": "", "default_value": "1", "required": true, "type": "string"}]}