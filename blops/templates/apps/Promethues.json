{"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>", "category": "监控", "icon": "", "version": "1.0.0", "maintainer": "<PERSON><PERSON><PERSON><PERSON>", "yamlTemplate": "# Sample YAML Deployment Template\napiVersion: apps/v1\nkind: Deployment\nmetadata:\n  name: {{.name}}\nspec:\n  replicas: {{.replicas}}\n  selector:\n    matchLabels:\n      app: {{.name}}\n  template:\n    metadata:\n      labels:\n        app: {{.name}}\n    spec:\n      containers:\n      - name: {{.name}}\n        image: {{.image}}\n        ports:\n        - containerPort: {{.port}}", "variables": [{"name": "name", "label": "应用名", "description": "", "default_value": "", "required": false, "type": "string"}, {"name": "replicas", "label": "副本数", "description": "", "default_value": "", "required": false, "type": "string"}, {"name": "image", "label": "镜像", "description": "", "default_value": "", "required": false, "type": "string"}]}