package config

import (
	"log"

	"github.com/jinzhu/configor"
)

type Config struct {
	APPName string `default:"blops"`

	SERVER struct {
		Mode string `default:"debug" json:"mode"`
		Port string `default:"3000" json:"port"`
	}
	DB struct {
		Name     string `json:"name"`
		User     string `default:"root"`
		Password string `required:"true" env:"DBPassword"`
		Host     string `json:"host"`
		Port     string `default:"3306"`
	}
	Duty struct {
		Mode string `default:"auto" json:"mode"`
	}
	<PERSON>ert      <PERSON>ert        `json:"alert"`
	GitlabConf GitLabConfig `json:"gitlab_conf"`
}

var Conf Config

func init() {
	err := configor.Load(&Conf, "config.yml")
	if err != nil {
		log.Fatal(err)
	}
}
