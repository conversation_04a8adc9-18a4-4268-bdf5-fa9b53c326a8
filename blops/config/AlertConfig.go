package config

type LarkRobot struct {
	AppId     string `json:"app_id" yaml:"app_id"`
	AppSecret string `json:"app_secret" yaml:"app_secret"`
}

type LarkChat struct {
	Default string `json:"default" yaml:"default"`
}

// LarkMsgGroup 消息聚合配置
type LarkMsgGroup struct {
	// 聚合阈值
	Size int `json:"size" yaml:"size"`
}

// Lark 飞书配置
type Lark struct {
	Group   LarkMsgGroup `json:"group" yaml:"group"`
	BaseUrl string       `json:"base_url" yaml:"base_url"`
	Webhook string       `json:"webhook,omitempty" yaml:"webhook"`
	Robot   LarkRobot    `json:"robot" yaml:"robot"`
	Chat    LarkChat     `json:"chat" yaml:"chat"`
}

type Platform struct {
	Lark Lark `json:"lark" yaml:"lark"`
}

// Alert 告警配置
type Alert struct {
	Platform Platform `json:"platform" yaml:"platform"`
}
