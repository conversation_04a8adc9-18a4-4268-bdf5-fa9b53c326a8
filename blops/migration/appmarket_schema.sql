-- 删除已存在的表（如果需要）
DROP TABLE IF EXISTS app_resource;
DROP TABLE IF EXISTS deployed_app;

-- 创建 deployed_app 表
CREATE TABLE deployed_app (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    template VARCHAR(255) NOT NULL,
    cluster VARCHAR(255) NOT NULL,
    namespace VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建 app_resource 表
CREATE TABLE app_resource (
    id INT UNSIGNED NOT NULL AUTO_INCREMENT,
    app_id INT UNSIGNED NOT NULL,
    kind VARCHAR(50) NOT NULL,
    resource_name VARCHAR(255) NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    INDEX idx_app_id (app_id),
    CONSTRAINT fk_app_resource_app_id FOREIGN KEY (app_id) REFERENCES deployed_app(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4; 