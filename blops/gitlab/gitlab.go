package gitlab

import (
	"blops/config"
	"blops/logger"
	"github.com/xanzy/go-gitlab"
)

var (
	conf   config.GitLabConfig
	client *gitlab.Client
)

func init() {
	conf = config.Conf.GitlabConf
	c, err := gitlab.NewClient(conf.AccessToken, gitlab.WithBaseURL(conf.BaseURL+"/api/v4/"))
	if err != nil {
		logger.Errorf("Failed to create client: %v", err)
		panic(err)
	}
	client = c
}

// 获取所有groups
func listGroups(page, size int) ([]*gitlab.Group, error) {
	opts := &gitlab.ListGroupsOptions{}
	opts.Page = page
	opts.PerPage = size
	groups, _, err := client.Groups.ListGroups(opts)
	if err != nil {
		logger.Errorf("[gitlab] - list groups error:", err)
		return nil, err
	}
	//递归获取所有groups
	//curPage := r.Header.Get("x-page")
	//totalPage := r.Header.Get("x-total-pages")
	//if curPage < totalPage {
	//
	//}
	return groups, nil
}

// 获取所有projects
func listAllProjects(gid int, projects []*gitlab.Project) ([]*gitlab.Project, error) {
	opts := &gitlab.ListGroupProjectsOptions{}
	opts.Page = 1
	opts.PerPage = 50
	pList, r, err := client.Groups.ListGroupProjects(gid, opts)
	if err != nil {
		logger.Errorf("[gitlab] - list groups error:", err)
		return nil, err
	}
	projects = append(projects, pList...)
	//递归获取所有projects
	curPage := r.Header.Get("x-page")
	totalPage := r.Header.Get("x-total-pages")
	if curPage < totalPage {
		return listAllProjects(gid, projects)
	}
	return projects, nil
}
