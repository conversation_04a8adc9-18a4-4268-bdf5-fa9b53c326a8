package base

import "net/http"

// ApiRes 接口返回
type ApiRes struct {
	// 数据
	Data interface{} `json:"data"`
	// SUCCESS FAIL ERROR
	Result string `json:"result" default:"SUCCESS"`
	// 错误码
	Code int `json:"code" default:"200"`
	// 描述
	Message string `json:"message,omitempty"`
	// 额外信息
	Extra string `json:"extra,omitempty"`
	// 页码
	Page int `json:"page,omitempty"`
	// 每页大小
	Size int `json:"size,omitempty"`
	// 总数
	Total int `json:"total,omitempty"`
}

func (res *ApiRes) Error() (int, ApiRes) {
	return res.Code, *res
}

func (res *ApiRes) WithMsg(msg string) (int, ApiRes) {
	if msg != "" {
		res.Message = msg
	}
	return http.StatusOK, *res
}

func (res *ApiRes) WithExtra(extra string) (int, ApiRes) {
	if extra != "" {
		res.Extra = extra
	}
	return res.Code, *res
}

func (res *ApiRes) Success() (int, ApiRes) {
	return res.Code, *res
}
