package kube

import (
	"blops/logger"
	"context"
	"fmt"
	"io/ioutil"
	"path/filepath"
	"strings"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/runtime/serializer/yaml"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"
)

// 定义资源类型的GroupVersionResource
var deploymentGVR = schema.GroupVersionResource{
	Group:    "apps",
	Version:  "v1",
	Resource: "deployments",
}

var serviceGVR = schema.GroupVersionResource{
	Group:    "",
	Version:  "v1",
	Resource: "services",
}

var ingressGVR = schema.GroupVersionResource{
	Group:    "networking.k8s.io",
	Version:  "v1",
	Resource: "ingresses",
}

var namespaceGVR = schema.GroupVersionResource{
	Group:    "",
	Version:  "v1",
	Resource: "namespaces",
}

// 定义 Pod 资源的 GVR
var podGVR = schema.GroupVersionResource{
	Group:    "",
	Version:  "v1",
	Resource: "pods",
}

// ClusterInfo 集群信息
type ClusterInfo struct {
	Name string `json:"name"`
}

// ListClusters 列出所有可用的集群
func ListClusters() ([]ClusterInfo, error) {
	// 获取kube目录下的所有kubeconfig文件
	kubePath := "kube"
	files, err := ioutil.ReadDir(kubePath)
	if err != nil {
		logger.Errorf("read kube directory error: %v", err)
		return nil, err
	}

	var clusters []ClusterInfo
	for _, file := range files {
		if !file.IsDir() && file.Name() != "rule_crds.go" && file.Name() != "cluster.go" {
			clusters = append(clusters, ClusterInfo{Name: file.Name()})
		}
	}

	return clusters, nil
}

// GetKubeClient 获取Kubernetes客户端
func GetKubeClient(clusterName string) (*kubernetes.Clientset, error) {
	kubeconfig := filepath.Join("/Users/<USER>/kube", clusterName)
	config, err := clientcmd.BuildConfigFromFlags("", kubeconfig)
	if err != nil {
		logger.Errorf("build k8s config error: %v", err)
		return nil, err
	}

	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		logger.Errorf("build k8s client error: %v", err)
		return nil, err
	}

	return clientset, nil
}

// GetDynamicClient 获取Dynamic客户端
func GetDynamicClient(clusterName string) (dynamic.Interface, error) {
	kubeconfig := filepath.Join("/Users/<USER>/kube", clusterName)
	config, err := clientcmd.BuildConfigFromFlags("", kubeconfig)
	if err != nil {
		logger.Errorf("build k8s config error: %v", err)
		return nil, err
	}

	client, err := dynamic.NewForConfig(config)
	if err != nil {
		logger.Errorf("build k8s dynamic client error: %v", err)
		return nil, err
	}

	return client, nil
}

// ListNamespaces 列出集群中的命名空间
func ListNamespaces(clusterName string) ([]map[string]interface{}, error) {
	client, err := GetDynamicClient(clusterName)
	if err != nil {
		return nil, err
	}

	list, err := client.Resource(namespaceGVR).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		logger.Errorf("list namespaces error: %v", err)
		return nil, err
	}

	var namespaces []map[string]interface{}
	for _, item := range list.Items {
		namespace := map[string]interface{}{
			"name":              item.GetName(),
			"creationTimestamp": item.GetCreationTimestamp().String(),
			"status":            item.Object["status"],
		}
		namespaces = append(namespaces, namespace)
	}

	return namespaces, nil
}

// ListDeployments 列出命名空间中的Deployment
func ListDeployments(clusterName, namespace string) ([]map[string]interface{}, error) {
	client, err := GetDynamicClient(clusterName)
	if err != nil {
		return nil, err
	}

	list, err := client.Resource(deploymentGVR).Namespace(namespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		logger.Errorf("list deployments error: %v", err)
		return nil, err
	}

	var deployments []map[string]interface{}
	for _, item := range list.Items {
		deployment := map[string]interface{}{
			"name":              item.GetName(),
			"creationTimestamp": item.GetCreationTimestamp().String(),
			"replicas":          item.Object["spec"].(map[string]interface{})["replicas"],
			"status":            item.Object["status"],
			"metadata":          item.Object["metadata"],
			"spec":              item.Object["spec"],
		}
		deployments = append(deployments, deployment)
	}

	return deployments, nil
}

// ListServices 列出命名空间中的Service
func ListServices(clusterName, namespace string) ([]map[string]interface{}, error) {
	client, err := GetDynamicClient(clusterName)
	if err != nil {
		return nil, err
	}

	list, err := client.Resource(serviceGVR).Namespace(namespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		logger.Errorf("list services error: %v", err)
		return nil, err
	}

	var services []map[string]interface{}
	for _, item := range list.Items {
		service := map[string]interface{}{
			"name":              item.GetName(),
			"creationTimestamp": item.GetCreationTimestamp().String(),
			"type":              item.Object["spec"].(map[string]interface{})["type"],
			"clusterIP":         item.Object["spec"].(map[string]interface{})["clusterIP"],
			"ports":             item.Object["spec"].(map[string]interface{})["ports"],
		}
		services = append(services, service)
	}

	return services, nil
}

// ListIngresses 列出命名空间中的Ingress
func ListIngresses(clusterName, namespace string) ([]map[string]interface{}, error) {
	client, err := GetDynamicClient(clusterName)
	if err != nil {
		return nil, err
	}

	list, err := client.Resource(ingressGVR).Namespace(namespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		logger.Errorf("list ingresses error: %v", err)
		return nil, err
	}

	var ingresses []map[string]interface{}
	for _, item := range list.Items {
		ingress := map[string]interface{}{
			"name":              item.GetName(),
			"creationTimestamp": item.GetCreationTimestamp().String(),
			"rules":             item.Object["spec"].(map[string]interface{})["rules"],
			"tls":               item.Object["spec"].(map[string]interface{})["tls"],
		}
		ingresses = append(ingresses, ingress)
	}

	return ingresses, nil
}

// GetResource 获取资源详情
func GetResource(clusterName, namespace, resourceType, name string) (map[string]interface{}, error) {
	client, err := GetDynamicClient(clusterName)
	if err != nil {
		return nil, err
	}

	var gvr schema.GroupVersionResource
	switch resourceType {
	case "deployment", "deployments":
		gvr = deploymentGVR
	case "service", "services":
		gvr = serviceGVR
	case "ingress", "ingresses":
		gvr = ingressGVR
	case "pod", "pods":
		gvr = podGVR
	default:
		return nil, fmt.Errorf("unsupported resource type: %s", resourceType)
	}

	obj, err := client.Resource(gvr).Namespace(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	if err != nil {
		logger.Errorf("get %s %s error: %v", resourceType, name, err)
		return nil, err
	}

	return obj.Object, nil
}

// getKindFromResource 根据资源类型获取对应的Kind
func getKindFromResource(resource string) string {
	switch resource {
	case "deployments":
		return "Deployment"
	case "services":
		return "Service"
	case "ingresses":
		return "Ingress"
	case "pods":
		return "Pod"
	default:
		// 默认将首字母大写，并去掉复数形式的s
		if len(resource) > 0 && resource[len(resource)-1] == 's' {
			return strings.Title(resource[:len(resource)-1])
		}
		return strings.Title(resource)
	}
}

// CreateResource 创建资源
func CreateResource(clusterName, namespace, resourceType, yamlData string) (map[string]interface{}, error) {
	client, err := GetDynamicClient(clusterName)
	if err != nil {
		return nil, err
	}

	// 解析YAML
	decoder := yaml.NewDecodingSerializer(unstructured.UnstructuredJSONScheme)
	obj := &unstructured.Unstructured{}
	_, gvk, err := decoder.Decode([]byte(yamlData), nil, obj)
	if err != nil {
		logger.Errorf("decode yaml error: %v", err)
		return nil, err
	}

	// 根据资源类型获取GVR
	var gvr schema.GroupVersionResource
	switch resourceType {
	case "deployment", "deployments":
		gvr = deploymentGVR
	case "service", "services":
		gvr = serviceGVR
	case "ingress", "ingresses":
		gvr = ingressGVR
	case "pod", "pods":
		gvr = podGVR
	default:
		return nil, fmt.Errorf("unsupported resource type: %s", resourceType)
	}

	// 检查GVK是否匹配
	if gvk.Group != gvr.Group || gvk.Kind != getKindFromResource(gvr.Resource) {
		return nil, fmt.Errorf("resource type mismatch: expected %s, got %s", resourceType, gvk.Kind)
	}

	// 创建资源
	result, err := client.Resource(gvr).Namespace(namespace).Create(context.TODO(), obj, metav1.CreateOptions{})
	if err != nil {
		logger.Errorf("create %s error: %v", resourceType, err)
		return nil, err
	}

	return result.Object, nil
}

// UpdateResource 更新资源
func UpdateResource(clusterName, namespace, resourceType, name, yamlData string) (map[string]interface{}, error) {
	client, err := GetDynamicClient(clusterName)
	if err != nil {
		return nil, err
	}

	// 解析YAML
	decoder := yaml.NewDecodingSerializer(unstructured.UnstructuredJSONScheme)
	obj := &unstructured.Unstructured{}
	_, gvk, err := decoder.Decode([]byte(yamlData), nil, obj)
	if err != nil {
		logger.Errorf("decode yaml error: %v", err)
		return nil, err
	}

	// 根据资源类型获取GVR
	var gvr schema.GroupVersionResource
	switch resourceType {
	case "deployment", "deployments":
		gvr = deploymentGVR
	case "service", "services":
		gvr = serviceGVR
	case "ingress", "ingresses":
		gvr = ingressGVR
	case "pod", "pods":
		gvr = podGVR
	default:
		return nil, fmt.Errorf("unsupported resource type: %s", resourceType)
	}

	// 检查GVK是否匹配
	if gvk.Group != gvr.Group || gvk.Kind != getKindFromResource(gvr.Resource) {
		return nil, fmt.Errorf("resource type mismatch: expected %s, got %s", resourceType, gvk.Kind)
	}

	// 检查资源名称是否匹配
	if obj.GetName() != name {
		return nil, fmt.Errorf("resource name mismatch: expected %s, got %s", name, obj.GetName())
	}

	// 获取当前资源的ResourceVersion
	current, err := client.Resource(gvr).Namespace(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	if err != nil {
		logger.Errorf("get current %s %s error: %v", resourceType, name, err)
		return nil, err
	}

	// 设置ResourceVersion以确保更新正确
	obj.SetResourceVersion(current.GetResourceVersion())

	// 更新资源
	result, err := client.Resource(gvr).Namespace(namespace).Update(context.TODO(), obj, metav1.UpdateOptions{})
	if err != nil {
		logger.Errorf("update %s %s error: %v", resourceType, name, err)
		return nil, err
	}

	return result.Object, nil
}

// DeleteResource 删除资源
func DeleteResource(clusterName, namespace, resourceType, name string) error {
	client, err := GetDynamicClient(clusterName)
	if err != nil {
		return err
	}

	var gvr schema.GroupVersionResource
	switch resourceType {
	case "deployment", "deployments":
		gvr = deploymentGVR
	case "service", "services":
		gvr = serviceGVR
	case "ingress", "ingresses":
		gvr = ingressGVR
	case "pod", "pods":
		gvr = podGVR
	default:
		return fmt.Errorf("unsupported resource type: %s", resourceType)
	}

	err = client.Resource(gvr).Namespace(namespace).Delete(context.TODO(), name, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf("delete %s %s error: %v", resourceType, name, err)
		return err
	}

	return nil
}

// ListPodsByDeployment 获取指定 Deployment 下的所有 Pod
func ListPodsByDeployment(clusterName, namespace, deploymentName string) ([]map[string]interface{}, error) {
	client, err := GetDynamicClient(clusterName)
	if err != nil {
		return nil, err
	}

	// 获取 Deployment 详情，以获取标签选择器
	deployment, err := client.Resource(deploymentGVR).Namespace(namespace).Get(context.TODO(), deploymentName, metav1.GetOptions{})
	if err != nil {
		logger.Errorf("get deployment error: %v", err)
		return nil, err
	}

	// 从 Deployment 中提取标签选择器
	spec, ok := deployment.Object["spec"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid deployment spec")
	}

	selector, ok := spec["selector"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid deployment selector")
	}

	matchLabels, ok := selector["matchLabels"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid deployment matchLabels")
	}

	// 构建标签选择器字符串
	labelSelector := ""
	for k, v := range matchLabels {
		if labelSelector != "" {
			labelSelector += ","
		}
		labelSelector += fmt.Sprintf("%s=%s", k, v)
	}

	// 获取 Pod 列表
	list, err := client.Resource(podGVR).Namespace(namespace).List(context.TODO(), metav1.ListOptions{
		LabelSelector: labelSelector,
	})
	if err != nil {
		logger.Errorf("list pods error: %v", err)
		return nil, err
	}

	var pods []map[string]interface{}
	for _, item := range list.Items {
		status, ok := item.Object["status"].(map[string]interface{})
		if !ok {
			continue
		}

		pod := map[string]interface{}{
			"name":              item.GetName(),
			"creationTimestamp": item.GetCreationTimestamp().String(),
			"status":            getPodStatus(status),
			"podIP":             status["podIP"],
			"hostIP":            status["hostIP"],
			"restarts":          getPodRestarts(status),
			"node":              status["nodeName"],
		}
		pods = append(pods, pod)
	}

	return pods, nil
}

// 获取 Pod 状态
func getPodStatus(status map[string]interface{}) string {
	phase, ok := status["phase"].(string)
	if !ok {
		return "Unknown"
	}
	return phase
}

// 获取 Pod 重启次数
func getPodRestarts(status map[string]interface{}) int {
	containerStatuses, ok := status["containerStatuses"].([]interface{})
	if !ok || len(containerStatuses) == 0 {
		return 0
	}

	restarts := 0
	for _, cs := range containerStatuses {
		containerStatus, ok := cs.(map[string]interface{})
		if !ok {
			continue
		}

		if r, ok := containerStatus["restartCount"].(float64); ok {
			restarts += int(r)
		}
	}

	return restarts
}
