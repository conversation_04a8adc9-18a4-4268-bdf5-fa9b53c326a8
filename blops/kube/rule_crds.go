package kube

import (
	"blops/logger"
	"blops/tmpl"
	"blops/tmpl/k8s"
	"context"
	"encoding/json"
	"fmt"
	"path/filepath"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/runtime/serializer/yaml"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/tools/clientcmd"
)

var gvr = schema.GroupVersionResource{
	Group:    "monitoring.coreos.com",
	Version:  "v1",
	Resource: "prometheusrules",
}

var gvk = schema.GroupVersionKind{
	Group:   "monitoring.coreos.com",
	Version: "v1",
	Kind:    "prometheusrules",
}

var cgvr = schema.GroupVersionResource{
	Group:    "",
	Version:  "v1",
	Resource: "configmaps",
}

var cgvk = schema.GroupVersionKind{
	Group:   "",
	Version: "v1",
	Kind:    "configmaps",
}

type Annotations struct {
	Message string `json:"message"`
}

type Labels struct {
	Level    string `json:"level"`
	Receiver string `json:"receiver"`
	Severity string `json:"severity"`
	User     string `json:"user"`
}

type Rules struct {
	Alert       string      `json:"alert"`
	Annotations Annotations `json:"annotations"`
	Expr        string      `json:"expr"`
	For         string      `json:"for"`
	Labels      Labels      `json:"labels"`
}

type Groups struct {
	Name  string  `json:"name"`
	Rules []Rules `json:"rules"`
}

type Spec struct {
	Groups []Groups `json:"groups"`
}

type Rule struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec Spec `json:"spec,omitempty"`
}

type RuleCO struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`

	Items []Rule `json:"items"`
}

func listRules(client dynamic.Interface, namespace string) (*RuleCO, error) {
	list, err := client.Resource(gvr).Namespace(namespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		return nil, err
	}
	data, err := list.MarshalJSON()
	if err != nil {
		return nil, err
	}
	var ctList RuleCO
	if err := json.Unmarshal(data, &ctList); err != nil {
		return nil, err
	}
	return &ctList, nil
}

func createRuleWithYaml(client dynamic.Interface, namespace string, yamlData string) (*Rule, error) {
	decoder := yaml.NewDecodingSerializer(unstructured.UnstructuredJSONScheme)
	obj := &unstructured.Unstructured{}

	if _, _, err := decoder.Decode([]byte(yamlData), &gvk, obj); err != nil {
		return nil, err
	}

	utd, err := client.Resource(gvr).Namespace(namespace).Create(context.TODO(), obj, metav1.CreateOptions{})
	if err != nil {
		return nil, err
	}
	data, err := utd.MarshalJSON()
	if err != nil {
		return nil, err
	}
	var ct Rule
	if err := json.Unmarshal(data, &ct); err != nil {
		return nil, err
	}
	return &ct, nil
}

func updateRuleWithYaml(client dynamic.Interface, namespace string, yamlData string) (*Rule, error) {
	decoder := yaml.NewDecodingSerializer(unstructured.UnstructuredJSONScheme)
	obj := &unstructured.Unstructured{}
	if _, _, err := decoder.Decode([]byte(yamlData), &gvk, obj); err != nil {
		return nil, err
	}

	utd, err := client.Resource(gvr).Namespace(namespace).Get(context.TODO(), obj.GetName(), metav1.GetOptions{})
	if err != nil {
		return nil, err
	}
	obj.SetResourceVersion(utd.GetResourceVersion())
	utd, err = client.Resource(gvr).Namespace(namespace).Update(context.TODO(), obj, metav1.UpdateOptions{})
	if err != nil {
		return nil, err
	}

	data, err := utd.MarshalJSON()
	if err != nil {
		return nil, err
	}
	var ct Rule
	if err := json.Unmarshal(data, &ct); err != nil {
		return nil, err
	}
	return &ct, nil
}

func updateConfigmapWithYaml(client dynamic.Interface, namespace string, yamlData string) (*Rule, error) {
	decoder := yaml.NewDecodingSerializer(unstructured.UnstructuredJSONScheme)
	obj := &unstructured.Unstructured{}
	if _, _, err := decoder.Decode([]byte(yamlData), &cgvk, obj); err != nil {
		return nil, err
	}

	utd, err := client.Resource(cgvr).Namespace(namespace).Get(context.TODO(), obj.GetName(), metav1.GetOptions{})
	if err != nil {
		return nil, err
	}
	obj.SetResourceVersion(utd.GetResourceVersion())
	utd, err = client.Resource(cgvr).Namespace(namespace).Update(context.TODO(), obj, metav1.UpdateOptions{})
	if err != nil {
		return nil, err
	}

	data, err := utd.MarshalJSON()
	if err != nil {
		return nil, err
	}
	var ct Rule
	if err := json.Unmarshal(data, &ct); err != nil {
		return nil, err
	}
	return &ct, nil
}

func SetRule(pAlertRuleDO *k8s.PrometheusAlertRuleDO, method string) (ret string, err error) {
	kubeconfig := filepath.Join("/Users/<USER>/kube", pAlertRuleDO.Cluster)
	config, err := clientcmd.BuildConfigFromFlags("", kubeconfig)
	if err != nil {
		logger.Errorf("build k8s config error: %v", err)
		return "", err
	}
	client, err := dynamic.NewForConfig(config)
	if err != nil {
		logger.Errorf("build k8s client error: %v", err)
		return "", err
	}

	createData, err := tmpl.TemplateHandlerStrWithFiles[k8s.PrometheusAlertRuleDO]("prometheus_rule.tmpl", pAlertRuleDO)
	if err != nil {
		logger.Errorf("get prometheus alert rule error: %v", err)
		return "", err
	}

	logger.Infof("get rule data: %s, from tmpl", createData)

	var r *Rule
	if method == "update" {
		r, err = updateRuleWithYaml(client, pAlertRuleDO.Namespace, createData)
	} else {
		r, err = createRuleWithYaml(client, pAlertRuleDO.Namespace, createData)
	}
	if err != nil {
		logger.Errorf("update or create rule error: %v", err)
		return "", err
	}
	logger.Info("update or create rule success")
	return fmt.Sprintf("%s %s", r.Namespace, r.Name), nil
	//return "", nil
}

func SetConfigmap(pAlertRuleDO *k8s.PrometheusAlertRuleDO, method string) (ret string, err error) {
	//kubeconfig := filepath.Join("kube", pAlertRuleDO.Cluster)
	//config, err := clientcmd.BuildConfigFromFlags("", kubeconfig)
	//if err != nil {
	//	logger.Errorf("build k8s config error: %v", err)
	//	return "", err
	//}
	//client, err := dynamic.NewForConfig(config)
	//if err != nil {
	//	logger.Errorf("build k8s client error: %v", err)
	//	return "", err
	//}

	tmp, err := tmpl.TemplateHandlerStrWithFiles[k8s.PrometheusAlertRuleDO]("loki_config_map.tmpl", pAlertRuleDO)
	if err != nil {
		logger.Errorf("get loki config map error: %v", err)
		return "", err
	}
	logger.Infof("get loki config map tmpl str: %s", tmp)
	//if method == "update" {
	//	ct, err := updateConfigmapWithYaml(client, pAlertRuleDO.Namespace, tmp)
	//	if err != nil {
	//		logger.Errorf("update config map yaml err: %v", err)
	//		return "", err
	//	}
	//	logger.Infof("%s\n", ct.Name)
	//	return ct.Name, nil
	//} else {
	//	logger.Info("not update configmap")
	//	return "none", nil
	//}
	return "", nil
}
