package kube

import (
	"bytes"
	"fmt"

	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/serializer/yaml"
	yamlutil "k8s.io/apimachinery/pkg/util/yaml"
)

// ParseYamlToUnstructured 将YAML字符串解析为Unstructured对象
func ParseYamlToUnstructured(yamlData string) (*unstructured.Unstructured, error) {
	// 将YAML字符串转换为JSON
	decoder := yamlutil.NewYAMLOrJSONDecoder(bytes.NewReader([]byte(yamlData)), 100)
	var rawObj runtime.RawExtension
	if err := decoder.Decode(&rawObj); err != nil {
		return nil, fmt.Errorf("解析YAML失败: %v", err)
	}

	// 将JSON转换为Unstructured对象
	obj, _, err := yaml.NewDecodingSerializer(unstructured.UnstructuredJSONScheme).Decode(rawObj.Raw, nil, nil)
	if err != nil {
		return nil, fmt.Errorf("转换为Unstructured对象失败: %v", err)
	}

	// 类型断言为Unstructured对象
	unstructuredObj, ok := obj.(*unstructured.Unstructured)
	if !ok {
		return nil, fmt.Errorf("无法将对象转换为Unstructured类型")
	}

	return unstructuredObj, nil
}
