package kube

import (
	"blops/logger"
	"bytes"
	"fmt"
	"net/http"
	"path/filepath"

	"github.com/gorilla/websocket"
	_ "github.com/moby/spdystream"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	_ "k8s.io/apimachinery/pkg/util/httpstream/spdy"
	"k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/tools/remotecommand"

	// 新增：引入 Kubernetes Clientset
	"k8s.io/client-go/kubernetes"
)

// 定义 WebSocket 升级器
var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true // 允许所有来源的 WebSocket 连接
	},
}

// TerminalSession 表示一个终端会话
type TerminalSession struct {
	wsConn   *websocket.Conn
	sizeChan chan remotecommand.TerminalSize
	doneChan chan struct{}
}

// 实现 remotecommand.TerminalSizeQueue 接口
func (t *TerminalSession) Next() *remotecommand.TerminalSize {
	select {
	case size := <-t.sizeChan:
		return &size
	case <-t.doneChan:
		return nil
	}
}

// 实现 io.Reader 接口
func (t *TerminalSession) Read(p []byte) (int, error) {
	_, message, err := t.wsConn.ReadMessage()
	if err != nil {
		logger.Errorf("read message error: %v", err)
		return 0, err
	}

	// 处理终端大小调整消息
	if len(message) >= 2 && message[0] == 1 {
		width := uint16(message[1]) + uint16(message[2])<<8
		height := uint16(message[3]) + uint16(message[4])<<8
		t.sizeChan <- remotecommand.TerminalSize{Width: width, Height: height}
		return 0, nil
	}

	// 处理普通输入
	copy(p, message)
	return len(message), nil
}

// 实现 io.Writer 接口
func (t *TerminalSession) Write(p []byte) (int, error) {
	err := t.wsConn.WriteMessage(websocket.TextMessage, p)
	if err != nil {
		logger.Errorf("write message error: %v", err)
		return 0, err
	}
	return len(p), nil
}

// 关闭会话
func (t *TerminalSession) Close() error {
	close(t.doneChan)
	return t.wsConn.Close()
}

// HandlePodExec 处理 Pod 终端执行请求
func HandlePodExec(w http.ResponseWriter, r *http.Request, clusterName, namespace, podName, containerName string) {
	// 记录请求信息
	logger.Infof("收到 Pod 终端请求: cluster=%s, namespace=%s, pod=%s, container=%s", clusterName, namespace, podName, containerName)

	// 升级 HTTP 连接为 WebSocket
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		logger.Errorf("升级 WebSocket 连接失败: %v", err)
		return
	}

	logger.Infof("WebSocket 连接已建立: %s", r.RemoteAddr)

	// 创建终端会话
	session := &TerminalSession{
		wsConn:   conn,
		sizeChan: make(chan remotecommand.TerminalSize),
		doneChan: make(chan struct{}),
	}
	defer session.Close()

	// 获取 Kubernetes 客户端
	client, err := GetKubeClient(clusterName)
	if err != nil {
		logger.Errorf("获取 Kubernetes 客户端失败: %v", err)
		session.Write([]byte(fmt.Sprintf("获取集群客户端失败: %v\n", err)))
		return
	}

	logger.Infof("已获取 Kubernetes 客户端: %s", clusterName)

	// 如果没有指定容器名称，则获取 Pod 的第一个容器
	if containerName == "" {
		logger.Infof("未指定容器名称，尝试获取 Pod 的第一个容器")
		pod, err := client.CoreV1().Pods(namespace).Get(r.Context(), podName, metav1.GetOptions{})
		if err != nil {
			logger.Errorf("获取 Pod 信息失败: %v", err)
			session.Write([]byte(fmt.Sprintf("获取 Pod 信息失败: %v\n", err)))
			return
		}
		if len(pod.Spec.Containers) > 0 {
			containerName = pod.Spec.Containers[0].Name
			logger.Infof("使用 Pod 的第一个容器: %s", containerName)
		} else {
			logger.Errorf("Pod 中没有可用的容器")
			session.Write([]byte("Pod 中没有可用的容器\n"))
			return
		}
	}

	// 构建 Exec 请求
	req := client.CoreV1().RESTClient().Post().
		Resource("pods").
		Name(podName).
		Namespace(namespace).
		SubResource("exec").
		VersionedParams(&v1.PodExecOptions{
			Container: containerName,
			Command:   []string{"/bin/sh", "-c", "[ -x /bin/bash ] && exec /bin/bash || exec /bin/sh"},
			Stdin:     true,
			Stdout:    true,
			Stderr:    true,
			TTY:       true,
		}, scheme.ParameterCodec)

	logger.Infof("已构建 Exec 请求: %s", req.URL().String())

	// 创建 SPDY 执行器
	exec, err := remotecommand.NewSPDYExecutor(GetKubeConfig(clusterName), "POST", req.URL())
	if err != nil {
		logger.Errorf("创建 SPDY 执行器失败: %v", err)
		session.Write([]byte(fmt.Sprintf("创建终端执行器失败: %v\n", err)))
		return
	}

	logger.Infof("已创建 SPDY 执行器，开始执行命令")

	// 开始执行
	err = exec.Stream(remotecommand.StreamOptions{
		Stdin:             session,
		Stdout:            session,
		Stderr:            session,
		Tty:               true,
		TerminalSizeQueue: session,
	})

	if err != nil {
		logger.Errorf("执行命令失败: %v", err)
		session.Write([]byte(fmt.Sprintf("执行命令失败: %v\n", err)))
		return
	}

	logger.Infof("Pod 终端会话结束: %s/%s/%s", clusterName, namespace, podName)
}

// GetKubeConfig 获取 Kubernetes 配置
func GetKubeConfig(clusterName string) *rest.Config {
	kubeconfig := filepath.Join("/Users/<USER>/kube", clusterName)
	config, err := clientcmd.BuildConfigFromFlags("", kubeconfig)
	if err != nil {
		logger.Errorf("build k8s config error: %v", err)
		return nil
	}
	return config
}

// 新增：获取 Kubernetes Clientset
func GetClientset(clusterName string) (*kubernetes.Clientset, error) {
	config := GetKubeConfig(clusterName)
	if config == nil {
		return nil, fmt.Errorf("failed to get kubeconfig for cluster %s", clusterName)
	}
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create clientset: %w", err)
	}
	return clientset, nil
}

// ExecInPod 在 Pod 中执行命令
func ExecInPod(clusterName, namespace, podName string, command []string) (string, error) {
	// 获取集群客户端
	clientset, err := GetClientset(clusterName)
	if err != nil {
		return "", fmt.Errorf("failed to get clientset: %v", err)
	}

	// 创建执行请求
	req := clientset.CoreV1().RESTClient().Post().
		Resource("pods").
		Name(podName).
		Namespace(namespace).
		SubResource("exec")

	req.VersionedParams(&v1.PodExecOptions{
		Command: command,
		Stdin:   false,
		Stdout:  true,
		Stderr:  true,
		TTY:     false,
	}, scheme.ParameterCodec)

	// 创建执行器
	exec, err := remotecommand.NewSPDYExecutor(GetKubeConfig(clusterName), "POST", req.URL())
	if err != nil {
		return "", fmt.Errorf("failed to create executor: %v", err)
	}

	// 创建输出缓冲区
	var stdout, stderr bytes.Buffer

	// 执行命令
	err = exec.Stream(remotecommand.StreamOptions{
		Stdout: &stdout,
		Stderr: &stderr,
	})
	if err != nil {
		return "", fmt.Errorf("failed to execute command: %v, stderr: %s", err, stderr.String())
	}

	// 如果有错误输出，将其包含在返回结果中
	if stderr.Len() > 0 {
		return fmt.Sprintf("stdout:\n%s\nstderr:\n%s", stdout.String(), stderr.String()), nil
	}

	return stdout.String(), nil
}
