package kube

import (
	"blops/logger"
	"bytes"
	"context"
	"fmt"
	"io"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
)

// 获取集群支持的CronJob API版本
func getCronJobGVR(clusterName string) (schema.GroupVersionResource, error) {
	// 默认使用 batch/v1
	defaultGVR := schema.GroupVersionResource{
		Group:    "batch",
		Version:  "v1",
		Resource: "cronjobs",
	}

	// 获取API资源列表
	client, err := GetClientset(clusterName)
	if err != nil {
		return defaultGVR, err
	}

	// 检查API资源
	apiResourceLists, err := client.Discovery().ServerPreferredResources()
	if err != nil {
		logger.Error(fmt.Sprintf("获取API资源列表失败: %v, 将使用默认版本 batch/v1", err))
		return defaultGVR, nil
	}

	// 查找支持的CronJob版本
	for _, apiResourceList := range apiResourceLists {
		if apiResourceList == nil {
			continue
		}

		gv, err := schema.ParseGroupVersion(apiResourceList.GroupVersion)
		if err != nil {
			continue
		}

		if gv.Group == "batch" {
			for _, resource := range apiResourceList.APIResources {
				if resource.Name == "cronjobs" {
					logger.Infof("找到CronJob API版本: %s", apiResourceList.GroupVersion)
					return schema.GroupVersionResource{
						Group:    gv.Group,
						Version:  gv.Version,
						Resource: "cronjobs",
					}, nil
				}
			}
		}
	}

	// 如果找不到，先尝试 batch/v1beta1
	betaGVR := schema.GroupVersionResource{
		Group:    "batch",
		Version:  "v1beta1",
		Resource: "cronjobs",
	}

	// 测试 beta 版本是否可用
	dynamicClient, err := GetDynamicClient(clusterName)
	if err != nil {
		return defaultGVR, nil
	}

	_, err = dynamicClient.Resource(betaGVR).Namespace("default").List(context.TODO(), metav1.ListOptions{Limit: 1})
	if err == nil {
		logger.Infof("使用CronJob API版本: batch/v1beta1")
		return betaGVR, nil
	}

	// 如果 beta 版本不可用，尝试 v1 版本
	v1GVR := schema.GroupVersionResource{
		Group:    "batch",
		Version:  "v1",
		Resource: "cronjobs",
	}

	_, err = dynamicClient.Resource(v1GVR).Namespace("default").List(context.TODO(), metav1.ListOptions{Limit: 1})
	if err == nil {
		logger.Infof("使用CronJob API版本: batch/v1")
		return v1GVR, nil
	}

	logger.Infof("未找到支持的CronJob API版本，将使用默认版本 batch/v1")
	return defaultGVR, nil
}

// ListCronJobs 列出命名空间中的CronJob
func ListCronJobs(clusterName, namespace string) ([]map[string]interface{}, error) {
	client, err := GetDynamicClient(clusterName)
	if err != nil {
		return nil, err
	}

	// 获取适用的API版本
	cronJobGVR, err := getCronJobGVR(clusterName)
	if err != nil {
		logger.Error(fmt.Sprintf("获取CronJob API版本失败: %v, 将使用默认版本", err))
	}

	list, err := client.Resource(cronJobGVR).Namespace(namespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		logger.Errorf("list cronjobs error: %v", err)
		return nil, err
	}

	var cronJobs []map[string]interface{}
	for _, item := range list.Items {
		cronJob := map[string]interface{}{
			"name":              item.GetName(),
			"creationTimestamp": item.GetCreationTimestamp().String(),
			"schedule":          getSchedule(item),
			"suspend":           getSuspend(item),
			"active":            getActiveJobs(item),
			"lastSchedule":      getLastScheduleTime(item),
			"metadata":          item.Object["metadata"],
			"spec":              item.Object["spec"],
			"status":            item.Object["status"],
		}
		cronJobs = append(cronJobs, cronJob)
	}

	return cronJobs, nil
}

// GetCronJob 获取指定的CronJob
func GetCronJob(clusterName, namespace, name string) (map[string]interface{}, error) {
	client, err := GetDynamicClient(clusterName)
	if err != nil {
		return nil, err
	}

	// 获取适用的API版本
	cronJobGVR, err := getCronJobGVR(clusterName)
	if err != nil {
		logger.Error(fmt.Sprintf("获取CronJob API版本失败: %v, 将使用默认版本", err))
	}

	cronJob, err := client.Resource(cronJobGVR).Namespace(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	if err != nil {
		logger.Errorf("get cronjob error: %v", err)
		return nil, err
	}

	result := map[string]interface{}{
		"name":              cronJob.GetName(),
		"creationTimestamp": cronJob.GetCreationTimestamp().String(),
		"schedule":          getSchedule(*cronJob),
		"suspend":           getSuspend(*cronJob),
		"active":            getActiveJobs(*cronJob),
		"lastSchedule":      getLastScheduleTime(*cronJob),
		"metadata":          cronJob.Object["metadata"],
		"spec":              cronJob.Object["spec"],
		"status":            cronJob.Object["status"],
	}

	return result, nil
}

// CreateCronJob 创建CronJob
func CreateCronJob(clusterName, namespace, yamlData string) (map[string]interface{}, error) {
	client, err := GetDynamicClient(clusterName)
	if err != nil {
		return nil, err
	}

	unstructuredObj, err := ParseYamlToUnstructured(yamlData)
	if err != nil {
		return nil, err
	}

	// 确保资源类型是CronJob
	if unstructuredObj.GetKind() != "CronJob" {
		return nil, fmt.Errorf("expected kind CronJob, got %s", unstructuredObj.GetKind())
	}

	// 获取适用的API版本
	cronJobGVR, err := getCronJobGVR(clusterName)
	if err != nil {
		logger.Error(fmt.Sprintf("获取CronJob API版本失败: %v, 将使用默认版本", err))
	}

	// 更新API版本
	apiVersion := fmt.Sprintf("%s/%s", cronJobGVR.Group, cronJobGVR.Version)
	unstructuredObj.SetAPIVersion(apiVersion)

	// 创建CronJob
	created, err := client.Resource(cronJobGVR).Namespace(namespace).Create(context.TODO(), unstructuredObj, metav1.CreateOptions{})
	if err != nil {
		logger.Errorf("create cronjob error: %v", err)
		return nil, err
	}

	result := map[string]interface{}{
		"name":              created.GetName(),
		"creationTimestamp": created.GetCreationTimestamp().String(),
		"schedule":          getSchedule(*created),
		"suspend":           getSuspend(*created),
		"metadata":          created.Object["metadata"],
		"spec":              created.Object["spec"],
	}

	return result, nil
}

// UpdateCronJob 更新CronJob
func UpdateCronJob(clusterName, namespace, name, yamlData string) (map[string]interface{}, error) {
	client, err := GetDynamicClient(clusterName)
	if err != nil {
		return nil, err
	}

	// 获取适用的API版本
	cronJobGVR, err := getCronJobGVR(clusterName)
	if err != nil {
		logger.Error(fmt.Sprintf("获取CronJob API版本失败: %v, 将使用默认版本", err))
	}

	// 先获取现有的 CronJob
	existing, err := client.Resource(cronJobGVR).Namespace(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	if err != nil {
		logger.Errorf("获取现有 CronJob 失败: %v", err)
		return nil, err
	}

	// 解析 YAML 数据
	unstructuredObj, err := ParseYamlToUnstructured(yamlData)
	if err != nil {
		// 如果解析失败，尝试添加缺少的字段
		logger.Error(fmt.Sprintf("解析 YAML 失败: %v, 尝试修复...", err))

		// 添加 apiVersion 和 kind 字段
		apiVersion := fmt.Sprintf("%s/%s", cronJobGVR.Group, cronJobGVR.Version)
		fixedYaml := fmt.Sprintf("apiVersion: %s\nkind: CronJob\n%s", apiVersion, yamlData)

		unstructuredObj, err = ParseYamlToUnstructured(fixedYaml)
		if err != nil {
			logger.Error(fmt.Sprintf("修复后解析 YAML 仍然失败: %v", err))
			return nil, err
		}
	}

	// 确保资源类型是CronJob
	if unstructuredObj.GetKind() == "" {
		unstructuredObj.SetKind("CronJob")
	}

	// 确保名称匹配
	if unstructuredObj.GetName() == "" {
		unstructuredObj.SetName(name)
	} else if unstructuredObj.GetName() != name {
		return nil, fmt.Errorf("name in YAML (%s) does not match requested name (%s)", unstructuredObj.GetName(), name)
	}

	// 更新API版本
	apiVersion := fmt.Sprintf("%s/%s", cronJobGVR.Group, cronJobGVR.Version)
	unstructuredObj.SetAPIVersion(apiVersion)

	// 保留现有的一些元数据
	metadata := unstructuredObj.Object["metadata"].(map[string]interface{})
	existingMetadata := existing.Object["metadata"].(map[string]interface{})

	// 保留 resourceVersion，这对于更新操作是必需的
	if rv, exists := existingMetadata["resourceVersion"]; exists {
		metadata["resourceVersion"] = rv
	}

	// 更新CronJob
	updated, err := client.Resource(cronJobGVR).Namespace(namespace).Update(context.TODO(), unstructuredObj, metav1.UpdateOptions{})
	if err != nil {
		logger.Errorf("update cronjob error: %v", err)
		return nil, err
	}

	result := map[string]interface{}{
		"name":              updated.GetName(),
		"creationTimestamp": updated.GetCreationTimestamp().String(),
		"schedule":          getSchedule(*updated),
		"suspend":           getSuspend(*updated),
		"metadata":          updated.Object["metadata"],
		"spec":              updated.Object["spec"],
		"status":            updated.Object["status"],
	}

	return result, nil
}

// DeleteCronJob 删除CronJob
func DeleteCronJob(clusterName, namespace, name string) error {
	client, err := GetDynamicClient(clusterName)
	if err != nil {
		return err
	}

	// 获取适用的API版本
	cronJobGVR, err := getCronJobGVR(clusterName)
	if err != nil {
		logger.Error(fmt.Sprintf("获取CronJob API版本失败: %v, 将使用默认版本", err))
	}

	err = client.Resource(cronJobGVR).Namespace(namespace).Delete(context.TODO(), name, metav1.DeleteOptions{})
	if err != nil {
		logger.Errorf("delete cronjob error: %v", err)
		return err
	}

	return nil
}

// 辅助函数，从Unstructured对象中获取schedule
func getSchedule(item unstructured.Unstructured) string {
	spec, ok := item.Object["spec"].(map[string]interface{})
	if !ok {
		return ""
	}

	schedule, ok := spec["schedule"].(string)
	if !ok {
		return ""
	}

	return schedule
}

// 辅助函数，从Unstructured对象中获取suspend状态
func getSuspend(item unstructured.Unstructured) bool {
	spec, ok := item.Object["spec"].(map[string]interface{})
	if !ok {
		return false
	}

	suspend, ok := spec["suspend"].(bool)
	if !ok {
		return false
	}

	return suspend
}

// 辅助函数，从Unstructured对象中获取活跃任务数
func getActiveJobs(item unstructured.Unstructured) int {
	status, ok := item.Object["status"].(map[string]interface{})
	if !ok {
		return 0
	}

	active, ok := status["active"].([]interface{})
	if !ok {
		return 0
	}

	return len(active)
}

// 辅助函数，从Unstructured对象中获取最后调度时间
func getLastScheduleTime(item unstructured.Unstructured) string {
	status, ok := item.Object["status"].(map[string]interface{})
	if !ok {
		return ""
	}

	lastSchedule, ok := status["lastScheduleTime"].(string)
	if !ok {
		return ""
	}

	return lastSchedule
}

// GetCronJobJobs 获取CronJob创建的Jobs
func GetCronJobJobs(clusterName, namespace, cronJobName string) ([]map[string]interface{}, error) {
	client, err := GetDynamicClient(clusterName)
	if err != nil {
		return nil, err
	}

	// 获取Job的GVR
	jobGVR := schema.GroupVersionResource{
		Group:    "batch",
		Version:  "v1",
		Resource: "jobs",
	}

	// 列出所有Job
	jobList, err := client.Resource(jobGVR).Namespace(namespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		logger.Error(fmt.Sprintf("获取Job列表失败: %v", err))
		return nil, err
	}

	var relatedJobs []map[string]interface{}

	// 筛选出由指定CronJob创建的Job
	for _, job := range jobList.Items {
		// 检查Job的所有者引用
		ownerRefs := job.GetOwnerReferences()
		for _, ownerRef := range ownerRefs {
			if ownerRef.Kind == "CronJob" && ownerRef.Name == cronJobName {
				// 这是由我们的CronJob创建的Job
				jobInfo := map[string]interface{}{
					"name":              job.GetName(),
					"creationTimestamp": job.GetCreationTimestamp().String(),
					"status":            getJobStatus(job),
					"metadata":          job.Object["metadata"],
					"spec":              job.Object["spec"],
					"status_details":    job.Object["status"],
				}

				// 获取Job的Pod
				pods, err := getJobPods(clusterName, namespace, job.GetName())
				if err == nil {
					jobInfo["pods"] = pods
				}

				relatedJobs = append(relatedJobs, jobInfo)
				break
			}
		}
	}

	return relatedJobs, nil
}

// 获取Job的状态
func getJobStatus(job unstructured.Unstructured) string {
	status, ok := job.Object["status"].(map[string]interface{})
	if !ok {
		return "Unknown"
	}

	if succeeded, ok := status["succeeded"].(int64); ok && succeeded > 0 {
		return "Succeeded"
	}

	if failed, ok := status["failed"].(int64); ok && failed > 0 {
		return "Failed"
	}

	if active, ok := status["active"].(int64); ok && active > 0 {
		return "Active"
	}

	return "Pending"
}

// 获取Job创建的Pod
func getJobPods(clusterName, namespace, jobName string) ([]map[string]interface{}, error) {
	client, err := GetDynamicClient(clusterName)
	if err != nil {
		return nil, err
	}

	// Pod的GVR
	podGVR := schema.GroupVersionResource{
		Group:    "",
		Version:  "v1",
		Resource: "pods",
	}

	// 列出所有Pod
	podList, err := client.Resource(podGVR).Namespace(namespace).List(context.TODO(), metav1.ListOptions{
		LabelSelector: fmt.Sprintf("job-name=%s", jobName),
	})
	if err != nil {
		logger.Error(fmt.Sprintf("获取Pod列表失败: %v", err))
		return nil, err
	}

	var pods []map[string]interface{}
	for _, pod := range podList.Items {
		podInfo := map[string]interface{}{
			"name":              pod.GetName(),
			"creationTimestamp": pod.GetCreationTimestamp().String(),
			"phase":             getPodPhase(pod),
			"metadata":          pod.Object["metadata"],
			"spec":              pod.Object["spec"],
			"status":            pod.Object["status"],
		}
		pods = append(pods, podInfo)
	}

	return pods, nil
}

// 获取Pod的阶段
func getPodPhase(pod unstructured.Unstructured) string {
	status, ok := pod.Object["status"].(map[string]interface{})
	if !ok {
		return "Unknown"
	}

	phase, ok := status["phase"].(string)
	if !ok {
		return "Unknown"
	}

	return phase
}

// 获取Pod的日志
func GetPodLogs(clusterName, namespace, podName, containerName string) (string, error) {
	clientset, err := GetClientset(clusterName)
	if err != nil {
		return "", err
	}

	// 设置日志获取选项
	podLogOptions := &corev1.PodLogOptions{
		Container: containerName,
		TailLines: int64Ptr(1000), // 获取最后1000行日志
	}

	// 如果没有指定容器名，则获取第一个容器的日志
	if containerName == "" {
		// 先获取Pod信息
		pod, err := clientset.CoreV1().Pods(namespace).Get(context.TODO(), podName, metav1.GetOptions{})
		if err != nil {
			return "", err
		}

		if len(pod.Spec.Containers) > 0 {
			podLogOptions.Container = pod.Spec.Containers[0].Name
		}
	}

	// 获取日志
	req := clientset.CoreV1().Pods(namespace).GetLogs(podName, podLogOptions)
	podLogs, err := req.Stream(context.TODO())
	if err != nil {
		return "", err
	}
	defer podLogs.Close()

	// 读取日志内容
	buf := new(bytes.Buffer)
	_, err = io.Copy(buf, podLogs)
	if err != nil {
		return "", err
	}

	return buf.String(), nil
}

// 辅助函数，创建int64指针
func int64Ptr(i int64) *int64 {
	return &i
}

// TriggerCronJob 立即触发 CronJob 创建一个 Job
func TriggerCronJob(clusterName, namespace, name string) (map[string]interface{}, error) {
	// 获取 CronJob 对象
	cronJobGVR, err := getCronJobGVR(clusterName)
	if err != nil {
		logger.Error(fmt.Sprintf("获取 CronJob API 版本失败: %v", err))
		return nil, err
	}

	// 使用动态客户端获取 CronJob
	dynamicClient, err := GetDynamicClient(clusterName)
	if err != nil {
		return nil, err
	}

	cronJob, err := dynamicClient.Resource(cronJobGVR).Namespace(namespace).Get(context.TODO(), name, metav1.GetOptions{})
	if err != nil {
		logger.Errorf("获取 CronJob 失败: %v", err)
		return nil, err
	}

	// 创建一个 Job 对象作为 CronJob 的临时手动触发
	timestamp := time.Now().Unix()
	jobName := fmt.Sprintf("%s-manual-%d", name, timestamp)

	// 从 CronJob 模板创建 Job
	cronJobObj := cronJob.Object
	cronJobSpec, ok := cronJobObj["spec"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("无法读取 CronJob spec")
	}

	jobTemplate, ok := cronJobSpec["jobTemplate"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("无法读取 jobTemplate")
	}

	// 创建 Job 对象
	jobObj := map[string]interface{}{
		"apiVersion": "batch/v1",
		"kind":       "Job",
		"metadata": map[string]interface{}{
			"name":      jobName,
			"namespace": namespace,
			"ownerReferences": []map[string]interface{}{
				{
					"apiVersion":         cronJob.GetAPIVersion(),
					"kind":               "CronJob",
					"name":               name,
					"uid":                cronJob.GetUID(),
					"controller":         true,
					"blockOwnerDeletion": true,
				},
			},
			"labels": map[string]interface{}{
				"cronjob-name": name,
				"triggered-by": "manual",
			},
		},
		"spec": jobTemplate["spec"],
	}

	jobUnstructured := &unstructured.Unstructured{Object: jobObj}

	// 创建 Job
	jobGVR := schema.GroupVersionResource{
		Group:    "batch",
		Version:  "v1",
		Resource: "jobs",
	}

	createdJob, err := dynamicClient.Resource(jobGVR).Namespace(namespace).Create(context.TODO(), jobUnstructured, metav1.CreateOptions{})
	if err != nil {
		logger.Errorf("创建 Job 失败: %v", err)
		return nil, err
	}

	result := map[string]interface{}{
		"name":              createdJob.GetName(),
		"creationTimestamp": createdJob.GetCreationTimestamp().String(),
		"status":            "Created",
	}

	return result, nil
}

// DeleteCronJobJobs 删除与CronJob相关的所有Job
func DeleteCronJobJobs(clusterName, namespace, cronJobName string) error {
	// 获取动态客户端
	client, err := GetDynamicClient(clusterName)
	if err != nil {
		return err
	}

	// 获取Job的GVR
	jobGVR := schema.GroupVersionResource{
		Group:    "batch",
		Version:  "v1",
		Resource: "jobs",
	}

	// 列出所有Job
	jobList, err := client.Resource(jobGVR).Namespace(namespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		logger.Error(fmt.Sprintf("获取Job列表失败: %v", err))
		return err
	}

	// 记录删除错误
	var deleteErrors []error

	// 筛选出由指定CronJob创建的Job并删除
	for _, job := range jobList.Items {
		// 检查Job的所有者引用
		ownerRefs := job.GetOwnerReferences()
		for _, ownerRef := range ownerRefs {
			if ownerRef.Kind == "CronJob" && ownerRef.Name == cronJobName {
				// 这是由我们的CronJob创建的Job，删除它
				jobName := job.GetName()
				logger.Info(fmt.Sprintf("删除Job: %s", jobName))

				// 删除Job的背景传播策略
				deletePolicy := metav1.DeletePropagationBackground
				deleteOptions := metav1.DeleteOptions{
					PropagationPolicy: &deletePolicy,
				}

				// 执行删除
				err := client.Resource(jobGVR).Namespace(namespace).Delete(context.TODO(), jobName, deleteOptions)
				if err != nil {
					logger.Error(fmt.Sprintf("删除Job %s 失败: %v", jobName, err))
					deleteErrors = append(deleteErrors, err)
				}
				break
			}
		}
	}

	// 如果有删除错误，返回第一个错误
	if len(deleteErrors) > 0 {
		return fmt.Errorf("删除部分Job失败: %v", deleteErrors[0])
	}

	return nil
}
