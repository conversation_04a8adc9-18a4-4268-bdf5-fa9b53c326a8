package feishu

import (
	"blops/logger"
	"blops/rest"
	"blops/utils"
	"errors"
	"github.com/sirupsen/logrus"
)

type FeishuApi[D any] struct {
	*rest.Rest[D]
}

var (
	feishuApi *FeishuApi[ApiResDTO[any]]
)

func init() {
	feishuApi = &FeishuApi[ApiResDTO[any]]{
		&rest.Rest[ApiResDTO[any]]{
			BaseUrl: conf.BaseUrl,
		},
	}
}

func Get[D any](uri string, params interface{}) (data D, err error) {
	res, err := feishuApi.Get(uri, params)
	if err != nil {
		return data, err
	}
	logrus.Println(res.Code)
	if res.Code == 0 {
		return utils.JsonUnmarshal[D](res.Data)
	}
	logrus.Println(res.Msg)
	return data, errors.New(res.Msg)
}

func Post[D any](uri string, params interface{}, body interface{}) (data D, err error) {
	res, err := feishuApi.Post(uri, params, body)
	if err != nil {
		return data, err
	}
	logger.Info(res.Code)
	if res.Code == 0 {
		return utils.JsonUnmarshal[D](res.Data)
	}
	logger.Error(res.Msg)
	return data, errors.New(res.Msg)
}
