package feishu

import (
	"blops/config"
	. "blops/enums/feishu"
	"blops/logger"
	"blops/rest"
	"encoding/json"
	"errors"
	"sync"
)

var (
	conf config.Lark
)

func init() {
	conf = config.Conf.Alert.Platform.Lark
}

var tokenRestClient *rest.Rest[TenantAccessTokenResponse]

var once sync.Once

// GenAccessToken 获取token
func GenAccessToken() (string, error) {
	once.Do(func() {
		tokenRestClient = rest.NewRestClient[TenantAccessTokenResponse]()
	})
	tokenRestClient.BaseUrl = feishuApi.BaseUrl
	tokenRestClient.Headers = feishuApi.Headers
	res, err := tokenRestClient.Post("/open-apis/auth/v3/tenant_access_token/internal/", nil, map[string]string{
		"app_id":     conf.Robot.AppId,
		"app_secret": conf.Robot.AppSecret,
	})
	if err != nil {
		logger.Error(err)
		return "", err
	}
	if res.Code == 0 {
		if feishuApi.Headers == nil {
			feishuApi.Headers = map[string]string{}
		}
		feishuApi.Headers["Authorization"] = "Bearer " + res.TenantAccessToken
		return res.TenantAccessToken, nil
	}
	logger.Info(res)
	return "", errors.New(res.Msg)
}

func CreateChat(request CreateChatRequest) (string, error) {
	res, err := Post[CreateChatRespBody]("/open-apis/im/v1/chats", map[string]string{
		"user_id_type": UserIdTypeEnum[OpenId],
	}, request)
	if err != nil {
		logger.Errorf("create lark chat error: %v", err)
		return "", err
	}
	logger.Info(res.ChatId)
	if res.ChatId == "" {
		return "", nil
	}
	return res.ChatId, nil
}

func AddChatMembers(chatId string, request ChatMembersInviteRequest) (bool, error) {
	res, err := Post[ChatMembersInviteRespBody]("/open-apis/im/v1/chats/"+chatId+"/members", map[string]string{
		"member_id_type": UserIdTypeEnum[OpenId],
	}, request)
	if err != nil {
		logger.Errorf("get lark user error: %v", err)
		return false, err
	}
	logger.Info(len(res.InvalidIDList))
	return true, nil
}

// GetUserIdByPhoneOrEmail 获取用户id
func GetUserIdByPhoneOrEmail(userType UserIdType, dto ReqGetUserIdDTO) ([]ResGetUserIdOneDTO, error) {
	res, err := Post[ResGetUserIdDTO]("/open-apis/contact/v3/users/batch_get_id", map[string]string{
		"user_id_type": UserIdTypeEnum[userType],
	}, dto)
	if err != nil {
		logger.Errorf("get lark user error: %v", err)
		return nil, err
	}
	logger.Info(len(res.UserList))
	return res.UserList, nil
}

// SendMessages 发送消息
func SendMessages(msgReceiverType MsgReceiverType, dto CreateMessageRequest) (*MessageItem, error) {
	res, err := Post[MessageItem]("/open-apis/im/v1/messages", map[string]string{
		"receive_id_type": MsgReceiverTypeEnum[msgReceiverType],
	}, dto)
	if err != nil {
		logger.Error(err)
		return nil, err
	}
	logger.Info(res.MessageID)
	return &res, nil
}

func GenCard(template CardTitleTemplate, title, content string) CardContent {
	var cardElementList []interface{}
	cardElementList = append(cardElementList, CardElement{
		Tag: LarkContentTagTypeEnum[DIV],
		Text: &CardText{
			Tag:     LarkContentTagTypeEnum[LarkMd],
			Content: content,
		},
	})

	return CardContent{
		Header: &CardHeader{
			Title: &CardText{
				Tag:     LarkContentTagTypeEnum[PlainText],
				Content: title,
			},
			Template: CardTitleTemplateEnum[template],
		},
		Elements: cardElementList,
	}
}

// SendCard 发送消息卡片
func SendCard(receiveId string, receiveType MsgReceiverType, cardContent CardContent) (msgId string, err error) {
	msgId = ""
	_, err = GenAccessToken()
	if err != nil {
		logger.Error(err)
		return
	}
	logger.Info("lark tenant_access_token get success...")
	cardF, err := json.Marshal(cardContent)
	if err != nil {
		logger.Errorf("card content marshal error: %v", err)
		return
	}
	res, err := SendMessages(receiveType, CreateMessageRequest{
		ReceiveID: receiveId,
		Content:   string(cardF),
		MsgType:   SendMsgTypeEnum[INTERACTIVE],
	})
	if err != nil {
		logger.Error(err)
		return
	}
	msgId = res.MessageID
	return
}
