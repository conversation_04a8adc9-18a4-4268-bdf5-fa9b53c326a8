package ldap

import (
	"blops/logger"
	"fmt"
	"github.com/go-ldap/ldap"
)

func connect() (*ldap.Conn, error) {
	l, err := ldap.Dial("tcp", "10.80.1.21:389")
	if err != nil {
		logger.Errorf("连接失败", err)
		return nil, err
	}
	return l, nil
}

func auth(conn *ldap.Conn, username, password string) error {
	u := fmt.Sprintf("cn=%s,dc=blacklake,dc=tech", username)
	err := conn.Bind(u, password)
	if err != nil {
		logger.Errorf("ldap (u: %s, p: %s) auth error", username, password, err)
	}
	return err
}

func AuthLdap(u string, p string) (displayName string, err error) {
	l, err := connect()
	if err != nil {
		return "", err
	}
	if err := auth(l, u, p); err != nil {
		return "", err
	}
	searchRequest := ldap.NewSearchRequest(fmt.Sprintf("cn=%s,dc=blacklake,dc=tech", u),
		ldap.ScopeWholeSubtree, ldap.NeverDerefAliases, 0, 0, false,
		"(&(objectClass=organizationalPerson))",
		[]string{"dn", "cn", "mail", "displayName"}, nil)
	search, err := l.Search(searchRequest)
	defer l.Close()
	if err != nil {
		logger.Error(err)
		return "", err
	}
	for _, entry := range search.Entries {
		fmt.Printf(">>>%v, %v\n", entry.DN, entry.GetAttributeValue("displayName"))
		displayName = entry.GetAttributeValue("displayName")
	}

	return displayName, err
}

func ListAllUser() ([]UserDTO, error) {
	conn, err := connect()
	if err != nil {
		return nil, err
	}
	if err := auth(conn, "admin", "/CbZ^6sUGc2kb"); err != nil {
		return nil, err
	}
	searchRequest := ldap.NewSearchRequest("dc=blacklake,dc=tech",
		ldap.ScopeWholeSubtree, ldap.NeverDerefAliases, 0, 0, false,
		"(&(objectClass=organizationalPerson))",
		[]string{}, nil)
	search, err := conn.Search(searchRequest)
	defer conn.Close()
	if err != nil {
		logger.Errorf("list ldap user error", err)
		return nil, err
	}
	for _, entry := range search.Entries {
		fmt.Printf(">>>%v, %v\n", entry.DN, entry.GetAttributeValue("displayName"))
		//displayName = entry.GetAttributeValue("displayName")
	}
	return nil, nil
}
