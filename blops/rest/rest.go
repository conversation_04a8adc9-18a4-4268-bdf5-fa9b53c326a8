package rest

import (
	"encoding/json"
	"io"
	"net/http"
	hurl "net/url"
	"reflect"
	"strings"
)

type Rest[R any] struct {
	BaseUrl string            `json:"base_url,omitempty"`
	Headers map[string]string `json:"headers"`
}

func NewRestClient[R any]() *Rest[R] {
	return &Rest[R]{}
}

func (r *Rest[R]) Request(method string, url string, params interface{}, body interface{}) (res R, err error) {
	client := &http.Client{}
	var reqUrl = ""
	if r.BaseUrl != "" {
		reqUrl += r.BaseUrl
	}
	reqUrl += url
	if params != nil || !reflect.ValueOf(&params).IsNil() {
		reqUrl += "?"
		var p = map[string]string{}
		pList, err := json.Marshal(params)
		if err != nil {
			return res, err
		}
		err = json.Unmarshal(pList, &p)
		if err != nil {
			return res, err
		}
		var urlParams = hurl.Values{}
		for k, v := range p {
			urlParams.Add(k, v)
		}
		reqUrl += urlParams.Encode()
	}
	var reqBody []byte
	if body != nil && !reflect.ValueOf(&body).IsNil() {
		reqBody, _ = json.Marshal(body)
	}
	req, err := http.NewRequest(method, reqUrl, strings.NewReader(string(reqBody)))
	if err != nil {
		return res, err
	}
	if r.Headers == nil {
		r.Headers = map[string]string{}
	}
	r.Headers["Content-Type"] = "application/json"
	for k, v := range r.Headers {
		req.Header.Set(k, v)
	}
	resp, err := client.Do(req)
	if err != nil {
		return res, err
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			println(err)
		}
	}(resp.Body)
	err = json.NewDecoder(resp.Body).Decode(&res)
	if err != nil {
		return res, err
	}
	return res, err
}

// Get rest get方法
func (r *Rest[R]) Get(url string, params interface{}) (res R, err error) {
	return r.Request(http.MethodGet, url, params, nil)
}

// Post rest post方法
func (r *Rest[R]) Post(url string, params interface{}, body interface{}) (res R, err error) {
	return r.Request(http.MethodPost, url, params, body)
}
