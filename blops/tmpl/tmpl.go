package tmpl

import (
	"blops/logger"
	"bytes"
	"path"
	"text/template"
)

// TemplateHandlerStrWithFiles 利用模版解析结构体数据到string
// D 模版数据结构体
func TemplateHandlerStrWithFiles[D any](filename string, data *D) (string, error) {
	t, err := template.New(filename).ParseFiles(path.Join("tmpl/k8s", filename))
	if err != nil {
		logger.Errorf("模版创建失败", err)
		return "", err
	}
	var d bytes.Buffer
	err = t.Execute(&d, data)
	if err != nil {
		logger.Errorf("模版解析失败", err)
		return "", err
	}
	logger.Info(d.String())
	return d.String(), nil
}

// TemplateHandlerStrWithStr 利用模版解析结构体数据到string
// D 模版数据结构体
func TemplateHandlerStrWithStr[D any](name string, content string, data *D) (string, error) {
	t, err := template.New(name).Option("missingkey=default").Parse(content)
	if err != nil {
		logger.Errorf("模版创建失败: %v", err)
		return "", err
	}
	var d bytes.Buffer
	err = t.Execute(&d, data)
	if err != nil {
		logger.Errorf("模版解析失败: %v", err)
		return "", err
	}
	logger.Info(d.String())
	return d.String(), nil
}
