apiVersion: v1
kind: ConfigMap
metadata:
  annotations:
    meta.helm.sh/release-name: loki
    meta.helm.sh/release-namespace: ops
  labels:
    app: loki
    app.kubernetes.io/managed-by: Helm
    chart: loki-2.8.4
    heritage: Helm
    release: loki
  name: {{ .Name }}
  namespace: {{ .Namespace }}
data:
  loki-alerting-rules.yaml: |-
    groups:
    {{ range $m, $group := .AlertGroupList }}
      - name: {{ $group.Name }}
        rules:
        {{ range $n, $alert := $group.AlertList }}
        - alert: {{ $alert.AlertName }}
          annotations:
            message: {{ $alert.Message }}
          expr: {{ $alert.Expr }}
          for: {{ $alert.Period }}
          labels:
            level: {{ $alert.Level }}
            receiver: {{ $alert.Receiver }}
            webhook: {{ $alert.Webhook }}
        {{end}}
    {{end}}