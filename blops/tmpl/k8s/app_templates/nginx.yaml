apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{.name}}
  namespace: {{.namespace}}
  labels:
    app: {{.name}}
spec:
  replicas: {{.replicas}}
  selector:
    matchLabels:
      app: {{.name}}
  template:
    metadata:
      labels:
        app: {{.name}}
    spec:
      containers:
      - name: {{.name}}
        image: {{.image}}
        ports:
        - containerPort: {{.port}}
        resources:
          requests:
            memory: "{{.memoryRequest}}"
            cpu: "{{.cpuRequest}}"
          limits:
            memory: "{{.memoryLimit}}"
            cpu: "{{.cpuLimit}}"
{{if .enableService}}
---
apiVersion: v1
kind: Service
metadata:
  name: {{.name}}
  namespace: {{.namespace}}
spec:
  selector:
    app: {{.name}}
  ports:
  - port: {{.servicePort}}
    targetPort: {{.port}}
  type: {{.serviceType}}
{{end}} 