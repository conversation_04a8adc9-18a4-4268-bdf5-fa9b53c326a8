package k8s

import "encoding/json"

// AppTemplateDO represents an application template
type AppTemplateDO struct {
	// Basic info
	Name        string `json:"name"`
	Description string `json:"description"`
	Category    string `json:"category"`
	Icon        string `json:"icon"`
	Version     string `json:"version"`
	Maintainer  string `json:"maintainer"`

	// YAML template
	YamlTemplate string `json:"yamlTemplate"`

	// Variable definitions
	Variables []AppTemplateVariable `json:"variables"`
}

// AppTemplateVariable represents a variable that can be replaced in the YAML template
type AppTemplateVariable struct {
	Name         string           `json:"name"`
	Label        string           `json:"label"`
	Description  string           `json:"description"`
	DefaultValue interface{}      `json:"default_value"`
	Required     bool             `json:"required"`
	Type         string           `json:"type"`              // string, number, boolean, select, etc.
	Options      []VariableOption `json:"options,omitempty"` // For select type
}

// VariableOption represents an option for a select-type variable
type VariableOption struct {
	Label string      `json:"label"`
	Value interface{} `json:"value"`
}

// AppDeploymentDO represents the deployment information for an application
type AppDeploymentDO struct {
	TemplateName string                 `json:"template_name"`
	Namespace    string                 `json:"namespace"`
	ClusterName  string                 `json:"cluster_name"`
	Variables    map[string]interface{} `json:"variables"`
}

// ToJSON converts the object to JSON string
func (a *AppTemplateDO) ToJSON() (string, error) {
	bytes, err := json.Marshal(a)
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}

// FromJSON parses the JSON string into the object
func (a *AppTemplateDO) FromJSON(jsonStr string) error {
	return json.Unmarshal([]byte(jsonStr), a)
}

// ToJSON converts the object to JSON string
func (a *AppDeploymentDO) ToJSON() (string, error) {
	bytes, err := json.Marshal(a)
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}

// FromJSON parses the JSON string into the object
func (a *AppDeploymentDO) FromJSON(jsonStr string) error {
	return json.Unmarshal([]byte(jsonStr), a)
}
