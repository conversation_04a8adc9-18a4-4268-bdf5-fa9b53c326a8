package k8s

type AlertRuleDO struct {
	// 告警名称
	AlertName string `json:"alert_name"`
	// 消息
	Message string `json:"message"`
	// 计算表达式
	Expr string `json:"expr"`
	// 检测周期
	Period string `json:"period"`
	// 告警级别
	Level string `json:"level"`
	// 接收方
	Receiver string `json:"receiver"`
	// web钩子
	Webhook string `json:"webhook"`
	// 自定义标签
	Labels map[string]string `json:"labels"`
}

type AlertGroupDO struct {
	// 告警分组名称
	Name string `json:"group_name"`
	// 告警规则列表
	AlertList []AlertRuleDO `json:"alert_list"`
}

// PrometheusAlertRuleDO prometheus 告警模版-数据结构
type PrometheusAlertRuleDO struct {
	// metadata告警规则名称
	Name string `json:"name"`
	// 集群
	Cluster string `json:"cluster"`
	// 所属空间
	Namespace string `json:"namespace"`
	// 告警分组列表
	AlertGroupList []AlertGroupDO `json:"alert_group_list"`
}
