apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: {{ .Name }}
  namespace: {{ .Namespace }}
  labels:
    prometheus: k8s
    role: alert-rules
spec:
  groups:
  {{ range $m, $group := .AlertGroupList -}}
  - name: {{ $group.Name }}
    rules:
    {{ range $n, $alert := $group.AlertList -}}
    - alert: {{ $alert.AlertName }}
      annotations:
        message: {{ $alert.Message }}
      expr: {{ $alert.Expr }}
      for: {{ $alert.Period }}
      labels:
        level: {{ $alert.Level }}
        receiver: {{ $alert.Receiver }}
        webhook: {{ $alert.Webhook }}
        severity: warning
        user: prometheus
        {{- range $key, $value := $alert.Labels }}
        {{ $key }}: {{ $value }}
        {{- end }}
    {{end}}
  {{end}}