package router

import (
	"blops/app/controllers"

	"github.com/gin-gonic/gin"
)

func ClusterRouter(r *gin.RouterGroup) {
	g := r.Group("/v1/cluster")
	{
		g.POST("/list", controllers.ClusterCtrl.ListClusters)
		g.POST("/namespace/list", controllers.ClusterCtrl.ListNamespaces)
		g.POST("/deployment/list", controllers.ClusterCtrl.ListDeployments)
		g.POST("/service/list", controllers.ClusterCtrl.ListServices)
		g.POST("/ingress/list", controllers.ClusterCtrl.ListIngresses)
		g.POST("/deployment/pods", controllers.ClusterCtrl.ListPodsByDeployment)
		g.POST("/resource/get", controllers.ClusterCtrl.GetResource)
		g.POST("/resource/create", controllers.ClusterCtrl.CreateResource)
		g.POST("/resource/update", controllers.ClusterCtrl.UpdateResource)
		g.POST("/resource/delete", controllers.ClusterCtrl.DeleteResource)
		g.GET("/pods", controllers.PodCtrl.ListPods)
	}
}
