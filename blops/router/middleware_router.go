package router

import (
	"blops/app/controllers"

	"github.com/gin-gonic/gin"
)

// MiddlewareRouter 中间件路由定义
func MiddlewareRouter(r *gin.RouterGroup) {
	g := r.Group("/v1/middleware")
	{
		// 环境相关路由
		g.GET("/env", controllers.MiddlewareCtrl.ListEnvs)
		g.GET("/env/:id", controllers.MiddlewareCtrl.GetEnv)
		g.POST("/env", controllers.MiddlewareCtrl.CreateEnv)
		g.PUT("/env", controllers.MiddlewareCtrl.UpdateEnv)
		g.DELETE("/env/:id", controllers.MiddlewareCtrl.DeleteEnv)

		// 链接相关路由
		g.GET("/link", controllers.MiddlewareCtrl.ListLinks)
		g.GET("/link/:id", controllers.MiddlewareCtrl.GetLink)
		g.POST("/link", controllers.MiddlewareCtrl.CreateLink)
		g.PUT("/link", controllers.MiddlewareCtrl.UpdateLink)
		g.DELETE("/link/:id", controllers.MiddlewareCtrl.DeleteLink)
		g.POST("/link/by-type", controllers.MiddlewareCtrl.ListLinksByType)
		g.POST("/link/by-env", controllers.MiddlewareCtrl.ListLinksByEnv)
		g.POST("/link/by-type-env", controllers.MiddlewareCtrl.ListLinksByTypeAndEnv)
	}
}
