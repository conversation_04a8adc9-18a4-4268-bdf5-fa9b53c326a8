package router

import (
	"blops/app/controllers"

	"github.com/gin-gonic/gin"
)

func AppMarketRouter(r *gin.RouterGroup) {
	// 创建应用市场控制器
	ctrl := controllers.NewAppMarketController()

	// 注册路由到/v1/app-market路径
	appGroup := r.Group("/v1/app-market")
	{
		appGroup.POST("/templates/_list", ctrl.ListAppTemplates)
		appGroup.GET("/templates/:name", ctrl.GetAppTemplateDetail)
		appGroup.POST("/templates", ctrl.AddAppTemplate)
		appGroup.PUT("/templates/:name", ctrl.UpdateAppTemplate)
		appGroup.DELETE("/templates/:name", ctrl.DeleteAppTemplate)
		appGroup.GET("/deployed", ctrl.ListDeployedApps)
		appGroup.GET("/deployed/:id", ctrl.GetDeployedApp)
		appGroup.POST("/deployed", ctrl.CreateDeployedApp)
		appGroup.PUT("/deployed/:id", ctrl.UpdateDeployedApp)
		appGroup.DELETE("/deployed/:id", ctrl.DeleteDeployedApp)
	}

	// 新增：已部署应用 CRUD
	deployed := r.Group("/appmarket/deployed")
	{
		// 列表
		deployed.GET("", ctrl.ListDeployedApps)
		// 详情
		deployed.GET("/:id", ctrl.GetDeployedApp)
		// 新增
		deployed.POST("", ctrl.CreateDeployedApp)
		// 更新
		deployed.PUT("/:id", ctrl.UpdateDeployedApp)
		// 删除
		deployed.DELETE("/:id", ctrl.DeleteDeployedApp)
	}
}
