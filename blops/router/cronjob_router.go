package router

import (
	"blops/app/controllers"

	"github.com/gin-gonic/gin"
)

func CronJobRouter(r *gin.RouterGroup) {
	g := r.Group("/v1/cronjob")
	{
		g.POST("/list", controllers.CronJobCtrl.ListCronJobs)
		g.POST("/get", controllers.CronJobCtrl.GetCronJob)
		g.POST("/create", controllers.CronJobCtrl.CreateCronJob)
		g.POST("/update", controllers.CronJobCtrl.UpdateCronJob)
		g.POST("/delete", controllers.CronJobCtrl.DeleteCronJob)
		g.POST("/jobs", controllers.CronJobCtrl.GetCronJobJobs)
		g.POST("/trigger", controllers.CronJobCtrl.TriggerCronJob)
		g.POST("/delete-jobs", controllers.CronJobCtrl.DeleteCronJobJobs)
	}

	podGroup := r.Group("/v1/pod")
	{
		podGroup.POST("/logs", controllers.CronJobCtrl.GetPodLogs)
	}
}
