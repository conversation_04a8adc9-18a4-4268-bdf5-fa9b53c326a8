package router

import (
	"blops/app/controllers"

	"github.com/gin-gonic/gin"
)

func AIDiagnosisRouter(g *gin.RouterGroup) {
	aiGroup := g.Group("/ai")
	{
		aiGroup.POST("/diagnosis", controllers.AIDiagnosis)
		// 添加通用AI分析接口
		aiGroup.POST("/analyze", controllers.AIAnalyze)
		// 添加AI助手聊天接口
		aiGroup.POST("/chat", controllers.AIChat)
		// 添加聊天历史管理接口
		aiGroup.GET("/chat/history", controllers.GetChatHistory)
		aiGroup.DELETE("/chat/history", controllers.ClearChatHistory)
		// 添加快速问题模板接口
		aiGroup.GET("/templates", controllers.GetQuickTemplates)
	}

	// 集群相关API
	clusterGroup := g.Group("/cluster")
	{
		// 获取集群列表
		clusterGroup.GET("/list", controllers.GetClusters)

		// 获取命名空间事件
		clusterGroup.GET("/:cluster_id/namespace/:namespace/events", controllers.GetNamespaceEvents)

		// 获取命名空间列表
		clusterGroup.GET("/:cluster_id/namespaces", controllers.GetNamespaces)
	}
}
