package router

import (
	"blops/app/controllers"
	"github.com/gin-gonic/gin"
)

func AlertRouter(r *gin.RouterGroup) {
	alertRoute := r.Group("/v1/alert").Use()
	{
		alertRoute.POST("/all", controllers.AlertCtrl.AddAlert)
		alertRoute.POST("/list", controllers.AlertCtrl.SaveAlert)
		alertRoute.POST("/handler", controllers.AlertCtrl.AlertHandler)

		alertRoute.POST("/conf", controllers.RuleCtrl.AddRule)
		alertRoute.POST("/conf/_update", controllers.RuleCtrl.UpdateRule)
		alertRoute.POST("/conf/_list", controllers.RuleCtrl.GetRule)
		alertRoute.POST("/template", controllers.RuleCtrl.AddTemplate)
		alertRoute.POST("/template/_update", controllers.RuleCtrl.UpdateTemplate)
		alertRoute.POST("/template/_list", controllers.RuleCtrl.PullTemplate)
		alertRoute.GET("/tree", controllers.RuleCtrl.GetTree)
	}
}
