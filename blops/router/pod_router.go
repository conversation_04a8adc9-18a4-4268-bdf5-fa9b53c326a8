package router

import (
	"blops/app/controllers"

	"github.com/gin-gonic/gin"
)

// @Summary      Pod 执行命令
// @Description  在指定的 Pod 中执行命令
// @Tags         Pod管理
// @Accept       json
// @Produce      json
// @Param        cluster_id  query     string  true  "集群ID"
// @Param        namespace   query     string  true  "命名空间"
// @Param        pod_name    query     string  true  "Pod名称"
// @Param        container   query     string  false "容器名称"
// @Success      200         {object}  models.Response  "执行成功"
// @Failure      400         {object}  models.Response  "请求参数错误"
// @Failure      500         {object}  models.Response  "执行失败"
// @Router       /v1/pod/exec [get]
// @Security     ApiKeyAuth
func PodRouter(r *gin.RouterGroup) {
	g := r.Group("/v1/pod")
	{
		// WebSocket 路由需要特殊处理，不能使用标准的 gin 路由
		g.GET("/exec", func(c *gin.Context) {
			controllers.PodCtrl.HandlePodExec(c.Writer, c.Request)
		})

		g.GET("/command", func(c *gin.Context) {
			controllers.PodCtrl.ExecCommand(c)
		})
	}
}

// @Summary      Pod 命令执行
// @Description  获取可在 Pod 中执行的命令列表
// @Tags         Pod管理
// @Accept       json
// @Produce      json
// @Success      200  {object}  models.Response  "获取命令列表成功"
// @Router       /v1/pod/command [get]
// @Security     ApiKeyAuth
