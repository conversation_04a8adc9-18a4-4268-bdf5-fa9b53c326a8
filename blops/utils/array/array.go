package array

import (
	"blops/logger"
	"encoding/json"
	"reflect"
)

func ToString[T any](t []T) (string, error) {
	if t == nil {
		return "", nil
	}
	byteData, err := json.Marshal(t)
	if err != nil {
		logger.Errorf("array to string error: %v", err)
		return "", err
	}
	return string(byteData), nil
}

func FromArray[T any](arrStr string) ([]T, error) {
	var list []T
	if arrStr != "" {
		err := json.Unmarshal([]byte(arrStr), &list)
		if err != nil {
			logger.Error(err)
			return nil, err
		}
	}
	return list, nil
}

func Repeat[T any](tList []T, key string) bool {
	if tList == nil {
		return false
	}
	temp := make(map[any]bool)
	for _, t := range tList {
		s := reflect.ValueOf(t)
		v := s.FieldByName(key).Interface()
		if exist, _ := temp[v]; exist {
			return true
		} else {
			temp[v] = true
		}
	}
	return false
}

func RepeatWithResult[T any](tList []T, key string) []T {
	if tList == nil {
		return nil
	}
	temp := make(map[any]bool)
	var repeatTList []T
	for _, t := range tList {
		s := reflect.ValueOf(&t)
		v := s.Elem().FieldByName(key).Interface()
		if exist, _ := temp[v]; exist {
			repeatTList = append(repeatTList, t)
		}
	}
	return repeatTList
}
