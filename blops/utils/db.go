package utils

import (
	"blops/app/models"
	"blops/config"
	"fmt"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
)

// DB is the global database connection
var DB *gorm.DB

// InitDB reads DSN from config, opens a GORM connection, and auto-migrates models.
func InitDB() error {
	var err error
	// build DSN from config fields
	dsn := fmt.Sprintf(
		"%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		config.Conf.DB.User,
		config.Conf.DB.Password,
		config.Conf.DB.Host,
		config.Conf.DB.Port,
		config.Conf.DB.Name,
	)
	DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true,
		},
	})
	if err != nil {
		return err
	}
	// Auto migrate your models
	return DB.AutoMigrate(
		&models.DeployedApp{},
		&models.AppResource{},
	)
}
