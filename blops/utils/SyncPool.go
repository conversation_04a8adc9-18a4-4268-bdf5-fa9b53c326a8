package utils

import (
	"sync"
)

type Pool[T Item[T]] struct {
	p sync.Pool
}

type Default[T any] interface {
	Default() T
}

type Item[T any] interface {
	Default[T]
}

var (
	once sync.Once
)

func NewPool[T Item[T]]() *Pool[T] {
	var pl *Pool[T]
	once.Do(func() {
		pl = &Pool[T]{
			p: sync.Pool{New: func() any {
				var a T
				return a.Default()
			}},
		}
	})
	return pl
}

func (p *Pool[T]) Get() T {
	return p.p.Get().(T)
}

func (p *Pool[T]) Put(t T) {
	p.p.Put(t)
}
