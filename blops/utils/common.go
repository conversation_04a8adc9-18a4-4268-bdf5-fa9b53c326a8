package utils

import (
	"blops/enums/http"
	"blops/logger"
	"github.com/gin-gonic/gin"
	"reflect"
)

func IsNil(i interface{}) bool {
	vi := reflect.ValueOf(i)
	if vi.Kind() == reflect.Ptr {
		return vi.IsNil()
	}
	return false
}

func CheckParams[T any](c *gin.Context, t *T) error {
	err := c.ShouldBindJ<PERSON>N(&t)
	if err != nil {
		logger.Errorf("参数错误", err)
		c.JSON(http.BadRequest(http.Options{Msg: "参数错误"}).WithExtra(err.Error()))
		return err
	}
	return nil
}
