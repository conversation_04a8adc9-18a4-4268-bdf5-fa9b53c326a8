package utils

import (
	"fmt"
	"github.com/dgrijalva/jwt-go"
)

var mySigningKey = []byte("1GK8lO$1xMEer3bF")

var exp = 365 * 24 * 3600

type MyCustomClaims struct {
	Username    string `json:"username,omitempty"`
	DisplayName string `json:"displayName,omitempty"`
	jwt.StandardClaims
}

func SignJwtToken(claims MyCustomClaims) (string, error) {
	claims.ExpiresAt = int64(exp)
	tk := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return tk.SignedString(mySigningKey)
}

func ParseToken(tokenString string) *MyCustomClaims {
	//解析token
	token, err := jwt.ParseWithClaims(tokenString, &MyCustomClaims{}, func(token *jwt.Token) (interface{}, error) {
		return mySigningKey, nil
	})
	if err != nil {
		fmt.Println(err)
	}
	claims, ok := token.Claims.(*MyCustomClaims)
	if !ok {
		fmt.Printf("token is valid")
	}
	return claims
}
