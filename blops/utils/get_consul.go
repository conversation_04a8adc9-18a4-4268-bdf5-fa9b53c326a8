package utils

import (
	"fmt"
	"github.com/hashicorp/consul/api"
	"github.com/sirupsen/logrus"
)

func client() {
	var lastIndex uint64
	config := api.DefaultConfig()
	config.Address = "https://consul.blacklake.tech" //consul server

	client, err := api.NewClient(config)
	if err != nil {
		fmt.Println("api new client is failed, err:", err)
		return
	}
	services, metainfo, err := client.Health().Service("ali-test-kafka", "", true, &api.QueryOptions{
		WaitIndex: lastIndex, // 同步点，这个调用将一直阻塞，直到有新的更新
	})
	if err != nil {
		logrus.Panic("error retrieving instances from Consul:", err)
	}
	lastIndex = metainfo.LastIndex

	for _, service := range services {
		fmt.Println("service.Service.Address:", service.Service.Address, "service.Service.Port:", service.Service.Port,
			"service.Service.Tags:", service.Service.Tags)
		for _, tag := range service.Service.Tags {
			fmt.Println(tag)
		}
	}
}
