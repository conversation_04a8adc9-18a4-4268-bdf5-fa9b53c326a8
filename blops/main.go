package main

import (
	_ "blops/docs" // 导入生成的 docs
	"blops/router"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// @title           Blops API
// @version         1.0
// @description     Blops 是一个 Kubernetes 集群管理和诊断平台
// @termsOfService  http://swagger.io/terms/

// @contact.name   API Support
// @contact.url    http://www.blacklake.tech/support
// @contact.email  <EMAIL>

// @license.name  Apache 2.0
// @license.url   http://www.apache.org/licenses/LICENSE-2.0.html

// @host      localhost:3000
// @BasePath  /api

// @securityDefinitions.apikey ApiKeyAuth
// @in header
// @name X-Auth

func main() {
	// 初始化 Gin 引擎
	r := gin.Default()

	// 设置路由
	router.Generate(r)

	// 在 main.go 中使用不同的路径
	r.GET("/swagger-ui/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// 启动服务器
	r.Run(":3000")
}
