FROM harbor.blacklake.tech/infra/golang:1.20.1 as builder
ENV GOPROXY https://goproxy.cn,direct
COPY work /work
WORKDIR /work
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go mod tidy && go mod download && go build -o blops main.go

FROM centos:centos7
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
COPY --from=builder /work/blops /blops
COPY --from=builder /work/config.yml /config.yml
COPY --from=builder /work/kube /kube
COPY --from=builder /work/tmpl /tmpl
RUN chmod +x /blops
CMD ["/blops"]