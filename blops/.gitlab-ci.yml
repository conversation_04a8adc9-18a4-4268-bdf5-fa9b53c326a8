image: harbor.blacklake.tech/az/gitlab-ci-runtime:latest

variables:
  APP_NAME: "blops-api"

stages:
  - build

build-feature:
  stage: build
  only:
    - dev
    - feature
    - test
    - master
    - /^feat-*/
    - /^release-*/
  script:
    - /ci/scripts/login_harbor.sh
    - date -d @`git show -s --format=%ct` +"%Y%m%d%H%M%S" > ${CI_COMMIT_SHA}_commit_date
    - export BUILD_DATE=`cat ${CI_COMMIT_SHA}_commit_date`
    - export DOCKER_IMAGE=harbor.blacklake.tech/az/${APP_NAME}:${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHA}-${BUILD_DATE}
    - mkdir /work
    - cp -a * /work
    - cp -a /work work
    - docker build -t ${DOCKER_IMAGE} .
    - docker push ${DOCKER_IMAGE}
