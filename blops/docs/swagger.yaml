basePath: /api
definitions:
  models.AIDiagnosisRequest:
    properties:
      cluster:
        example: ali-test
        type: string
      events:
        items: {}
        type: array
      namespace:
        example: default
        type: string
    type: object
  models.AlertHandlerRequest:
    properties:
      action:
        example: resolve
        type: string
      alert_id:
        example: "1"
        type: string
      comment:
        example: 已处理
        type: string
    type: object
  models.AlertRequest:
    properties:
      description:
        example: CPU使用率超过80%
        type: string
      level:
        example: warning
        type: string
      name:
        example: CPU使用率过高
        type: string
      rule:
        example: cpu_usage > 80
        type: string
    type: object
  models.AlertSaveRequest:
    properties:
      description:
        example: CPU使用率超过80%
        type: string
      id:
        example: "1"
        type: string
      level:
        example: warning
        type: string
      name:
        example: CPU使用率过高
        type: string
      rule:
        example: cpu_usage > 80
        type: string
    type: object
  models.ClusterRequest:
    properties:
      cluster_id:
        example: ali-test
        type: string
    type: object
  models.CreateResourceRequest:
    properties:
      cluster_id:
        example: ali-test
        type: string
      yaml:
        example: |-
          apiVersion: v1
          kind: Pod
          metadata:
            name: nginx
          spec:
            containers:
            - name: nginx
              image: nginx:latest
        type: string
    type: object
  models.DeleteResourceRequest:
    properties:
      cluster_id:
        example: ali-test
        type: string
      kind:
        example: Pod
        type: string
      name:
        example: nginx-pod
        type: string
      namespace:
        example: default
        type: string
    type: object
  models.DeploymentRequest:
    properties:
      cluster_id:
        example: ali-test
        type: string
      deployment:
        example: nginx-deployment
        type: string
      namespace:
        example: default
        type: string
    type: object
  models.HostListRequest:
    properties:
      page:
        example: 1
        type: integer
      page_size:
        example: 10
        type: integer
      search:
        example: ""
        type: string
    type: object
  models.LoginRequest:
    properties:
      password:
        example: password
        type: string
      username:
        example: admin
        type: string
    type: object
  models.NamespaceRequest:
    properties:
      cluster_id:
        example: ali-test
        type: string
      namespace:
        example: default
        type: string
    type: object
  models.ResourceRequest:
    properties:
      cluster_id:
        example: ali-test
        type: string
      kind:
        example: Pod
        type: string
      name:
        example: nginx-pod
        type: string
      namespace:
        example: default
        type: string
    type: object
  models.Response:
    properties:
      code:
        example: 200
        type: integer
      data: {}
      message:
        example: 操作成功
        type: string
    type: object
  models.RuleListRequest:
    properties:
      page:
        example: 1
        type: integer
      page_size:
        example: 10
        type: integer
      search:
        example: ""
        type: string
    type: object
  models.RuleRequest:
    properties:
      description:
        example: CPU相关规则
        type: string
      name:
        example: CPU规则
        type: string
      rules:
        example: cpu_usage > 80
        type: string
    type: object
  models.RuleUpdateRequest:
    properties:
      description:
        example: CPU相关规则
        type: string
      id:
        example: "1"
        type: string
      name:
        example: CPU规则
        type: string
      rules:
        example: cpu_usage > 80
        type: string
    type: object
  models.TemplateListRequest:
    properties:
      page:
        example: 1
        type: integer
      page_size:
        example: 10
        type: integer
      search:
        example: ""
        type: string
    type: object
  models.TemplateRequest:
    properties:
      content:
        example: 服务器 {{.hostname}} CPU使用率为 {{.cpu_usage}}%
        type: string
      description:
        example: 告警通知模板
        type: string
      name:
        example: 告警模板
        type: string
    type: object
  models.TemplateUpdateRequest:
    properties:
      content:
        example: 服务器 {{.hostname}} CPU使用率为 {{.cpu_usage}}%
        type: string
      description:
        example: 告警通知模板
        type: string
      id:
        example: "1"
        type: string
      name:
        example: 告警模板
        type: string
    type: object
  models.UpdateResourceRequest:
    properties:
      cluster_id:
        example: ali-test
        type: string
      yaml:
        example: |-
          apiVersion: v1
          kind: Pod
          metadata:
            name: nginx
          spec:
            containers:
            - name: nginx
              image: nginx:1.19
        type: string
    type: object
host: localhost:3000
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.blacklake.tech/support
  description: Blops 是一个 Kubernetes 集群管理和诊断平台
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: Blops API
  version: "1.0"
paths:
  /ai/diagnosis:
    post:
      consumes:
      - application/json
      description: 分析Kubernetes集群中的事件并提供诊断结果
      parameters:
      - description: 诊断请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.AIDiagnosisRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 诊断成功
          schema:
            $ref: '#/definitions/models.Response'
        "400":
          description: 无效的请求参数
          schema:
            $ref: '#/definitions/models.Response'
        "500":
          description: AI分析失败
          schema:
            $ref: '#/definitions/models.Response'
      security:
      - ApiKeyAuth: []
      summary: AI诊断分析
      tags:
      - AI诊断
  /cluster/{cluster_id}/namespace/{namespace}/events:
    get:
      consumes:
      - application/json
      description: 获取指定集群和命名空间中的所有事件
      parameters:
      - description: 集群ID
        in: path
        name: cluster_id
        required: true
        type: string
      - description: 命名空间
        in: path
        name: namespace
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取事件列表成功
          schema:
            $ref: '#/definitions/models.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/models.Response'
        "500":
          description: 获取事件列表失败
          schema:
            $ref: '#/definitions/models.Response'
      security:
      - ApiKeyAuth: []
      summary: 获取命名空间事件
      tags:
      - 集群管理
  /cluster/{cluster_id}/namespaces:
    get:
      consumes:
      - application/json
      description: 获取指定集群中的所有命名空间
      parameters:
      - description: 集群ID
        in: path
        name: cluster_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取命名空间列表成功
          schema:
            $ref: '#/definitions/models.Response'
        "400":
          description: 集群ID不能为空
          schema:
            $ref: '#/definitions/models.Response'
        "500":
          description: 获取命名空间列表失败
          schema:
            $ref: '#/definitions/models.Response'
      security:
      - ApiKeyAuth: []
      summary: 获取命名空间列表
      tags:
      - 集群管理
  /cluster/list:
    get:
      consumes:
      - application/json
      description: 获取所有可用的Kubernetes集群列表
      produces:
      - application/json
      responses:
        "200":
          description: 获取集群列表成功
          schema:
            $ref: '#/definitions/models.Response'
      security:
      - ApiKeyAuth: []
      summary: 获取集群列表
      tags:
      - 集群管理
  /v1/alert/all:
    post:
      consumes:
      - application/json
      description: 添加新的告警
      parameters:
      - description: 告警请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.AlertRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 添加告警成功
          schema:
            $ref: '#/definitions/models.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.Response'
        "500":
          description: 添加告警失败
          schema:
            $ref: '#/definitions/models.Response'
      security:
      - ApiKeyAuth: []
      summary: 添加告警
      tags:
      - 告警管理
  /v1/alert/conf:
    post:
      consumes:
      - application/json
      description: 添加新的告警规则
      parameters:
      - description: 规则请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.RuleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 添加规则成功
          schema:
            $ref: '#/definitions/models.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.Response'
        "500":
          description: 添加规则失败
          schema:
            $ref: '#/definitions/models.Response'
      security:
      - ApiKeyAuth: []
      summary: 添加规则
      tags:
      - 告警管理
  /v1/alert/conf/_list:
    post:
      consumes:
      - application/json
      description: 获取告警规则列表
      parameters:
      - description: 规则列表请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.RuleListRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 获取规则成功
          schema:
            $ref: '#/definitions/models.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.Response'
        "500":
          description: 获取规则失败
          schema:
            $ref: '#/definitions/models.Response'
      security:
      - ApiKeyAuth: []
      summary: 获取规则
      tags:
      - 告警管理
  /v1/alert/conf/_update:
    post:
      consumes:
      - application/json
      description: 更新现有的告警规则
      parameters:
      - description: 规则更新请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.RuleUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新规则成功
          schema:
            $ref: '#/definitions/models.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.Response'
        "500":
          description: 更新规则失败
          schema:
            $ref: '#/definitions/models.Response'
      security:
      - ApiKeyAuth: []
      summary: 更新规则
      tags:
      - 告警管理
  /v1/alert/handler:
    post:
      consumes:
      - application/json
      description: 处理接收到的告警
      parameters:
      - description: 告警处理请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.AlertHandlerRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 处理告警成功
          schema:
            $ref: '#/definitions/models.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.Response'
        "500":
          description: 处理告警失败
          schema:
            $ref: '#/definitions/models.Response'
      security:
      - ApiKeyAuth: []
      summary: 告警处理
      tags:
      - 告警管理
  /v1/alert/list:
    post:
      consumes:
      - application/json
      description: 保存告警配置
      parameters:
      - description: 告警保存请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.AlertSaveRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 保存告警成功
          schema:
            $ref: '#/definitions/models.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.Response'
        "500":
          description: 保存告警失败
          schema:
            $ref: '#/definitions/models.Response'
      security:
      - ApiKeyAuth: []
      summary: 保存告警
      tags:
      - 告警管理
  /v1/alert/template:
    post:
      consumes:
      - application/json
      description: 添加新的告警模板
      parameters:
      - description: 模板请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.TemplateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 添加模板成功
          schema:
            $ref: '#/definitions/models.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.Response'
        "500":
          description: 添加模板失败
          schema:
            $ref: '#/definitions/models.Response'
      security:
      - ApiKeyAuth: []
      summary: 添加模板
      tags:
      - 告警管理
  /v1/alert/template/_list:
    post:
      consumes:
      - application/json
      description: 获取告警模板列表
      parameters:
      - description: 模板列表请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.TemplateListRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 获取模板成功
          schema:
            $ref: '#/definitions/models.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.Response'
        "500":
          description: 获取模板失败
          schema:
            $ref: '#/definitions/models.Response'
      security:
      - ApiKeyAuth: []
      summary: 获取模板
      tags:
      - 告警管理
  /v1/alert/template/_update:
    post:
      consumes:
      - application/json
      description: 更新现有的告警模板
      parameters:
      - description: 模板更新请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.TemplateUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新模板成功
          schema:
            $ref: '#/definitions/models.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.Response'
        "500":
          description: 更新模板失败
          schema:
            $ref: '#/definitions/models.Response'
      security:
      - ApiKeyAuth: []
      summary: 更新模板
      tags:
      - 告警管理
  /v1/alert/tree:
    get:
      consumes:
      - application/json
      description: 获取告警规则的树形结构
      produces:
      - application/json
      responses:
        "200":
          description: 获取树结构成功
          schema:
            $ref: '#/definitions/models.Response'
        "500":
          description: 获取树结构失败
          schema:
            $ref: '#/definitions/models.Response'
      security:
      - ApiKeyAuth: []
      summary: 获取树结构
      tags:
      - 告警管理
  /v1/cluster/deployment/list:
    post:
      consumes:
      - application/json
      description: 获取指定集群和命名空间中的所有部署
      parameters:
      - description: 命名空间请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.NamespaceRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 获取部署列表成功
          schema:
            $ref: '#/definitions/models.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.Response'
        "500":
          description: 获取部署列表失败
          schema:
            $ref: '#/definitions/models.Response'
      security:
      - ApiKeyAuth: []
      summary: 获取部署列表
      tags:
      - 集群管理
  /v1/cluster/deployment/pods:
    post:
      consumes:
      - application/json
      description: 获取指定部署的所有Pod
      parameters:
      - description: 部署请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.DeploymentRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 获取Pod列表成功
          schema:
            $ref: '#/definitions/models.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.Response'
        "500":
          description: 获取Pod列表失败
          schema:
            $ref: '#/definitions/models.Response'
      security:
      - ApiKeyAuth: []
      summary: 获取部署的Pod列表
      tags:
      - 集群管理
  /v1/cluster/ingress/list:
    post:
      consumes:
      - application/json
      description: 获取指定集群和命名空间中的所有入口
      parameters:
      - description: 命名空间请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.NamespaceRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 获取入口列表成功
          schema:
            $ref: '#/definitions/models.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.Response'
        "500":
          description: 获取入口列表失败
          schema:
            $ref: '#/definitions/models.Response'
      security:
      - ApiKeyAuth: []
      summary: 获取入口列表
      tags:
      - 集群管理
  /v1/cluster/list:
    post:
      consumes:
      - application/json
      description: 获取所有可用的Kubernetes集群列表
      produces:
      - application/json
      responses:
        "200":
          description: 获取集群列表成功
          schema:
            $ref: '#/definitions/models.Response'
        "500":
          description: 获取集群列表失败
          schema:
            $ref: '#/definitions/models.Response'
      security:
      - ApiKeyAuth: []
      summary: 获取集群列表
      tags:
      - 集群管理
  /v1/cluster/namespace/list:
    post:
      consumes:
      - application/json
      description: 获取指定集群中的所有命名空间
      parameters:
      - description: 集群请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.ClusterRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 获取命名空间列表成功
          schema:
            $ref: '#/definitions/models.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.Response'
        "500":
          description: 获取命名空间列表失败
          schema:
            $ref: '#/definitions/models.Response'
      security:
      - ApiKeyAuth: []
      summary: 获取命名空间列表
      tags:
      - 集群管理
  /v1/cluster/resource/create:
    post:
      consumes:
      - application/json
      description: 创建Kubernetes资源
      parameters:
      - description: 创建资源请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.CreateResourceRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 创建资源成功
          schema:
            $ref: '#/definitions/models.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.Response'
        "500":
          description: 创建资源失败
          schema:
            $ref: '#/definitions/models.Response'
      security:
      - ApiKeyAuth: []
      summary: 创建资源
      tags:
      - 集群管理
  /v1/cluster/resource/delete:
    post:
      consumes:
      - application/json
      description: 删除Kubernetes资源
      parameters:
      - description: 删除资源请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.DeleteResourceRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 删除资源成功
          schema:
            $ref: '#/definitions/models.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.Response'
        "500":
          description: 删除资源失败
          schema:
            $ref: '#/definitions/models.Response'
      security:
      - ApiKeyAuth: []
      summary: 删除资源
      tags:
      - 集群管理
  /v1/cluster/resource/get:
    post:
      consumes:
      - application/json
      description: 获取指定类型的Kubernetes资源
      parameters:
      - description: 资源请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.ResourceRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 获取资源成功
          schema:
            $ref: '#/definitions/models.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.Response'
        "500":
          description: 获取资源失败
          schema:
            $ref: '#/definitions/models.Response'
      security:
      - ApiKeyAuth: []
      summary: 获取资源
      tags:
      - 集群管理
  /v1/cluster/resource/update:
    post:
      consumes:
      - application/json
      description: 更新Kubernetes资源
      parameters:
      - description: 更新资源请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.UpdateResourceRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新资源成功
          schema:
            $ref: '#/definitions/models.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.Response'
        "500":
          description: 更新资源失败
          schema:
            $ref: '#/definitions/models.Response'
      security:
      - ApiKeyAuth: []
      summary: 更新资源
      tags:
      - 集群管理
  /v1/cluster/service/list:
    post:
      consumes:
      - application/json
      description: 获取指定集群和命名空间中的所有服务
      parameters:
      - description: 命名空间请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.NamespaceRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 获取服务列表成功
          schema:
            $ref: '#/definitions/models.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.Response'
        "500":
          description: 获取服务列表失败
          schema:
            $ref: '#/definitions/models.Response'
      security:
      - ApiKeyAuth: []
      summary: 获取服务列表
      tags:
      - 集群管理
  /v1/pod/exec:
    get:
      consumes:
      - application/json
      description: 在指定的 Pod 中执行命令
      parameters:
      - description: 集群ID
        in: query
        name: cluster_id
        required: true
        type: string
      - description: 命名空间
        in: query
        name: namespace
        required: true
        type: string
      - description: Pod名称
        in: query
        name: pod_name
        required: true
        type: string
      - description: 容器名称
        in: query
        name: container
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 执行成功
          schema:
            $ref: '#/definitions/models.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.Response'
        "500":
          description: 执行失败
          schema:
            $ref: '#/definitions/models.Response'
      security:
      - ApiKeyAuth: []
      summary: Pod 执行命令
      tags:
      - Pod管理
  /v1/resource/host/_list:
    post:
      consumes:
      - application/json
      description: 获取所有可用的主机列表
      parameters:
      - description: 主机列表请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.HostListRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 获取主机列表成功
          schema:
            $ref: '#/definitions/models.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.Response'
        "500":
          description: 获取主机列表失败
          schema:
            $ref: '#/definitions/models.Response'
      security:
      - ApiKeyAuth: []
      summary: 获取主机列表
      tags:
      - 资源管理
  /v1/user/_login:
    post:
      consumes:
      - application/json
      description: 用户登录并获取认证令牌
      parameters:
      - description: 登录请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 登录成功
          schema:
            $ref: '#/definitions/models.Response'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/models.Response'
        "401":
          description: 认证失败
          schema:
            $ref: '#/definitions/models.Response'
        "500":
          description: 登录失败
          schema:
            $ref: '#/definitions/models.Response'
      summary: 用户登录
      tags:
      - 用户管理
securityDefinitions:
  ApiKeyAuth:
    in: header
    name: X-Auth
    type: apiKey
swagger: "2.0"
