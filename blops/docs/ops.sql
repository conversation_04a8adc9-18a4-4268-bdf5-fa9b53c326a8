use blops_admin;

CREATE TABLE `t_host_group`
(
    `id`         BIGINT       NOT NULL AUTO_INCREMENT comment '自增ID',
    `name`       VARCHAR(255) NOT NULL comment '分组名称',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY uniq_name (`name`)
);

CREATE TABLE `t_host_group_relation`
(
    `id`         BIGINT NOT NULL AUTO_INCREMENT comment '自增ID',
    `group_id`   BIGINT NOT NULL comment '主机分组Id',
    `host_id`    BIGINT NOT NULL comment '主机Id',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY uniq_group_host_id (`group_id`, `host_id`)
);

CREATE TABLE `t_host`
(
    `id`         BIGINT       NOT NULL AUTO_INCREMENT comment '自增ID',
    `name`       VARCHAR(255) NOT NULL comment '主机名',
    `status`     TINYINT      NOT NULL default 2 comment '主机状态：0停用 1暂停 2正常',
    `os`         VARCHAR(255) NOT NULL default '' comment '操作系统',
    `created_at` DATETIME              DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME              DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
);

CREATE TABLE `t_host_ip`
(
    `id`         BIGINT       NOT NULL AUTO_INCREMENT comment '自增ID',
    `ip`         VARCHAR(255) NOT NULL comment '主机ip',
    `host_id`    BIGINT       NOT NULL comment '主机id',
    `type`       TINYINT      NOT NULL comment 'ip类型：0内网 1外网',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY uniq_ip_type (`ip`, `type`)
);

CREATE TABLE `t_alert`
(
    `id`         bigint(20)    NOT NULL AUTO_INCREMENT COMMENT '唯一Id',
    `level`      varchar(8)    NOT NULL DEFAULT '' COMMENT '事件级别',
    `status`     varchar(16)   NOT NULL COMMENT '告警状态',
    `message`    varchar(1024) NOT NULL DEFAULT '' COMMENT '告警信息',
    `instance`   varchar(64)   NOT NULL DEFAULT '' COMMENT '主机名称',
    `address`    varchar(64)   NOT NULL DEFAULT '' COMMENT '告警地址',
    `created_at` DATETIME               DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME               DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `receiver`   varchar(16)   NOT NULL DEFAULT 'unknown_receiver' COMMENT '告警类别',
    `alert_name` varchar(256)  NOT NULL DEFAULT 'unknown_alert_name' COMMENT '告警name',
    `cluster`    varchar(32)   NOT NULL DEFAULT 'unknown_cluster' COMMENT '集群',
    PRIMARY KEY (`id`)
);

CREATE TABLE `t_rule`
(
    `id`           BIGINT        NOT NULL AUTO_INCREMENT comment '自增ID',
    `finger_print` varchar(64)   NOT NULL COMMENT 'alert指纹',
    `alert_name`   VARCHAR(255)  NOT NULL comment '告警名称',
    `types`        VARCHAR(255)  NOT NULL comment '告警类型',
    `message`      VARCHAR(1024) NOT NULL comment '消息',
    `expr`         VARCHAR(1024) NOT NULL comment '表达式',
    `symbol`       VARCHAR(1024) NOT NULL comment '符号',
    `value`        VARCHAR(1024) NOT NULL comment '阈值',
    `receiver`     VARCHAR(255)  NOT NULL comment '接收者',
    `roles`        VARCHAR(255)  NOT NULL comment '角色',
    `cluster`      varchar(32)   NOT NULL DEFAULT 'unknown_cluster' COMMENT '集群',
    `started_at`   varchar(32)            DEFAULT NULL COMMENT '发生时间',
    `ended_at`     varchar(32)            DEFAULT NULL COMMENT '恢复时间',
    `created_at`   DATETIME               DEFAULT CURRENT_TIMESTAMP,
    `updated_at`   DATETIME               DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
);

alter table t_rule
    add index `idx_cluster` (`alert_name`, `types`);

alter table t_rule
    add column `template_id` bigint not null comment '模版id';

CREATE TABLE `t_expr`
(
    `id`         BIGINT        NOT NULL AUTO_INCREMENT comment '自增ID',
    `alert_name` VARCHAR(255)  NOT NULL comment '告警名称',
    `alert_code` VARCHAR(255)  NOT NULL comment '告警标识',
    `message`    VARCHAR(1024) NOT NULL comment '消息',
    `expr`       VARCHAR(1024) NOT NULL comment '表达式',
    `perch`      text comment '占位符',
    `created_at` DATETIME               DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME               DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at` BIGINT        NOT NULL DEFAULT 0 comment '删除时间, >0已删除',
    PRIMARY KEY (`id`),
    index `idx_alert_name_code_deleted_at` (`alert_name`, `alert_code`, `deleted_at`)
);

alter table t_expr
    modify column `perch` text comment '占位符';

alter table t_expr
    add column `deleted_at` BIGINT NOT NULL DEFAULT 0 comment '删除时间, >0已删除';

alter table t_expr
    add index `idx_alert_name_code_deleted_at` (`alert_name`, `alert_code`, `deleted_at`);

CREATE TABLE `t_tree`
(
    `id`    BIGINT        NOT NULL AUTO_INCREMENT comment '自增ID',
    `key`   VARCHAR(255)  NOT NULL comment '告警名称',
    `value` VARCHAR(255)  NOT NULL comment '告警标识',
    `title` VARCHAR(1024) NOT NULL comment '描述',
    PRIMARY KEY (`id`)
);

CREATE TABLE `t_leaf`
(
    `id`      BIGINT        NOT NULL AUTO_INCREMENT UNIQUE comment '自增ID',
    `key`     VARCHAR(1024) NOT NULL comment '告警名称',
    `value`   VARCHAR(1024) NOT NULL comment '告警标识',
    `title`   VARCHAR(1024) NOT NULL comment '描述',
    `tree_id` BIGINT,
    foreign key (tree_id) references t_tree (id)
);

create table `t_alert_base_chat`
(
    `id`       BIGINT       NOT NULL AUTO_INCREMENT comment '主键',
    `env`      VARCHAR(16)  not null comment 'label env',
    `receiver` VARCHAR(32)  not null comment 'label receiver',
    `level`    VARCHAR(16)  not null comment 'label appid',
    `chat_id`  VARCHAR(128) NOT NULL comment '群id',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP comment '更新时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP comment '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_env_receiver_level` (`env`, `receiver`, `level`)
) comment 'base告警群组';

create table `t_alert_app_chat`
(
    `id`       BIGINT       NOT NULL AUTO_INCREMENT comment '主键',
    `env`      VARCHAR(16)  not null comment 'label env',
    `appid`    VARCHAR(128) not null comment 'label appid',
    `chat_id`  VARCHAR(128) NOT NULL comment '群id',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP comment '更新时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP comment '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_env_appid` (`env`, `appid`)
) comment 'app告警群组';

CREATE TABLE `on_duty`
(
    `id`         BIGINT      NOT NULL AUTO_INCREMENT comment '唯一id',
    `user_type`  VARCHAR(16) NOT NULL comment '人员类型：dba ops运维 mid中间件 bd大数据',
    `user_id`    BIGINT      not null comment '用户id',
    `status`     VARCHAR(3)  NOT NULL DEFAULT 'off' comment '当前是否值班off否on是',
    `start`      bigint      NOT NULL default 0 comment '开始时间',
    `end`        bigint      NOT NULL default 0 comment '截止时间',
    created_at   DATETIME             DEFAULT CURRENT_TIMESTAMP comment '创建时间',
    updated_at   DATETIME             DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP comment '更新时间',
    `deleted_at` BIGINT      NOT NULL DEFAULT 0 comment '删除时间, >0已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_type_user_id` (`user_type`, `user_id`)
) comment '值班管理';

select * from t_user;

insert into on_duty  (user_type, user_id, `status`) values ('mid', '1', 'on'), ('mid', '');
