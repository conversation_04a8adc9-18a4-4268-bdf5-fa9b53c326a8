// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.blacklake.tech/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Apache 2.0",
            "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/ai/diagnosis": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "分析Kubernetes集群中的事件并提供诊断结果",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AI诊断"
                ],
                "summary": "AI诊断分析",
                "parameters": [
                    {
                        "description": "诊断请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.AIDiagnosisRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "诊断成功",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "400": {
                        "description": "无效的请求参数",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "500": {
                        "description": "AI分析失败",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    }
                }
            }
        },
        "/cluster/list": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取所有可用的Kubernetes集群列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "集群管理"
                ],
                "summary": "获取集群列表",
                "responses": {
                    "200": {
                        "description": "获取集群列表成功",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    }
                }
            }
        },
        "/cluster/{cluster_id}/namespace/{namespace}/events": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取指定集群和命名空间中的所有事件",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "集群管理"
                ],
                "summary": "获取命名空间事件",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群ID",
                        "name": "cluster_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "namespace",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取事件列表成功",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "500": {
                        "description": "获取事件列表失败",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    }
                }
            }
        },
        "/cluster/{cluster_id}/namespaces": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取指定集群中的所有命名空间",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "集群管理"
                ],
                "summary": "获取命名空间列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群ID",
                        "name": "cluster_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取命名空间列表成功",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "400": {
                        "description": "集群ID不能为空",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "500": {
                        "description": "获取命名空间列表失败",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    }
                }
            }
        },
        "/v1/alert/all": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "添加新的告警",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警管理"
                ],
                "summary": "添加告警",
                "parameters": [
                    {
                        "description": "告警请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.AlertRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "添加告警成功",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "500": {
                        "description": "添加告警失败",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    }
                }
            }
        },
        "/v1/alert/conf": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "添加新的告警规则",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警管理"
                ],
                "summary": "添加规则",
                "parameters": [
                    {
                        "description": "规则请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.RuleRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "添加规则成功",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "500": {
                        "description": "添加规则失败",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    }
                }
            }
        },
        "/v1/alert/conf/_list": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取告警规则列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警管理"
                ],
                "summary": "获取规则",
                "parameters": [
                    {
                        "description": "规则列表请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.RuleListRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取规则成功",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "500": {
                        "description": "获取规则失败",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    }
                }
            }
        },
        "/v1/alert/conf/_update": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "更新现有的告警规则",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警管理"
                ],
                "summary": "更新规则",
                "parameters": [
                    {
                        "description": "规则更新请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.RuleUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新规则成功",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "500": {
                        "description": "更新规则失败",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    }
                }
            }
        },
        "/v1/alert/handler": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "处理接收到的告警",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警管理"
                ],
                "summary": "告警处理",
                "parameters": [
                    {
                        "description": "告警处理请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.AlertHandlerRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "处理告警成功",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "500": {
                        "description": "处理告警失败",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    }
                }
            }
        },
        "/v1/alert/list": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "保存告警配置",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警管理"
                ],
                "summary": "保存告警",
                "parameters": [
                    {
                        "description": "告警保存请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.AlertSaveRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "保存告警成功",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "500": {
                        "description": "保存告警失败",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    }
                }
            }
        },
        "/v1/alert/template": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "添加新的告警模板",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警管理"
                ],
                "summary": "添加模板",
                "parameters": [
                    {
                        "description": "模板请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.TemplateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "添加模板成功",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "500": {
                        "description": "添加模板失败",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    }
                }
            }
        },
        "/v1/alert/template/_list": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取告警模板列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警管理"
                ],
                "summary": "获取模板",
                "parameters": [
                    {
                        "description": "模板列表请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.TemplateListRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取模板成功",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "500": {
                        "description": "获取模板失败",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    }
                }
            }
        },
        "/v1/alert/template/_update": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "更新现有的告警模板",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警管理"
                ],
                "summary": "更新模板",
                "parameters": [
                    {
                        "description": "模板更新请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.TemplateUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新模板成功",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "500": {
                        "description": "更新模板失败",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    }
                }
            }
        },
        "/v1/alert/tree": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取告警规则的树形结构",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "告警管理"
                ],
                "summary": "获取树结构",
                "responses": {
                    "200": {
                        "description": "获取树结构成功",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "500": {
                        "description": "获取树结构失败",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    }
                }
            }
        },
        "/v1/cluster/deployment/list": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取指定集群和命名空间中的所有部署",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "集群管理"
                ],
                "summary": "获取部署列表",
                "parameters": [
                    {
                        "description": "命名空间请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.NamespaceRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取部署列表成功",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "500": {
                        "description": "获取部署列表失败",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    }
                }
            }
        },
        "/v1/cluster/deployment/pods": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取指定部署的所有Pod",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "集群管理"
                ],
                "summary": "获取部署的Pod列表",
                "parameters": [
                    {
                        "description": "部署请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.DeploymentRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取Pod列表成功",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "500": {
                        "description": "获取Pod列表失败",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    }
                }
            }
        },
        "/v1/cluster/ingress/list": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取指定集群和命名空间中的所有入口",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "集群管理"
                ],
                "summary": "获取入口列表",
                "parameters": [
                    {
                        "description": "命名空间请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.NamespaceRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取入口列表成功",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "500": {
                        "description": "获取入口列表失败",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    }
                }
            }
        },
        "/v1/cluster/list": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取所有可用的Kubernetes集群列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "集群管理"
                ],
                "summary": "获取集群列表",
                "responses": {
                    "200": {
                        "description": "获取集群列表成功",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "500": {
                        "description": "获取集群列表失败",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    }
                }
            }
        },
        "/v1/cluster/namespace/list": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取指定集群中的所有命名空间",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "集群管理"
                ],
                "summary": "获取命名空间列表",
                "parameters": [
                    {
                        "description": "集群请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.ClusterRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取命名空间列表成功",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "500": {
                        "description": "获取命名空间列表失败",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    }
                }
            }
        },
        "/v1/cluster/resource/create": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "创建Kubernetes资源",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "集群管理"
                ],
                "summary": "创建资源",
                "parameters": [
                    {
                        "description": "创建资源请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.CreateResourceRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建资源成功",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "500": {
                        "description": "创建资源失败",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    }
                }
            }
        },
        "/v1/cluster/resource/delete": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "删除Kubernetes资源",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "集群管理"
                ],
                "summary": "删除资源",
                "parameters": [
                    {
                        "description": "删除资源请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.DeleteResourceRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除资源成功",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "500": {
                        "description": "删除资源失败",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    }
                }
            }
        },
        "/v1/cluster/resource/get": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取指定类型的Kubernetes资源",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "集群管理"
                ],
                "summary": "获取资源",
                "parameters": [
                    {
                        "description": "资源请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.ResourceRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取资源成功",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "500": {
                        "description": "获取资源失败",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    }
                }
            }
        },
        "/v1/cluster/resource/update": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "更新Kubernetes资源",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "集群管理"
                ],
                "summary": "更新资源",
                "parameters": [
                    {
                        "description": "更新资源请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.UpdateResourceRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新资源成功",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "500": {
                        "description": "更新资源失败",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    }
                }
            }
        },
        "/v1/cluster/service/list": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取指定集群和命名空间中的所有服务",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "集群管理"
                ],
                "summary": "获取服务列表",
                "parameters": [
                    {
                        "description": "命名空间请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.NamespaceRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取服务列表成功",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "500": {
                        "description": "获取服务列表失败",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    }
                }
            }
        },
        "/v1/pod/exec": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "在指定的 Pod 中执行命令",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Pod管理"
                ],
                "summary": "Pod 执行命令",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群ID",
                        "name": "cluster_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "namespace",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Pod名称",
                        "name": "pod_name",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "容器名称",
                        "name": "container",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "执行成功",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "500": {
                        "description": "执行失败",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    }
                }
            }
        },
        "/v1/resource/host/_list": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取所有可用的主机列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "资源管理"
                ],
                "summary": "获取主机列表",
                "parameters": [
                    {
                        "description": "主机列表请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.HostListRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取主机列表成功",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "500": {
                        "description": "获取主机列表失败",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    }
                }
            }
        },
        "/v1/user/_login": {
            "post": {
                "description": "用户登录并获取认证令牌",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "用户登录",
                "parameters": [
                    {
                        "description": "登录请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.LoginRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "登录成功",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "401": {
                        "description": "认证失败",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "500": {
                        "description": "登录失败",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "models.AIDiagnosisRequest": {
            "type": "object",
            "properties": {
                "cluster": {
                    "type": "string",
                    "example": "ali-test"
                },
                "events": {
                    "type": "array",
                    "items": {}
                },
                "namespace": {
                    "type": "string",
                    "example": "default"
                }
            }
        },
        "models.AlertHandlerRequest": {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "example": "resolve"
                },
                "alert_id": {
                    "type": "string",
                    "example": "1"
                },
                "comment": {
                    "type": "string",
                    "example": "已处理"
                }
            }
        },
        "models.AlertRequest": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string",
                    "example": "CPU使用率超过80%"
                },
                "level": {
                    "type": "string",
                    "example": "warning"
                },
                "name": {
                    "type": "string",
                    "example": "CPU使用率过高"
                },
                "rule": {
                    "type": "string",
                    "example": "cpu_usage \u003e 80"
                }
            }
        },
        "models.AlertSaveRequest": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string",
                    "example": "CPU使用率超过80%"
                },
                "id": {
                    "type": "string",
                    "example": "1"
                },
                "level": {
                    "type": "string",
                    "example": "warning"
                },
                "name": {
                    "type": "string",
                    "example": "CPU使用率过高"
                },
                "rule": {
                    "type": "string",
                    "example": "cpu_usage \u003e 80"
                }
            }
        },
        "models.ClusterRequest": {
            "type": "object",
            "properties": {
                "cluster_id": {
                    "type": "string",
                    "example": "ali-test"
                }
            }
        },
        "models.CreateResourceRequest": {
            "type": "object",
            "properties": {
                "cluster_id": {
                    "type": "string",
                    "example": "ali-test"
                },
                "yaml": {
                    "type": "string",
                    "example": "apiVersion: v1\nkind: Pod\nmetadata:\n  name: nginx\nspec:\n  containers:\n  - name: nginx\n    image: nginx:latest"
                }
            }
        },
        "models.DeleteResourceRequest": {
            "type": "object",
            "properties": {
                "cluster_id": {
                    "type": "string",
                    "example": "ali-test"
                },
                "kind": {
                    "type": "string",
                    "example": "Pod"
                },
                "name": {
                    "type": "string",
                    "example": "nginx-pod"
                },
                "namespace": {
                    "type": "string",
                    "example": "default"
                }
            }
        },
        "models.DeploymentRequest": {
            "type": "object",
            "properties": {
                "cluster_id": {
                    "type": "string",
                    "example": "ali-test"
                },
                "deployment": {
                    "type": "string",
                    "example": "nginx-deployment"
                },
                "namespace": {
                    "type": "string",
                    "example": "default"
                }
            }
        },
        "models.HostListRequest": {
            "type": "object",
            "properties": {
                "page": {
                    "type": "integer",
                    "example": 1
                },
                "page_size": {
                    "type": "integer",
                    "example": 10
                },
                "search": {
                    "type": "string",
                    "example": ""
                }
            }
        },
        "models.LoginRequest": {
            "type": "object",
            "properties": {
                "password": {
                    "type": "string",
                    "example": "password"
                },
                "username": {
                    "type": "string",
                    "example": "admin"
                }
            }
        },
        "models.NamespaceRequest": {
            "type": "object",
            "properties": {
                "cluster_id": {
                    "type": "string",
                    "example": "ali-test"
                },
                "namespace": {
                    "type": "string",
                    "example": "default"
                }
            }
        },
        "models.ResourceRequest": {
            "type": "object",
            "properties": {
                "cluster_id": {
                    "type": "string",
                    "example": "ali-test"
                },
                "kind": {
                    "type": "string",
                    "example": "Pod"
                },
                "name": {
                    "type": "string",
                    "example": "nginx-pod"
                },
                "namespace": {
                    "type": "string",
                    "example": "default"
                }
            }
        },
        "models.Response": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer",
                    "example": 200
                },
                "data": {},
                "message": {
                    "type": "string",
                    "example": "操作成功"
                }
            }
        },
        "models.RuleListRequest": {
            "type": "object",
            "properties": {
                "page": {
                    "type": "integer",
                    "example": 1
                },
                "page_size": {
                    "type": "integer",
                    "example": 10
                },
                "search": {
                    "type": "string",
                    "example": ""
                }
            }
        },
        "models.RuleRequest": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string",
                    "example": "CPU相关规则"
                },
                "name": {
                    "type": "string",
                    "example": "CPU规则"
                },
                "rules": {
                    "type": "string",
                    "example": "cpu_usage \u003e 80"
                }
            }
        },
        "models.RuleUpdateRequest": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string",
                    "example": "CPU相关规则"
                },
                "id": {
                    "type": "string",
                    "example": "1"
                },
                "name": {
                    "type": "string",
                    "example": "CPU规则"
                },
                "rules": {
                    "type": "string",
                    "example": "cpu_usage \u003e 80"
                }
            }
        },
        "models.TemplateListRequest": {
            "type": "object",
            "properties": {
                "page": {
                    "type": "integer",
                    "example": 1
                },
                "page_size": {
                    "type": "integer",
                    "example": 10
                },
                "search": {
                    "type": "string",
                    "example": ""
                }
            }
        },
        "models.TemplateRequest": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "string",
                    "example": "服务器 {{.hostname}} CPU使用率为 {{.cpu_usage}}%"
                },
                "description": {
                    "type": "string",
                    "example": "告警通知模板"
                },
                "name": {
                    "type": "string",
                    "example": "告警模板"
                }
            }
        },
        "models.TemplateUpdateRequest": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "string",
                    "example": "服务器 {{.hostname}} CPU使用率为 {{.cpu_usage}}%"
                },
                "description": {
                    "type": "string",
                    "example": "告警通知模板"
                },
                "id": {
                    "type": "string",
                    "example": "1"
                },
                "name": {
                    "type": "string",
                    "example": "告警模板"
                }
            }
        },
        "models.UpdateResourceRequest": {
            "type": "object",
            "properties": {
                "cluster_id": {
                    "type": "string",
                    "example": "ali-test"
                },
                "yaml": {
                    "type": "string",
                    "example": "apiVersion: v1\nkind: Pod\nmetadata:\n  name: nginx\nspec:\n  containers:\n  - name: nginx\n    image: nginx:1.19"
                }
            }
        }
    },
    "securityDefinitions": {
        "ApiKeyAuth": {
            "type": "apiKey",
            "name": "X-Auth",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:3000",
	BasePath:         "/api",
	Schemes:          []string{},
	Title:            "Blops API",
	Description:      "Blops 是一个 Kubernetes 集群管理和诊断平台",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
