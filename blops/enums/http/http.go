package http

import (
	"blops/base"
	"net/http"
)

type Options struct {
	Msg string `json:"msg"`
}

func checkMsg(ops ...Options) string {
	for _, op := range ops {
		if op.Msg != "" {
			return op.Msg
		}
	}
	return "fail or error，请求错误"
}

// Success 请求成功
func Success(data interface{}) (int, *base.ApiRes) {
	return 200, &base.ApiRes{
		Result:  "SUCCESS",
		Code:    http.StatusOK,
		Message: "success",
		Data:    data,
	}
}

// NotFound 未找到资源
func NotFound(ops ...Options) *base.ApiRes {
	return &base.ApiRes{
		Result:  "FAIL",
		Code:    http.StatusNotFound,
		Message: checkMsg(ops...),
	}
}

// BadRequest 请求失败
func BadRequest(ops ...Options) *base.ApiRes {
	return &base.ApiRes{
		Result:  "FAIL",
		Code:    http.StatusBadRequest,
		Message: checkMsg(ops...),
	}
}

// ServerError 服务错误
func ServerError(ops ...Options) *base.ApiRes {
	return &base.ApiRes{
		Result:  "ERROR",
		Code:    http.StatusInternalServerError,
		Message: checkMsg(ops...),
	}
}

// Unauthorized 权限验证失败
func Unauthorized(ops ...Options) *base.ApiRes {
	return &base.ApiRes{
		Result:  "ERROR",
		Code:    http.StatusUnauthorized,
		Message: checkMsg(ops...),
	}
}
