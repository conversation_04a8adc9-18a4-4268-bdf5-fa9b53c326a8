package feishu

type LarkContentTagType int

const (
	TEXT LarkContentTagType = iota
	DIV
	MARKDOWN
	LarkMd
	PlainText
	HR
	NOTE
	IMG
)

var LarkContentTagTypeEnum = map[LarkContentTagType]string{
	TEXT:      "text",
	DIV:       "div",
	MARKDOWN:  "markdown",
	LarkMd:    "lark_md",
	PlainText: "plain_text",
	HR:        "hr",
	NOTE:      "note",
	IMG:       "img",
}

func (m LarkContentTagType) Value() string {
	return LarkContentTagTypeEnum[m]
}
