# AI Assistant Configuration for Kubernetes Management

# General Settings
language: zh-CN  # Default language for responses
response_format: markdown  # Format for AI responses

# Kubernetes Cluster Settings
default_cluster: ali-test
available_clusters:
  - name: ali-test
    displayName: ali-test
    description: Test environment on Alibaba Cloud
  - name: ali-iot
    displayName: ali-iot
    description: IoT environment on Alibaba Cloud
  - name: ali-prod
    displayName: ali-prod
    description: Production environment on Alibaba Cloud
  - name: hwyx-prod
    displayName: hwyx-prod
    description: Production environment on Huawei Cloud
  - name: tx-prod
    displayName: tx-prod
    description: Production environment on Tencent Cloud

# Resource Types
resource_types:
  - deployment
  - service
  - ingress
  - pod
  - namespace

# Feature Settings
features:
  pod_details: true  # Enable detailed pod information for deployments
  yaml_editor: true  # Enable YAML editing for resources
  resource_creation: true  # Enable resource creation
  resource_deletion: true  # Enable resource deletion
  resource_update: true  # Enable resource updates
  
# UI Settings
ui:
  theme: light
  table_page_size: 10
  show_resource_status: true
  show_creation_time: true
  enable_quick_filters: true

# Deployment View Settings
deployment_view:
  show_replicas: true
  show_status: true
  show_pods: true
  show_containers: true
  show_images: true
  show_resource_usage: true

# Pod Details Settings
pod_details:
  show_status: true
  show_node: true
  show_ip: true
  show_restarts: true
  show_age: true
  show_logs: true
  show_events: true

# Logging Settings
logging:
  enabled: true
  level: info
  include_timestamps: true 