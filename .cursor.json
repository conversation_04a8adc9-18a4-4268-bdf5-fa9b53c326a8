{"projects": {"blops": {"projectType": "go", "formatOnSave": true, "linting": {"enabled": true, "golint": true, "errcheck": true, "gosec": true}, "build": {"command": "go build -o blops main.go", "commandDir": "${workspaceRoot}/blops"}, "run": {"command": "./blops", "commandDir": "${workspaceRoot}/blops"}, "test": {"command": "go test ./...", "commandDir": "${workspaceRoot}/blops"}, "search": {"excludePatterns": ["**/node_modules/**", "**/.git/**", "**/vendor/**", "**/.vscode/**", "**/bin/**", "**/tmp/**", "go.sum"]}, "autoComplete": {"importOnSave": true}, "filePatterns": {"go": {"formatter": "gofmt", "indent": "tab"}}, "debugConfigurations": [{"name": "Launch Blops", "type": "go", "request": "launch", "mode": "auto", "program": "${workspaceRoot}/blops/main.go", "cwd": "${workspaceRoot}/blops", "env": {}}], "tasks": [{"label": "Build Docker Image (Blops)", "command": "docker build -t blops:latest .", "type": "shell", "group": "build", "commandDir": "${workspaceRoot}/blops"}, {"label": "Generate Swagger Docs (Blops)", "command": "swag init", "type": "shell", "group": "build", "commandDir": "${workspaceRoot}/blops"}]}, "blops-web": {"projectType": "node", "formatOnSave": true, "linting": {"enabled": true, "eslint": true, "stylelint": true, "prettier": true}, "build": {"command": "npm run build", "commandDir": "${workspaceRoot}/blops-web"}, "run": {"command": "npm start", "commandDir": "${workspaceRoot}/blops-web"}, "test": {"command": "npm test", "commandDir": "${workspaceRoot}/blops-web"}, "search": {"excludePatterns": ["**/node_modules/**", "**/.git/**", "**/.vscode/**", "**/dist/**", "**/build/**"]}, "autoComplete": {"importOnSave": true}, "filePatterns": {"js": {"formatter": "prettier", "indent": "space"}, "ts": {"formatter": "prettier", "indent": "space"}, "json": {"formatter": "prettier", "indent": "space"}, "css": {"formatter": "stylelint", "indent": "space"}}, "debugConfigurations": [{"name": "Launch Blops-Web", "type": "node", "request": "launch", "program": "${workspaceRoot}/blops-web/src/index.js", "cwd": "${workspaceRoot}/blops-web", "env": {}}], "tasks": [{"label": "Build Docker Image (Blops-Web)", "command": "docker build -t blops-web:latest .", "type": "shell", "group": "build", "commandDir": "${workspaceRoot}/blops-web"}]}}}