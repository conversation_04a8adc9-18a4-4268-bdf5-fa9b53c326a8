#!/bin/bash

# Blops AI助手功能测试脚本
# 用于测试AI助手的各项API功能

set -e

# 配置
BASE_URL="http://localhost:3000"
AUTH_TOKEN="your-auth-token-here"
SESSION_ID="test-session-$(date +%s)"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v curl &> /dev/null; then
        log_error "curl 未安装，请先安装 curl"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_warning "jq 未安装，JSON输出将不会格式化"
        JQ_AVAILABLE=false
    else
        JQ_AVAILABLE=true
    fi
    
    log_success "依赖检查完成"
}

# 格式化JSON输出
format_json() {
    if [ "$JQ_AVAILABLE" = true ]; then
        echo "$1" | jq .
    else
        echo "$1"
    fi
}

# 测试服务器连接
test_connection() {
    log_info "测试服务器连接..."
    
    if curl -s --connect-timeout 5 "$BASE_URL" > /dev/null; then
        log_success "服务器连接正常"
    else
        log_error "无法连接到服务器: $BASE_URL"
        exit 1
    fi
}

# 测试快速模板接口
test_templates() {
    log_info "测试快速模板接口..."
    
    response=$(curl -s -X GET "$BASE_URL/api/ai/templates" \
        -H "Content-Type: application/json" \
        -H "X-AUTH: $AUTH_TOKEN")
    
    if echo "$response" | grep -q '"code":200'; then
        log_success "快速模板接口测试成功"
        template_count=$(echo "$response" | grep -o '"id"' | wc -l)
        log_info "获取到 $template_count 个快速模板"
        
        if [ "$JQ_AVAILABLE" = true ]; then
            echo "$response" | jq '.data[] | {id: .id, title: .title, category: .category}'
        fi
    else
        log_error "快速模板接口测试失败"
        format_json "$response"
        return 1
    fi
}

# 测试AI聊天接口
test_chat() {
    local message="$1"
    log_info "测试AI聊天接口: $message"
    
    response=$(curl -s -X POST "$BASE_URL/api/ai/chat" \
        -H "Content-Type: application/json" \
        -H "X-AUTH: $AUTH_TOKEN" \
        -d "{
            \"message\": \"$message\",
            \"sessionId\": \"$SESSION_ID\"
        }")
    
    if echo "$response" | grep -q '"code":200'; then
        log_success "AI聊天接口测试成功"
        
        if [ "$JQ_AVAILABLE" = true ]; then
            reply=$(echo "$response" | jq -r '.data.reply')
            timestamp=$(echo "$response" | jq -r '.data.timestamp')
            log_info "AI回复时间: $(date -d @$timestamp 2>/dev/null || date -r $timestamp 2>/dev/null || echo $timestamp)"
            echo "AI回复内容:"
            echo "$reply" | sed 's/<[^>]*>//g' | head -5  # 移除HTML标签并显示前5行
            echo "..."
        else
            format_json "$response"
        fi
    else
        log_error "AI聊天接口测试失败"
        format_json "$response"
        return 1
    fi
}

# 测试聊天历史接口
test_chat_history() {
    log_info "测试聊天历史接口..."
    
    response=$(curl -s -X GET "$BASE_URL/api/ai/chat/history?sessionId=$SESSION_ID" \
        -H "Content-Type: application/json" \
        -H "X-AUTH: $AUTH_TOKEN")
    
    if echo "$response" | grep -q '"code":200'; then
        log_success "聊天历史接口测试成功"
        
        if [ "$JQ_AVAILABLE" = true ]; then
            message_count=$(echo "$response" | jq '.data.messages | length')
            log_info "会话中有 $message_count 条消息"
        fi
    else
        log_error "聊天历史接口测试失败"
        format_json "$response"
        return 1
    fi
}

# 测试清空聊天历史接口
test_clear_history() {
    log_info "测试清空聊天历史接口..."
    
    response=$(curl -s -X DELETE "$BASE_URL/api/ai/chat/history?sessionId=$SESSION_ID" \
        -H "Content-Type: application/json" \
        -H "X-AUTH: $AUTH_TOKEN")
    
    if echo "$response" | grep -q '"code":200'; then
        log_success "清空聊天历史接口测试成功"
    else
        log_error "清空聊天历史接口测试失败"
        format_json "$response"
        return 1
    fi
}

# 性能测试
performance_test() {
    log_info "开始性能测试..."
    
    start_time=$(date +%s)
    test_chat "这是一个性能测试消息，请简单回复。"
    end_time=$(date +%s)
    
    duration=$((end_time - start_time))
    log_info "AI响应时间: ${duration}秒"
    
    if [ $duration -lt 30 ]; then
        log_success "性能测试通过 (< 30秒)"
    elif [ $duration -lt 60 ]; then
        log_warning "性能测试警告 (30-60秒)"
    else
        log_error "性能测试失败 (> 60秒)"
    fi
}

# 主测试流程
main() {
    echo "=================================="
    echo "Blops AI助手功能测试"
    echo "=================================="
    echo "测试时间: $(date)"
    echo "服务器: $BASE_URL"
    echo "会话ID: $SESSION_ID"
    echo "=================================="
    
    # 检查依赖
    check_dependencies
    
    # 测试连接
    test_connection
    
    # 测试各个接口
    echo ""
    log_info "开始API功能测试..."
    
    # 1. 测试快速模板
    test_templates
    echo ""
    
    # 2. 测试AI聊天
    test_chat "你好，请简单介绍一下Kubernetes。"
    echo ""
    
    # 3. 测试聊天历史
    test_chat_history
    echo ""
    
    # 4. 再发送一条消息测试上下文
    test_chat "请详细解释一下Pod的概念。"
    echo ""
    
    # 5. 性能测试
    performance_test
    echo ""
    
    # 6. 测试清空历史
    test_clear_history
    echo ""
    
    log_success "所有测试完成！"
    echo "=================================="
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示帮助信息"
    echo "  -u, --url URL       设置服务器URL (默认: http://localhost:3000)"
    echo "  -t, --token TOKEN   设置认证Token"
    echo "  -s, --session ID    设置会话ID"
    echo ""
    echo "示例:"
    echo "  $0 -u http://localhost:3000 -t your-token"
    echo "  $0 --url http://prod-server:3000 --token prod-token"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -u|--url)
            BASE_URL="$2"
            shift 2
            ;;
        -t|--token)
            AUTH_TOKEN="$2"
            shift 2
            ;;
        -s|--session)
            SESSION_ID="$2"
            shift 2
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查必要参数
if [ "$AUTH_TOKEN" = "your-auth-token-here" ]; then
    log_warning "使用默认Token，请使用 -t 参数设置有效的认证Token"
fi

# 运行主程序
main
