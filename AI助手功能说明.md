# Blops AI助手功能说明

## 功能概述

Blops AI助手是一个集成在整个应用中的全局智能问答组件，为用户提供Kubernetes集群管理和运维相关的专业指导。

## 主要特性

### 🤖 智能对话
- **专业知识**: 具备Kubernetes、容器化、云原生技术栈的专业知识
- **上下文记忆**: 支持多轮对话，记住对话历史
- **中文回答**: 提供准确、实用的中文建议和解决方案

### 🎯 快速模板
- **预设问题**: 提供8个常用问题模板
- **分类管理**: 按故障排查、性能优化、部署指导等分类
- **一键使用**: 点击模板即可快速填入问题

### 💬 聊天界面
- **悬浮按钮**: 右下角固定位置，随时可访问
- **抽屉式界面**: 不影响主要工作流程
- **响应式设计**: 支持桌面和移动设备

### 📝 历史管理
- **会话记录**: 自动保存聊天历史
- **一键清空**: 支持清空当前会话记录
- **消息限制**: 自动保留最近20条消息

## 技术实现

### 后端API

#### 1. AI聊天接口
```
POST /api/ai/chat
```
**请求参数:**
```json
{
  "message": "用户消息内容",
  "sessionId": "会话ID（可选）"
}
```

**响应格式:**
```json
{
  "code": 200,
  "message": "AI聊天成功",
  "data": {
    "reply": "AI回复内容（HTML格式）",
    "sessionId": "会话ID",
    "timestamp": 1640995200
  }
}
```

#### 2. 聊天历史接口
```
GET /api/ai/chat/history?sessionId=xxx
DELETE /api/ai/chat/history?sessionId=xxx
```

#### 3. 快速模板接口
```
GET /api/ai/templates
```

### 前端组件

#### 1. 全局AI助手组件
- **位置**: `src/components/AIAssistant/`
- **集成**: 通过`app.tsx`的`childrenRender`集成到全局布局
- **样式**: 响应式设计，支持移动端

#### 2. 服务层
- **位置**: `src/services/aiAssistant.ts`
- **功能**: 封装所有AI助手相关的API调用
- **超时**: 使用`aiRequest`实例，支持2分钟超时

#### 3. 状态管理
- **位置**: `src/models/aiAssistant.ts`
- **功能**: 使用React Hooks管理组件状态

## 快速模板列表

### 故障排查类
1. **Pod故障排查**: 帮助排查Pod启动失败、崩溃等问题
2. **网络连接问题**: 排查Pod间网络连接问题
3. **存储卷问题**: 解决PV/PVC存储相关问题

### 性能优化类
4. **资源优化建议**: 获取集群资源使用优化建议

### 部署指导类
5. **应用部署指导**: 获取应用部署的最佳实践指导

### 监控告警类
6. **监控告警配置**: 配置集群监控和告警规则

### 安全检查类
7. **安全检查建议**: 获取集群安全配置检查建议

### 平台使用类
8. **Blops平台使用**: 了解Blops平台功能使用方法

## 使用方法

### 1. 基本使用
1. 点击右下角的AI助手悬浮按钮（🤖图标）
2. 在弹出的聊天界面中输入问题
3. 按Enter键或点击发送按钮
4. 等待AI回复

### 2. 使用快速模板
1. 在聊天界面点击"模板"按钮
2. 从弹出的模板列表中选择合适的模板
3. 模板内容会自动填入输入框
4. 根据需要修改模板中的占位符（如{状态}、{错误信息}等）
5. 发送消息

### 3. 管理聊天记录
- **查看历史**: 聊天记录会自动保存并显示
- **清空记录**: 点击标题栏的清空按钮
- **自动限制**: 系统自动保留最近20条消息

## 最佳实践

### 1. 问题描述
- **具体详细**: 提供具体的错误信息、状态描述
- **包含上下文**: 说明操作步骤、环境信息
- **使用模板**: 利用快速模板确保问题描述完整

### 2. 交互技巧
- **分步询问**: 复杂问题可以分步骤询问
- **追问细节**: 对AI回复中不清楚的部分可以追问
- **验证建议**: 实施AI建议前请先在测试环境验证

### 3. 安全注意
- **敏感信息**: 不要在聊天中包含密码、密钥等敏感信息
- **生产环境**: 在生产环境执行AI建议前请谨慎评估

## 故障排除

### 1. 无法发送消息
- 检查网络连接
- 确认用户已登录
- 查看浏览器控制台错误信息

### 2. AI回复缓慢
- AI分析需要时间，请耐心等待
- 超时时间设置为2分钟
- 可以尝试简化问题描述

### 3. 回复内容异常
- 检查后端Gemini API配置
- 查看后端日志错误信息
- 确认API密钥有效性

## 开发说明

### 1. 扩展快速模板
在`ai_diagnosis_controller.go`的`GetQuickTemplates`函数中添加新模板：

```go
{
    ID:          "new-template-id",
    Title:       "模板标题",
    Description: "模板描述",
    Content:     "模板内容，支持{占位符}",
    Category:    "模板分类",
}
```

### 2. 自定义样式
修改`src/components/AIAssistant/index.less`文件来调整界面样式。

### 3. 添加新功能
1. 在后端控制器中添加新的API端点
2. 在前端服务层添加对应的API调用
3. 在组件中添加相应的UI和交互逻辑

## 更新日志

### v1.0.0 (2024-01-XX)
- ✅ 基础AI聊天功能
- ✅ 快速问题模板
- ✅ 聊天历史管理
- ✅ 全局悬浮按钮
- ✅ 响应式界面设计
- ✅ Gemini API集成

## 技术支持

如有问题或建议，请联系开发团队或在项目仓库中提交Issue。
