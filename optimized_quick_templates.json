[{"id": "pod-startup-failure", "title": "Pod启动失败诊断", "description": "全面诊断Pod无法启动的各种原因", "category": "故障排查", "content": "作为Kubernetes专家，请帮我诊断Pod启动失败问题。\n\n**环境信息：**\n- 集群版本：{集群版本}\n- 命名空间：{命名空间}\n- Pod名称：{Pod名称}\n\n**问题描述：**\n- Pod状态：{Pod状态}\n- 错误信息：{错误信息}\n- 事件信息：{事件信息}\n\n**请提供：**\n1. 问题根因分析\n2. 详细的诊断步骤和命令\n3. 具体的解决方案\n4. 预防措施建议\n\n请使用kubectl命令示例，并考虑镜像拉取、资源限制、配置错误等常见原因。"}, {"id": "pod-crashloop-backoff", "title": "Pod CrashLoopBackOff 问题", "description": "诊断和解决Pod反复崩溃重启问题", "category": "故障排查", "content": "作为Kubernetes运维专家，请帮我解决Pod CrashLoopBackOff问题。\n\n**Pod信息：**\n- Pod名称：{Pod名称}\n- 命名空间：{命名空间}\n- 重启次数：{重启次数}\n- 最后退出码：{退出码}\n\n**日志信息：**\n```\n{容器日志}\n```\n\n**需要分析：**\n1. 崩溃的根本原因\n2. 应用程序问题 vs 配置问题\n3. 资源限制是否合理\n4. 健康检查配置是否正确\n\n请提供详细的诊断命令、解决步骤和最佳实践建议。"}, {"id": "service-network-connectivity", "title": "Service网络连接问题", "description": "排查Pod间和Service访问的网络连接问题", "category": "故障排查", "content": "作为Kubernetes网络专家，请帮我排查Service网络连接问题。\n\n**网络环境：**\n- 源Pod：{源Pod}\n- 目标Service：{目标Service}\n- 命名空间：{命名空间}\n- 网络插件：{网络插件}\n\n**问题现象：**\n- 连接错误：{连接错误}\n- 超时时间：{超时时间}\n- 访问方式：{访问方式}\n\n**请诊断：**\n1. Service和Endpoints配置\n2. 网络策略限制\n3. DNS解析问题\n4. 防火墙和安全组\n5. 负载均衡配置\n\n请提供网络诊断命令、连通性测试方法和解决方案。"}, {"id": "pvc-mount-failure", "title": "存储卷挂载失败", "description": "诊断PVC挂载失败和存储相关问题", "category": "故障排查", "content": "作为Kubernetes存储专家，请帮我解决PVC挂载失败问题。\n\n**存储信息：**\n- PVC名称：{PVC名称}\n- StorageClass：{StorageClass}\n- 存储大小：{存储大小}\n- 访问模式：{访问模式}\n\n**错误信息：**\n```\n{挂载错误}\n```\n\n**环境详情：**\n- 存储后端：{存储后端}\n- 节点信息：{节点信息}\n\n**请分析：**\n1. PV/PVC绑定状态\n2. StorageClass配置\n3. 节点存储驱动\n4. 权限和安全上下文\n5. 存储后端可用性\n\n请提供详细的诊断步骤、解决方案和存储最佳实践。"}, {"id": "resource-quota-exceeded", "title": "资源配额超限问题", "description": "解决命名空间资源配额限制问题", "category": "故障排查", "content": "作为Kubernetes资源管理专家，请帮我解决资源配额超限问题。\n\n**配额信息：**\n- 命名空间：{命名空间}\n- 超限资源：{超限资源}\n- 当前使用量：{当前使用量}\n- 配额限制：{配额限制}\n\n**资源详情：**\n- CPU请求/限制：{CPU配额}\n- 内存请求/限制：{内存配额}\n- Pod数量：{Pod数量}\n- PVC数量：{PVC数量}\n\n**请提供：**\n1. 资源使用情况分析\n2. 配额调整建议\n3. 资源优化方案\n4. 监控和告警配置\n\n请包含资源查看命令、配额管理最佳实践和长期规划建议。"}, {"id": "cluster-resource-analysis", "title": "集群资源使用分析", "description": "分析集群整体资源使用情况并提供优化建议", "category": "性能优化", "content": "作为Kubernetes性能优化专家，请帮我分析集群资源使用情况。\n\n**集群概况：**\n- 节点数量：{节点数量}\n- 总CPU核数：{总CPU}\n- 总内存：{总内存}\n- Pod总数：{Pod总数}\n\n**资源使用率：**\n- CPU使用率：{CPU使用率}%\n- 内存使用率：{内存使用率}%\n- 存储使用率：{存储使用率}%\n- 网络带宽：{网络使用}\n\n**性能指标：**\n- 平均响应时间：{响应时间}\n- 错误率：{错误率}\n- 吞吐量：{吞吐量}\n\n**请提供：**\n1. 资源使用趋势分析\n2. 性能瓶颈识别\n3. 扩缩容建议\n4. 资源分配优化方案\n5. 成本优化建议\n\n请包含监控命令、性能调优最佳实践和容量规划建议。"}, {"id": "pod-resource-optimization", "title": "Pod资源配置优化", "description": "优化Pod的CPU和内存资源配置", "category": "性能优化", "content": "作为Kubernetes资源优化专家，请帮我优化Pod资源配置。\n\n**应用信息：**\n- 应用类型：{应用类型}\n- Pod副本数：{副本数}\n- 当前配置：{当前配置}\n\n**资源使用情况：**\n- CPU请求/限制：{CPU配置}\n- 内存请求/限制：{内存配置}\n- 实际CPU使用：{实际CPU使用}\n- 实际内存使用：{实际内存使用}\n\n**性能表现：**\n- 响应时间：{响应时间}\n- 吞吐量：{吞吐量}\n- 错误率：{错误率}\n- 重启次数：{重启次数}\n\n**请分析：**\n1. 资源配置是否合理\n2. 是否存在资源浪费\n3. 性能瓶颈分析\n4. 扩缩容策略建议\n5. QoS等级优化\n\n请提供具体的资源配置建议、监控指标和调优策略。"}, {"id": "hpa-configuration", "title": "水平Pod自动扩缩容配置", "description": "配置和优化HPA自动扩缩容策略", "category": "性能优化", "content": "作为Kubernetes自动扩缩容专家，请帮我配置HPA策略。\n\n**应用特征：**\n- 应用类型：{应用类型}\n- 流量模式：{流量模式}\n- 业务特点：{业务特点}\n\n**当前配置：**\n- 最小副本数：{最小副本}\n- 最大副本数：{最大副本}\n- 目标CPU利用率：{目标CPU}\n- 目标内存利用率：{目标内存}\n\n**性能需求：**\n- 预期QPS：{预期QPS}\n- 响应时间要求：{响应时间要求}\n- 可用性要求：{可用性要求}\n\n**请提供：**\n1. HPA配置最佳实践\n2. 扩缩容指标选择\n3. 扩缩容策略调优\n4. 监控和告警配置\n5. 成本控制建议\n\n请包含完整的HPA YAML配置示例和调优指南。"}, {"id": "security-rbac-audit", "title": "RBAC权限安全审计", "description": "审计和优化Kubernetes RBAC权限配置", "category": "安全检查", "content": "作为Kubernetes安全专家，请帮我进行RBAC权限安全审计。\n\n**审计范围：**\n- 命名空间：{命名空间}\n- 用户/服务账户：{账户信息}\n- 角色类型：{角色类型}\n\n**当前权限：**\n- ClusterRole：{ClusterRole}\n- Role：{Role}\n- RoleBinding：{RoleBinding}\n- ClusterRoleBinding：{ClusterRoleBinding}\n\n**安全关注点：**\n- 过度权限：{过度权限}\n- 敏感操作：{敏感操作}\n- 权限继承：{权限继承}\n\n**请检查：**\n1. 最小权限原则遵循情况\n2. 危险权限识别\n3. 权限分离和隔离\n4. 服务账户安全性\n5. 权限审计日志\n\n请提供权限优化建议、安全最佳实践和合规性检查清单。"}, {"id": "network-policy-security", "title": "网络策略安全配置", "description": "配置和验证Kubernetes网络安全策略", "category": "安全检查", "content": "作为Kubernetes网络安全专家，请帮我配置网络安全策略。\n\n**网络环境：**\n- 网络插件：{网络插件}\n- 集群网络：{集群网络}\n- 服务网络：{服务网络}\n\n**安全需求：**\n- 隔离级别：{隔离级别}\n- 访问控制：{访问控制}\n- 合规要求：{合规要求}\n\n**当前配置：**\n- 现有NetworkPolicy：{现有策略}\n- 默认行为：{默认行为}\n- 例外规则：{例外规则}\n\n**请设计：**\n1. 网络分段策略\n2. 入站流量控制\n3. 出站流量限制\n4. 跨命名空间访问\n5. 外部访问控制\n\n请提供NetworkPolicy YAML配置、测试验证方法和安全最佳实践。"}, {"id": "secret-management-security", "title": "密钥和敏感信息管理", "description": "安全管理Kubernetes中的密钥和敏感配置", "category": "安全检查", "content": "作为Kubernetes安全专家，请帮我优化密钥管理安全性。\n\n**密钥类型：**\n- Secret类型：{Secret类型}\n- 存储内容：{存储内容}\n- 使用范围：{使用范围}\n\n**当前实践：**\n- 密钥创建方式：{创建方式}\n- 访问控制：{访问控制}\n- 轮换策略：{轮换策略}\n\n**安全要求：**\n- 加密需求：{加密需求}\n- 合规标准：{合规标准}\n- 审计要求：{审计要求}\n\n**请评估：**\n1. 密钥存储安全性\n2. 访问权限控制\n3. 密钥轮换机制\n4. 静态加密配置\n5. 审计日志记录\n\n请提供密钥管理最佳实践、安全配置建议和外部密钥管理系统集成方案。"}, {"id": "namespace-resource-management", "title": "命名空间资源管理", "description": "管理和优化命名空间资源配置", "category": "资源管理", "content": "作为Kubernetes资源管理专家，请帮我优化命名空间资源管理。\n\n**命名空间信息：**\n- 命名空间：{命名空间}\n- 用途：{用途}\n- 团队：{团队}\n\n**资源配置：**\n- ResourceQuota：{ResourceQuota}\n- LimitRange：{LimitRange}\n- NetworkPolicy：{NetworkPolicy}\n\n**使用情况：**\n- Pod数量：{Pod数量}\n- 资源使用：{资源使用}\n- 存储使用：{存储使用}\n\n**管理需求：**\n- 隔离要求：{隔离要求}\n- 资源限制：{资源限制}\n- 成本控制：{成本控制}\n\n**请提供：**\n1. 资源配额设计\n2. 限制范围配置\n3. 多租户隔离策略\n4. 资源监控方案\n5. 成本分摊机制\n\n请包含完整的配置示例、管理工具推荐和最佳实践指南。"}, {"id": "node-resource-analysis", "title": "节点资源分析", "description": "分析节点资源使用情况和健康状态", "category": "资源管理", "content": "作为Kubernetes节点管理专家，请帮我分析节点资源状况。\n\n**节点信息：**\n- 节点名称：{节点名称}\n- 节点类型：{节点类型}\n- 操作系统：{操作系统}\n- 容器运行时：{容器运行时}\n\n**资源配置：**\n- CPU核数：{CPU核数}\n- 内存大小：{内存大小}\n- 存储容量：{存储容量}\n- 网络带宽：{网络带宽}\n\n**使用情况：**\n- CPU使用率：{CPU使用率}%\n- 内存使用率：{内存使用率}%\n- 磁盘使用率：{磁盘使用率}%\n- Pod数量：{Pod数量}\n\n**请分析：**\n1. 节点健康状态\n2. 资源分配效率\n3. 性能瓶颈识别\n4. 容量规划建议\n5. 节点优化方案\n\n请提供节点监控命令、性能调优建议和扩容策略。"}, {"id": "storage-class-management", "title": "存储类管理和优化", "description": "管理和优化Kubernetes存储类配置", "category": "资源管理", "content": "作为Kubernetes存储专家，请帮我优化存储类配置。\n\n**存储环境：**\n- 存储后端：{存储后端}\n- StorageClass：{StorageClass}\n- 存储类型：{存储类型}\n\n**性能要求：**\n- IOPS需求：{IOPS需求}\n- 吞吐量需求：{吞吐量需求}\n- 延迟要求：{延迟要求}\n\n**使用场景：**\n- 应用类型：{应用类型}\n- 数据特征：{数据特征}\n- 可用性要求：{可用性要求}\n\n**当前问题：**\n- 性能问题：{性能问题}\n- 成本问题：{成本问题}\n- 管理复杂性：{管理复杂性}\n\n**请提供：**\n1. StorageClass配置优化\n2. 存储性能调优\n3. 备份和恢复策略\n4. 成本优化方案\n5. 存储监控配置\n\n请包含存储类配置示例、性能测试方法和最佳实践。"}, {"id": "application-deployment-guide", "title": "应用部署最佳实践指导", "description": "提供应用在Kubernetes上部署的完整指导", "category": "部署运维", "content": "作为Kubernetes部署专家，请为我的应用提供部署指导。\n\n**应用信息：**\n- 应用类型：{应用类型}\n- 架构模式：{架构模式}\n- 技术栈：{技术栈}\n- 依赖服务：{依赖服务}\n\n**部署需求：**\n- 环境：{环境}\n- 可用性要求：{可用性要求}\n- 性能要求：{性能要求}\n- 安全要求：{安全要求}\n\n**资源需求：**\n- CPU/内存：{资源需求}\n- 存储需求：{存储需求}\n- 网络需求：{网络需求}\n\n**请提供：**\n1. 部署架构设计\n2. Kubernetes资源配置\n3. 配置管理策略\n4. 服务发现配置\n5. 健康检查设置\n6. 安全配置建议\n\n请包含完整的YAML配置示例、部署脚本和验证方法。"}, {"id": "rolling-update-strategy", "title": "滚动更新策略配置", "description": "配置安全可靠的应用滚动更新策略", "category": "部署运维", "content": "作为Kubernetes部署专家，请帮我设计滚动更新策略。\n\n**应用特征：**\n- 应用类型：{应用类型}\n- 当前版本：{当前版本}\n- 目标版本：{目标版本}\n- 副本数量：{副本数量}\n\n**业务要求：**\n- 可用性要求：{可用性要求}\n- 更新窗口：{更新窗口}\n- 回滚要求：{回滚要求}\n\n**风险评估：**\n- 兼容性风险：{兼容性风险}\n- 数据迁移：{数据迁移}\n- 依赖变更：{依赖变更}\n\n**请设计：**\n1. 滚动更新参数配置\n2. 健康检查策略\n3. 流量切换方案\n4. 监控和告警\n5. 回滚预案\n\n请提供Deployment配置、更新脚本和验证清单。"}, {"id": "rollback-operation-guide", "title": "应用回滚操作指导", "description": "快速安全地回滚应用到稳定版本", "category": "部署运维", "content": "作为Kubernetes运维专家，请指导我进行应用回滚操作。\n\n**回滚场景：**\n- 当前问题：{当前问题}\n- 影响范围：{影响范围}\n- 紧急程度：{紧急程度}\n\n**版本信息：**\n- 当前版本：{当前版本}\n- 目标版本：{目标版本}\n- 版本差异：{版本差异}\n\n**环境状态：**\n- 集群状态：{集群状态}\n- 应用状态：{应用状态}\n- 数据状态：{数据状态}\n\n**回滚要求：**\n- 时间要求：{时间要求}\n- 数据一致性：{数据一致性}\n- 服务可用性：{服务可用性}\n\n**请提供：**\n1. 回滚前检查清单\n2. 回滚操作步骤\n3. 数据处理方案\n4. 验证测试方法\n5. 风险控制措施\n\n请包含具体的kubectl命令、回滚脚本和验证方法。"}, {"id": "configmap-secret-management", "title": "配置和密钥管理", "description": "管理应用配置和敏感信息的最佳实践", "category": "部署运维", "content": "作为Kubernetes配置管理专家，请帮我优化配置管理策略。\n\n**配置类型：**\n- 应用配置：{应用配置}\n- 环境变量：{环境变量}\n- 配置文件：{配置文件}\n- 密钥信息：{密钥信息}\n\n**管理需求：**\n- 环境隔离：{环境隔离}\n- 版本控制：{版本控制}\n- 动态更新：{动态更新}\n- 安全要求：{安全要求}\n\n**当前挑战：**\n- 配置复杂性：{配置复杂性}\n- 同步问题：{同步问题}\n- 安全风险：{安全风险}\n\n**请提供：**\n1. ConfigMap设计策略\n2. Secret管理方案\n3. 配置热更新机制\n4. 版本控制集成\n5. 安全最佳实践\n\n请包含配置管理工具推荐、YAML示例和自动化方案。"}, {"id": "prometheus-monitoring-setup", "title": "Prometheus监控配置", "description": "配置Prometheus监控系统和指标收集", "category": "监控告警", "content": "作为Kubernetes监控专家，请帮我配置Prometheus监控系统。\n\n**监控需求：**\n- 监控对象：{监控对象}\n- 关键指标：{关键指标}\n- 监控粒度：{监控粒度}\n\n**环境信息：**\n- 集群规模：{集群规模}\n- 应用数量：{应用数量}\n- 数据保留：{数据保留}\n\n**性能要求：**\n- 采集频率：{采集频率}\n- 查询性能：{查询性能}\n- 存储需求：{存储需求}\n\n**请配置：**\n1. Prometheus服务器设置\n2. 服务发现配置\n3. 指标采集规则\n4. 存储和保留策略\n5. 高可用部署\n\n请提供完整的配置文件、部署脚本和监控仪表板。"}, {"id": "alertmanager-rules-setup", "title": "告警规则配置", "description": "配置智能化的Kubernetes告警规则", "category": "监控告警", "content": "作为Kubernetes告警专家，请帮我配置告警规则。\n\n**告警场景：**\n- 系统告警：{系统告警}\n- 应用告警：{应用告警}\n- 业务告警：{业务告警}\n\n**告警级别：**\n- 严重告警：{严重告警}\n- 警告告警：{警告告警}\n- 信息告警：{信息告警}\n\n**通知方式：**\n- 通知渠道：{通知渠道}\n- 通知对象：{通知对象}\n- 升级策略：{升级策略}\n\n**业务特点：**\n- 业务时间：{业务时间}\n- SLA要求：{SLA要求}\n- 容忍度：{容忍度}\n\n**请配置：**\n1. 告警规则定义\n2. 阈值设置策略\n3. 告警分组和路由\n4. 通知模板设计\n5. 告警抑制规则\n\n请提供告警规则YAML、通知配置和最佳实践。"}, {"id": "log-aggregation-analysis", "title": "日志聚合和分析", "description": "配置日志收集、聚合和分析系统", "category": "监控告警", "content": "作为Kubernetes日志专家，请帮我设计日志管理方案。\n\n**日志来源：**\n- 应用日志：{应用日志}\n- 系统日志：{系统日志}\n- 审计日志：{审计日志}\n\n**日志特征：**\n- 日志量：{日志量}\n- 日志格式：{日志格式}\n- 重要级别：{重要级别}\n\n**分析需求：**\n- 实时分析：{实时分析}\n- 历史查询：{历史查询}\n- 异常检测：{异常检测}\n\n**技术栈：**\n- 收集工具：{收集工具}\n- 存储方案：{存储方案}\n- 分析平台：{分析平台}\n\n**请设计：**\n1. 日志收集架构\n2. 日志格式标准化\n3. 存储和索引策略\n4. 查询和分析方案\n5. 日志保留策略\n\n请提供ELK/EFK配置、日志规范和分析仪表板。"}, {"id": "cluster-health-check", "title": "集群健康状态检查", "description": "全面检查Kubernetes集群健康状态", "category": "集群管理", "content": "作为Kubernetes集群管理专家，请帮我进行集群健康检查。\n\n**集群信息：**\n- 集群版本：{集群版本}\n- 节点数量：{节点数量}\n- 组件状态：{组件状态}\n\n**检查范围：**\n- 控制平面：{控制平面}\n- 工作节点：{工作节点}\n- 网络组件：{网络组件}\n- 存储系统：{存储系统}\n\n**关注指标：**\n- 可用性：{可用性}\n- 性能指标：{性能指标}\n- 资源使用：{资源使用}\n- 错误率：{错误率}\n\n**请检查：**\n1. 集群组件状态\n2. 节点健康状况\n3. 网络连通性\n4. 存储可用性\n5. 安全配置\n6. 性能指标\n\n请提供健康检查脚本、诊断命令和修复建议。"}, {"id": "node-maintenance-operation", "title": "节点维护操作", "description": "安全地进行节点维护和升级操作", "category": "集群管理", "content": "作为Kubernetes节点管理专家，请指导我进行节点维护。\n\n**维护类型：**\n- 维护操作：{维护操作}\n- 维护范围：{维护范围}\n- 维护时间：{维护时间}\n\n**节点信息：**\n- 节点名称：{节点名称}\n- 节点角色：{节点角色}\n- 运行Pod：{运行Pod}\n\n**业务影响：**\n- 关键应用：{关键应用}\n- 可用性要求：{可用性要求}\n- 维护窗口：{维护窗口}\n\n**请提供：**\n1. 维护前准备工作\n2. Pod驱逐策略\n3. 节点隔离步骤\n4. 维护操作指南\n5. 恢复验证方法\n\n请包含节点管理命令、维护脚本和回滚方案。"}, {"id": "cluster-upgrade-planning", "title": "集群版本升级规划", "description": "规划和执行Kubernetes集群版本升级", "category": "集群管理", "content": "作为Kubernetes升级专家，请帮我规划集群版本升级。\n\n**升级信息：**\n- 当前版本：{当前版本}\n- 目标版本：{目标版本}\n- 升级路径：{升级路径}\n\n**集群环境：**\n- 集群规模：{集群规模}\n- 部署方式：{部署方式}\n- 关键应用：{关键应用}\n\n**业务要求：**\n- 可用性要求：{可用性要求}\n- 升级窗口：{升级窗口}\n- 回滚要求：{回滚要求}\n\n**风险评估：**\n- 兼容性风险：{兼容性风险}\n- API变更：{API变更}\n- 功能废弃：{功能废弃}\n\n**请提供：**\n1. 升级前评估\n2. 升级策略设计\n3. 分阶段升级计划\n4. 测试验证方案\n5. 回滚预案\n\n请包含升级脚本、验证清单和最佳实践。"}, {"id": "backup-recovery-strategy", "title": "备份恢复策略", "description": "设计Kubernetes集群和应用的备份恢复方案", "category": "集群管理", "content": "作为Kubernetes备份专家，请帮我设计备份恢复策略。\n\n**备份范围：**\n- etcd数据：{etcd数据}\n- 应用数据：{应用数据}\n- 配置信息：{配置信息}\n\n**业务要求：**\n- RTO目标：{RTO目标}\n- RPO目标：{RPO目标}\n- 备份频率：{备份频率}\n\n**技术环境：**\n- 存储类型：{存储类型}\n- 备份工具：{备份工具}\n- 存储位置：{存储位置}\n\n**灾难场景：**\n- 节点故障：{节点故障}\n- 集群故障：{集群故障}\n- 数据损坏：{数据损坏}\n\n**请设计：**\n1. 备份策略和计划\n2. 备份工具选择\n3. 恢复流程设计\n4. 测试验证方案\n5. 监控和告警\n\n请提供备份脚本、恢复手册和最佳实践。"}, {"id": "blops-platform-features", "title": "Blops平台功能使用", "description": "了解和使用Blops平台的各项功能", "category": "平台使用", "content": "作为Blops平台专家，请帮我了解平台功能使用方法。\n\n**使用场景：**\n- 使用目的：{使用目的}\n- 用户角色：{用户角色}\n- 操作频率：{操作频率}\n\n**功能需求：**\n- 集群管理：{集群管理}\n- 应用部署：{应用部署}\n- 监控告警：{监控告警}\n- 日志查看：{日志查看}\n\n**当前问题：**\n- 操作困难：{操作困难}\n- 功能不熟悉：{功能不熟悉}\n- 效率问题：{效率问题}\n\n**请介绍：**\n1. 平台核心功能\n2. 操作界面指导\n3. 常用操作流程\n4. 高级功能使用\n5. 最佳实践建议\n\n请提供功能说明、操作指南和使用技巧。"}, {"id": "kubectl-commands-guide", "title": "kubectl命令使用指南", "description": "掌握kubectl命令的高效使用方法", "category": "平台使用", "content": "作为kubectl专家，请帮我掌握kubectl命令的高效使用。\n\n**使用场景：**\n- 操作类型：{操作类型}\n- 资源对象：{资源对象}\n- 操作频率：{操作频率}\n\n**当前水平：**\n- 熟悉程度：{熟悉程度}\n- 常用命令：{常用命令}\n- 遇到困难：{遇到困难}\n\n**学习需求：**\n- 基础命令：{基础命令}\n- 高级功能：{高级功能}\n- 自动化脚本：{自动化脚本}\n\n**请提供：**\n1. 常用命令清单\n2. 高级用法技巧\n3. 输出格式化方法\n4. 批量操作技巧\n5. 故障排查命令\n\n请包含命令示例、参数说明和实用技巧。"}, {"id": "troubleshooting-methodology", "title": "Kubernetes故障排查方法论", "description": "系统化的Kubernetes问题诊断和解决方法", "category": "平台使用", "content": "作为Kubernetes故障排查专家，请教我系统化的故障排查方法。\n\n**故障类型：**\n- 故障现象：{故障现象}\n- 影响范围：{影响范围}\n- 紧急程度：{紧急程度}\n\n**环境信息：**\n- 集群环境：{集群环境}\n- 应用架构：{应用架构}\n- 监控工具：{监控工具}\n\n**排查经验：**\n- 经验水平：{经验水平}\n- 常见问题：{常见问题}\n- 工具熟悉度：{工具熟悉度}\n\n**请教授：**\n1. 故障排查流程\n2. 信息收集方法\n3. 问题定位技巧\n4. 根因分析方法\n5. 解决方案验证\n\n请提供排查清单、诊断工具和方法论指导。"}]