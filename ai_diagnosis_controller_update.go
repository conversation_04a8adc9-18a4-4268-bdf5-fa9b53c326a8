// 这是对现有 ai_diagnosis_controller.go 中 GetQuickTemplates 函数的优化更新
// 请将此代码替换原有的 GetQuickTemplates 函数

// GetQuickTemplates 获取优化后的快速问题模板
// @Summary      获取快速问题模板
// @Description  获取基于专业Kubernetes知识优化的快速问题模板列表
// @Tags         AI助手
// @Accept       json
// @Produce      json
// @Param        category  query     string  false  "模板分类过滤"
// @Param        search    query     string  false  "搜索关键词"
// @Success      200 {object} models.Response{data=[]QuickTemplate} "获取成功"
// @Router       /ai/templates [get]
// @Security     ApiKeyAuth
func GetQuickTemplates(c *gin.Context) {
	category := c.Query("category")
	search := c.Query("search")

	// 优化后的专业模板数据
	templates := []QuickTemplate{
		// 故障排查类 - 高优先级
		{
			ID:          "pod-startup-failure-advanced",
			Title:       "Pod启动失败深度诊断",
			Description: "系统化诊断Pod启动失败的各种复杂原因",
			Content: `作为资深Kubernetes运维专家，请帮我进行Pod启动失败的深度诊断。

**环境信息：**
- 集群版本：{集群版本}
- 节点操作系统：{节点OS}
- 容器运行时：{容器运行时}
- 命名空间：{命名空间}
- Pod名称：{Pod名称}

**问题描述：**
- Pod当前状态：{Pod状态}
- 容器状态：{容器状态}
- 重启次数：{重启次数}
- 错误信息：{错误信息}
- 相关事件：{事件信息}

**请按以下步骤进行系统化诊断：**

1. **基础状态检查**
   - 提供检查Pod详细状态的kubectl命令
   - 分析Pod生命周期各阶段状态
   - 检查容器镜像和启动命令

2. **资源和调度分析**
   - 检查节点资源可用性
   - 分析Pod调度约束和亲和性
   - 验证资源请求和限制配置

3. **网络和存储检查**
   - 检查网络插件和DNS配置
   - 验证存储卷挂载状态
   - 分析PVC绑定情况

4. **安全和权限验证**
   - 检查ServiceAccount权限
   - 验证Pod安全策略
   - 分析镜像拉取权限

5. **配置和依赖分析**
   - 检查ConfigMap和Secret配置
   - 验证环境变量和启动参数
   - 分析应用依赖服务状态

请提供具体的kubectl命令示例、常见问题解决方案和预防措施建议。`,
			Category: "故障排查",
		},
		{
			ID:          "pod-crashloop-comprehensive",
			Title:       "CrashLoopBackOff综合分析",
			Description: "全面分析和解决Pod反复崩溃重启问题",
			Content: `作为Kubernetes故障排查专家，请帮我全面分析CrashLoopBackOff问题。

**Pod基本信息：**
- Pod名称：{Pod名称}
- 命名空间：{命名空间}
- 镜像版本：{镜像版本}
- 重启次数：{重启次数}
- 最后退出码：{退出码}
- 退出原因：{退出原因}

**容器日志信息：**
` + "```" + `
{容器日志}
` + "```" + `

**资源配置：**
- CPU请求/限制：{CPU配置}
- 内存请求/限制：{内存配置}
- 存储配置：{存储配置}

**请进行以下综合分析：**

1. **崩溃原因分类**
   - 应用程序错误（代码bug、配置错误）
   - 资源限制问题（OOMKilled、CPU限制）
   - 依赖服务问题（数据库连接、外部API）
   - 健康检查配置问题

2. **日志分析方法**
   - 提供查看容器日志的完整命令
   - 分析错误日志模式和关键信息
   - 识别崩溃时间点和触发条件

3. **资源使用分析**
   - 检查内存使用趋势和峰值
   - 分析CPU使用模式
   - 验证磁盘空间和IO性能

4. **配置优化建议**
   - 调整资源请求和限制
   - 优化健康检查参数
   - 配置优雅关闭和启动策略

5. **监控和预防措施**
   - 设置相关监控指标
   - 配置告警规则
   - 建立故障预防机制

请提供详细的诊断命令、配置优化方案和长期稳定性建议。`,
			Category: "故障排查",
		},
		{
			ID:          "service-discovery-troubleshooting",
			Title:       "服务发现和网络连接故障排查",
			Description: "深度排查Kubernetes服务发现和网络连接问题",
			Content: `作为Kubernetes网络专家，请帮我深度排查服务发现和网络连接问题。

**网络环境信息：**
- 网络插件：{网络插件}
- 集群CIDR：{集群CIDR}
- 服务CIDR：{服务CIDR}
- DNS服务：{DNS服务}

**问题场景：**
- 源Pod：{源Pod}
- 目标Service：{目标Service}
- 访问方式：{访问方式}（ClusterIP/NodePort/LoadBalancer）
- 错误现象：{错误现象}

**连接详情：**
- 连接超时：{连接超时}
- 错误代码：{错误代码}
- 间歇性问题：{间歇性问题}

**请按以下层次进行网络诊断：**

1. **DNS解析层面**
   - 检查CoreDNS配置和状态
   - 测试DNS解析功能
   - 验证DNS策略设置
   - 分析DNS缓存问题

2. **Service和Endpoints层面**
   - 检查Service配置和选择器
   - 验证Endpoints对象状态
   - 分析负载均衡算法
   - 检查服务端口配置

3. **网络连通性层面**
   - 测试Pod间网络连通性
   - 检查网络策略限制
   - 验证防火墙规则
   - 分析路由配置

4. **应用层面**
   - 检查应用监听端口
   - 验证健康检查配置
   - 分析应用日志
   - 测试应用响应

5. **集群组件层面**
   - 检查kube-proxy配置
   - 验证网络插件状态
   - 分析负载均衡器配置
   - 检查Ingress控制器

请提供完整的网络诊断工具使用方法、故障排查流程和网络优化建议。`,
			Category: "故障排查",
		},

		// 性能优化类 - 专业级
		{
			ID:          "cluster-performance-analysis",
			Title:       "集群性能深度分析与优化",
			Description: "全面分析集群性能瓶颈并提供专业优化方案",
			Content: `作为Kubernetes性能优化专家，请帮我进行集群性能深度分析。

**集群基础信息：**
- Kubernetes版本：{K8s版本}
- 节点规格：{节点规格}
- 节点数量：{节点数量}
- 总资源：CPU {总CPU}核，内存 {总内存}GB
- 工作负载数量：{工作负载数量}

**当前性能指标：**
- 集群CPU使用率：{CPU使用率}%
- 集群内存使用率：{内存使用率}%
- 存储IOPS：{存储IOPS}
- 网络吞吐量：{网络吞吐量}
- API Server响应时间：{API响应时间}ms

**业务性能表现：**
- 应用平均响应时间：{应用响应时间}ms
- 请求成功率：{成功率}%
- 并发处理能力：{并发能力}
- 资源利用效率：{资源效率}%

**请进行以下性能分析：**

1. **资源利用率分析**
   - 分析CPU、内存、存储、网络使用模式
   - 识别资源热点和瓶颈节点
   - 评估资源分配合理性
   - 计算资源浪费和不足情况

2. **工作负载性能分析**
   - 分析Pod资源配置合理性
   - 评估QoS等级分布
   - 识别性能敏感应用
   - 分析资源竞争情况

3. **集群组件性能分析**
   - 评估API Server性能
   - 分析etcd性能指标
   - 检查调度器效率
   - 评估网络插件性能

4. **存储性能分析**
   - 分析存储类性能特征
   - 评估PV/PVC使用效率
   - 识别存储IO瓶颈
   - 优化存储配置

5. **网络性能分析**
   - 分析网络延迟和吞吐量
   - 评估服务网格性能影响
   - 识别网络瓶颈
   - 优化网络配置

**请提供：**
- 详细的性能监控命令和工具
- 具体的优化配置建议
- 容量规划和扩展建议
- 性能基准测试方案
- 长期性能优化策略

请基于实际数据提供可执行的优化方案。`,
			Category: "性能优化",
		},

		// 安全检查类 - 企业级
		{
			ID:          "security-comprehensive-audit",
			Title:       "Kubernetes安全全面审计",
			Description: "企业级Kubernetes安全配置审计和加固建议",
			Content: `作为Kubernetes安全专家，请帮我进行全面的安全审计。

**审计范围：**
- 集群版本：{集群版本}
- 节点数量：{节点数量}
- 命名空间：{命名空间列表}
- 关键应用：{关键应用}

**合规要求：**
- 安全标准：{安全标准}（如CIS、NIST、SOC2）
- 行业要求：{行业要求}
- 内部政策：{内部政策}

**当前安全配置：**
- RBAC配置：{RBAC配置}
- 网络策略：{网络策略}
- Pod安全策略：{Pod安全策略}
- 准入控制器：{准入控制器}

**请进行以下安全审计：**

1. **身份认证和授权**
   - 审计用户和服务账户配置
   - 检查RBAC权限分配
   - 验证最小权限原则
   - 分析权限提升风险

2. **网络安全**
   - 审计网络策略配置
   - 检查服务暴露方式
   - 验证TLS配置
   - 分析网络隔离效果

3. **Pod和容器安全**
   - 检查Pod安全上下文
   - 审计容器镜像安全
   - 验证资源限制
   - 分析特权容器使用

4. **数据保护**
   - 审计Secret管理
   - 检查数据加密配置
   - 验证备份安全
   - 分析数据访问控制

5. **集群安全**
   - 检查API Server安全配置
   - 审计etcd安全设置
   - 验证节点安全加固
   - 分析审计日志配置

6. **供应链安全**
   - 审计镜像来源和签名
   - 检查依赖组件安全
   - 验证构建流程安全
   - 分析漏洞扫描结果

**请提供：**
- 详细的安全检查清单
- 具体的安全加固建议
- 合规性差距分析
- 安全监控配置方案
- 事件响应预案

请基于行业最佳实践提供可执行的安全加固方案。`,
			Category: "安全检查",
		},

		// 资源管理类 - 企业级
		{
			ID:          "enterprise-resource-governance",
			Title:       "企业级资源治理和成本优化",
			Description: "大规模Kubernetes集群资源治理和成本控制策略",
			Content: `作为Kubernetes资源治理专家，请帮我设计企业级资源管理策略。

**企业环境：**
- 集群规模：{集群规模}
- 团队数量：{团队数量}
- 应用数量：{应用数量}
- 月度成本：{月度成本}

**资源现状：**
- 总资源容量：{总资源容量}
- 资源利用率：{资源利用率}
- 资源浪费率：{资源浪费率}
- 成本分布：{成本分布}

**治理需求：**
- 多租户隔离：{多租户需求}
- 成本控制：{成本控制目标}
- 资源配额：{配额策略}
- 合规要求：{合规要求}

**请设计以下治理策略：**

1. **多租户资源隔离**
   - 设计命名空间隔离策略
   - 配置资源配额和限制
   - 实现网络和存储隔离
   - 建立租户管理机制

2. **资源配额管理**
   - 设计分层配额体系
   - 配置动态配额调整
   - 实现配额监控告警
   - 建立配额申请流程

3. **成本控制和优化**
   - 实现成本可视化
   - 建立成本分摊机制
   - 优化资源配置
   - 实现成本预算控制

4. **资源生命周期管理**
   - 建立资源申请审批流程
   - 实现资源自动回收
   - 配置资源使用监控
   - 建立资源优化建议

5. **治理策略执行**
   - 配置策略引擎
   - 实现自动化治理
   - 建立违规检测
   - 配置治理报告

6. **容量规划和预测**
   - 分析资源使用趋势
   - 预测容量需求
   - 规划扩容策略
   - 优化资源采购

**请提供：**
- 完整的治理架构设计
- 具体的配置实现方案
- 成本优化策略
- 监控和报告方案
- 治理流程和制度

请基于企业最佳实践提供可落地的治理方案。`,
			Category: "资源管理",
		},

		// 平台使用类 - Blops专用
		{
			ID:          "blops-advanced-features",
			Title:       "Blops平台高级功能使用指南",
			Description: "深入了解Blops平台的高级功能和最佳实践",
			Content: `作为Blops平台专家，请帮我掌握平台的高级功能使用方法。

**使用背景：**
- 用户角色：{用户角色}
- 使用场景：{使用场景}
- 技能水平：{技能水平}
- 主要需求：{主要需求}

**当前使用情况：**
- 已使用功能：{已使用功能}
- 遇到问题：{遇到问题}
- 效率瓶颈：{效率瓶颈}
- 学习需求：{学习需求}

**请详细介绍以下高级功能：**

1. **集群管理高级功能**
   - 多集群统一管理
   - 集群健康监控
   - 自动化运维操作
   - 集群性能优化

2. **应用部署和管理**
   - 应用模板和市场
   - CI/CD集成
   - 蓝绿部署和金丝雀发布
   - 应用生命周期管理

3. **监控和可观测性**
   - 自定义监控仪表板
   - 智能告警配置
   - 日志聚合和分析
   - 链路追踪集成

4. **安全和合规**
   - 安全策略管理
   - 合规性检查
   - 漏洞扫描集成
   - 审计日志分析

5. **资源优化和成本管理**
   - 资源使用分析
   - 成本可视化
   - 自动化资源优化
   - 预算控制和告警

6. **自动化和集成**
   - API和SDK使用
   - Webhook集成
   - 自定义插件开发
   - 第三方工具集成

**请提供：**
- 功能使用的详细步骤
- 最佳实践和使用技巧
- 常见问题和解决方案
- 高级配置示例
- 效率提升建议

请结合实际使用场景提供具体的操作指导。`,
			Category: "平台使用",
		},
	}

	// 应用过滤条件
	filteredTemplates := templates
	
	// 按分类过滤
	if category != "" {
		var categoryFiltered []QuickTemplate
		for _, template := range filteredTemplates {
			if template.Category == category {
				categoryFiltered = append(categoryFiltered, template)
			}
		}
		filteredTemplates = categoryFiltered
	}

	// 按搜索关键词过滤
	if search != "" {
		var searchFiltered []QuickTemplate
		for _, template := range filteredTemplates {
			if strings.Contains(strings.ToLower(template.Title), strings.ToLower(search)) ||
				strings.Contains(strings.ToLower(template.Description), strings.ToLower(search)) ||
				strings.Contains(strings.ToLower(template.Content), strings.ToLower(search)) {
				searchFiltered = append(searchFiltered, template)
			}
		}
		filteredTemplates = searchFiltered
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取快速模板成功",
		"data":    filteredTemplates,
	})
}
