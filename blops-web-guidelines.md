# Blops 前端 (blops-web) 开发规范

## 1. 引言

本规范旨在为使用 React、UmiJS 和 Ant Design 开发 Blops 前端应用提供标准和最佳实践。遵循这些规范有助于确保代码一致性、可维护性，并促进团队协作。

## 2. 项目结构

保持既定的 UmiJS 项目结构：

-   **`config/`**: 配置文件目录。
    -   `config.ts`: UmiJS 主配置文件。
    -   `routes.ts`: 应用路由定义文件。
    -   `proxy.ts`: 开发环境代理设置。
    -   `defaultSettings.ts`: 默认布局设置。
    -   `oneapi.json`: OpenAPI 规范文件（如果用于代码生成）。
-   **`mock/`**: 开发环境模拟数据文件。
-   **`public/`**: 由 Web 服务器直接提供的静态资源。
-   **`src/`**: 主要的应用源代码目录。
    -   **`app.tsx`**: UmiJS 运行时配置文件（布局、初始状态、请求拦截器）。
    -   **`access.ts`**: 访问控制逻辑。
    -   **`assets/`**: 静态资源，如图片、字体（在组件中导入）。
    -   **`components/`**: 可在多个页面复用的 UI 组件。
        -   每个组件放在单独的文件夹中（例如 `src/components/MyComponent/index.tsx`）。
    -   **`constants/`**: 应用范围内的常量值。
    -   **`dtos/` 或 `models/`**: API 数据结构（请求、响应）的 TypeScript 接口/类型。选择一个名称并保持一致（例如 `src/dtos/user.dto.ts`）。
    -   **`hooks/`**: 自定义 React Hooks。
    -   **`layouts/`**: 布局组件（如果需要自定义 `plugin-layout` 之外的布局）。
    -   **`locales/`**: 国际化文件（如果使用 `plugin-locale`）。
    -   **`models/`**: 用于全局状态管理的 DVA models（如果使用 `plugin-dva`）。
    -   **`pages/`**: 页面级组件，与路由对应。
        -   每个页面放在单独的文件夹中，通常包含一个 `index.tsx`（例如 `src/pages/cluster/ai-diagnosis/index.tsx`）。
        -   页面特定的组件可以放在页面文件夹内（例如 `src/pages/cluster/ai-diagnosis/components/ResultDisplay.tsx`）。
    -   **`services/`**: API 请求函数，按功能/资源组织（例如 `src/services/cluster.ts`, `src/services/aiDiagnosis.ts`）。
    -   **`styles/`**: 全局样式或共享的样式变量/混合（mixins）。
    -   **`utils/`**: 工具函数（例如 `request.ts`, `localStorage.ts`, 格式化函数）。
-   **`.env`**: 环境变量文件。
-   **`.eslintrc.js`, `.prettierrc.js`**: Linting 和格式化配置文件。
-   **`package.json`**: 项目依赖和脚本。
-   **`tsconfig.json`**: TypeScript 配置文件。

## 3. 命名规范

-   **组件:** PascalCase (大驼峰) (`UserProfile.tsx`, `DataForm.tsx`)。
-   **页面:** 文件夹名使用 kebab-case (短横线分隔) (`ai-diagnosis`)，主文件为 `index.tsx` (`src/pages/cluster/ai-diagnosis/index.tsx`)。
-   **Services/API 文件:** camelCase (小驼峰) (`clusterService.ts`, `aiDiagnosis.ts`)。内部函数也使用 camelCase (`getClusters`, `analyzeEvents`)。
-   **Utils:** camelCase (`dateUtils.ts`, `formatNumber`)。
-   **Models/DTOs/Interfaces:** PascalCase (`UserLoginRequest.ts`, `ClusterInfo.ts`)。如果偏好，接口可加 `I` 前缀（例如 `IUserProfileProps`），但需保持一致。
-   **变量/函数:** camelCase (`isLoading`, `fetchData`)。
-   **常量:** UPPER_SNAKE_CASE (大写下划线分隔) (`MAX_RETRIES`, `API_TIMEOUT`)。
-   **CSS Modules:** camelCase (`styles.container`, `styles.buttonActive`)。

## 4. 编码风格与格式化

-   **语言:** 所有新代码使用 **TypeScript**。充分利用其静态类型特性。
-   **框架:** 使用 **React 函数式组件** 和 **Hooks**。除非必要，避免使用类组件。
-   **格式化:** 使用 **Prettier** 进行自动代码格式化。确保将其集成到 IDE 中，并考虑作为 pre-commit hook。
-   **Linting:** 使用 **ESLint** 及推荐的 React/TypeScript 规则来捕获潜在错误并强制执行代码风格。解决所有 linting 警告/错误。
-   **注释:** 编写清晰的注释来解释代码的 *原因*，而不是 *做了什么*（代码本身应解释 *做了什么*）。对复杂函数和组件 props 使用 JSDoc。

## 5. 组件设计

-   **单一职责:** 组件应该只做好一件事。
-   **可复用性:** 识别并提取可复用的 UI 逻辑到 `src/components/`。
-   **Props:** 通过 props 向下传递数据。使用清晰、描述性的 prop 名称。使用 TypeScript 接口定义 prop 类型。
-   **State:** 除非需要在组件间共享，否则将 state 保持在局部（`useState`, `useReducer`）。
-   **组合:** 通过组合小型组件来构建复杂的 UI。

## 6. 状态管理

-   **局部状态:** 使用 `useState` 和 `useReducer` 管理组件内部状态。
-   **全局/共享状态:** 使用 UmiJS Models (`plugin-dva`) 管理跨多个页面或组件共享的状态。在 `src/models/` 中定义 models。

## 7. API 调用

-   **位置:** 所有后端 API 调用应在 `src/services/` 目录中定义，并按资源进行组织。
-   **工具:** 使用 `src/utils/request.ts` 中配置的 `umi-request` 实例。对于 AI 相关调用，使用特定的 `aiRequest` 实例（因其超时时间较长）。
-   **类型定义:** 在 `src/dtos/` (或 `src/models/`) 中为 API 请求负载和响应定义 TypeScript 类型/接口。在 service 函数和组件中使用这些类型。
-   **错误处理:** 为 API 调用实现健壮的错误处理。使用 `try...catch` 或 Promise 的 `.catch()` 块。使用 Ant Design 组件（`message`, `notification`, `Alert`）向用户显示友好的错误信息。
-   **加载状态:** 在 API 调用期间提供视觉反馈（例如 `Spin`、按钮的 loading 状态）。

## 8. 样式

-   **方法:** 主要使用 **CSS Modules** 实现组件作用域内的样式。
-   **Ant Design:** 利用 Ant Design 组件及其主题定制能力 (`config/theme.ts`)。避免使用全局 CSS 过度覆盖 Ant Design 的样式。
-   **全局样式:** 将最少的全局样式放在 `src/styles/global.less`（或类似文件）中。

## 9. 路由

-   在 `config/routes.ts` 中定义所有应用路由。
-   使用 UmiJS 的 `<Link>` 组件或 `history.push()` 进行导航。

## 10. 测试

-   使用 Jest 和 React Testing Library 为工具函数、自定义 Hooks 以及可能复杂的组件编写单元测试。
-   考虑为关键用户流程编写集成测试。

## 11. 依赖管理

-   使用 `pnpm`（或 `yarn`/`npm` - 保持一致）进行包管理。
-   定期审查和更新依赖项。
-   避免添加不必要的依赖。

## 12. Git 工作流

-   **分支:** 使用特性分支（例如 `feat/ai-diagnosis-page`, `fix/login-bug`）。从主开发分支（例如 `develop` 或 `main`）创建分支。
-   **提交:** 编写清晰、简洁的 commit 消息。考虑使用 Conventional Commits 格式（例如 `feat: add AI diagnosis result display`, `fix: correct namespace dropdown filtering`）。
-   **Pull Requests (PR):** 使用 PR 合并特性分支。合并前需要代码审查。
-   **合并:** 使用 squash 或 rebase 合并 PR，以保持主分支历史记录的整洁。
