.templateModal {
  .ant-modal-content {
    border-radius: 16px;
    overflow: hidden;
  }

  .ant-modal-header {
    background: linear-gradient(90deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e5e7eb;
    padding: 20px 24px;
  }

  .ant-modal-body {
    padding: 0;
    max-height: 70vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  &.darkModal {
    .ant-modal-content {
      background: #1f1f1f;
    }

    .ant-modal-header {
      background: linear-gradient(90deg, #2d2d2d 0%, #1a1a1a 100%);
      border-bottom: 1px solid #434343;
    }
  }
}

.modalHeader {
  display: flex;
  align-items: center;
  width: 100%;

  .modalIcon {
    color: #667eea;
    font-size: 20px;
    margin-right: 8px;
  }

  .modalTitle {
    margin: 0;
    color: #1f2937;
    font-weight: 600;
  }
}

.toolbar {
  padding: 20px 24px 0;
  background: #fafbfc;
  border-bottom: 1px solid #e5e7eb;

  .searchInput {
    border-radius: 8px;
    
    .ant-input {
      border-radius: 8px;
    }
  }

  .darkModal & {
    background: #262626;
    border-bottom: 1px solid #434343;
  }
}

.templatesContainer {
  flex: 1;
  overflow-y: auto;
  padding: 24px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    transition: background 0.2s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }
}

.templatesContent {
  .categorySection {
    margin-bottom: 32px;

    &:last-child {
      margin-bottom: 0;
    }

    .categoryHeader {
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 2px solid #e5e7eb;

      .categoryIcon {
        font-size: 18px;
        margin-right: 8px;
      }

      .categoryTitle {
        margin: 0;
        color: #1f2937;
        font-weight: 600;
      }
    }
  }

  .templateGrid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 20px;
  }
}

.templateCard {
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;

  &:hover {
    border-color: #667eea;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.15);
    transform: translateY(-2px);
  }

  .ant-card-body {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .ant-card-actions {
    border-top: 1px solid #e5e7eb;
    background: #fafbfc;

    .ant-card-actions > li {
      margin: 8px 0;

      .ant-btn-link {
        color: #667eea;
        font-weight: 500;

        &:hover {
          color: #4f46e5;
        }
      }
    }
  }

  &.darkCard {
    background: #374151;
    border-color: #4b5563;

    .ant-card-actions {
      background: #4b5563;
      border-top-color: #6b7280;
    }

    &:hover {
      border-color: #4facfe;
      box-shadow: 0 8px 32px rgba(79, 172, 254, 0.15);
    }
  }
}

.cardHeader {
  margin-bottom: 16px;

  .cardTitle {
    margin-bottom: 8px;

    .categoryIcon {
      font-size: 16px;
      margin-right: 6px;
    }

    .titleText {
      font-size: 15px;
      font-weight: 600;
      color: #1f2937;
      line-height: 1.4;
    }
  }

  .cardMeta {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .categoryTag {
      font-size: 11px;
      padding: 2px 8px;
      border-radius: 12px;
      margin: 0;
    }
  }
}

.cardContent {
  flex: 1;
  display: flex;
  flex-direction: column;

  .description {
    color: #6b7280;
    font-size: 13px;
    line-height: 1.5;
    margin-bottom: 12px;
  }

  .contentPreview {
    background: #f8fafc;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
    flex: 1;

    .previewText {
      font-size: 12px;
      color: #4b5563;
      line-height: 1.4;
      display: block;
      white-space: pre-wrap;
      word-break: break-word;
      background: transparent;
      border: none;
      padding: 0;
    }

    .darkCard & {
      background: #1f2937;
      border-color: #374151;

      .previewText {
        color: #d1d5db;
      }
    }
  }

  .tags {
    display: flex;
    align-items: center;
    margin-top: auto;

    .tagsIcon {
      color: #9ca3af;
      font-size: 12px;
      margin-right: 8px;
    }

    .tag {
      font-size: 11px;
      padding: 1px 6px;
      border-radius: 4px;
      margin: 0 4px 0 0;
    }
  }
}

.footer {
  padding: 16px 24px;
  background: #f8fafc;
  border-top: 1px solid #e5e7eb;
  text-align: center;

  .darkModal & {
    background: #262626;
    border-top: 1px solid #434343;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .templatesContent .templateGrid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .templateModal {
    .ant-modal-content {
      margin: 0;
      max-width: 100vw;
      height: 100vh;
      border-radius: 0;
    }

    .ant-modal-body {
      max-height: calc(100vh - 120px);
    }
  }

  .toolbar {
    padding: 16px;

    .ant-row {
      flex-direction: column;
      gap: 12px;

      .ant-col {
        width: 100%;
      }
    }
  }

  .templatesContainer {
    padding: 16px;
  }

  .templatesContent .templateGrid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .templateCard {
    .ant-card-body {
      padding: 16px;
    }

    .cardContent .contentPreview {
      padding: 10px;
    }
  }

  .footer {
    padding: 12px 16px;
    
    .ant-space {
      flex-direction: column;
      gap: 4px;
    }
  }
}

@media (max-width: 480px) {
  .modalHeader {
    .modalTitle {
      font-size: 16px;
    }
  }

  .templatesContent .categorySection .categoryHeader {
    .categoryTitle {
      font-size: 14px;
    }
  }

  .templateCard {
    .cardHeader .cardTitle .titleText {
      font-size: 14px;
    }

    .cardContent {
      .description {
        font-size: 12px;
      }

      .contentPreview .previewText {
        font-size: 11px;
      }
    }
  }
}

// 深色主题特殊处理
.darkModal {
  .modalHeader .modalTitle {
    color: #ffffff;
  }

  .templatesContent .categorySection .categoryHeader .categoryTitle {
    color: #ffffff;
  }

  .templateCard.darkCard {
    .cardHeader .cardTitle .titleText {
      color: #ffffff;
    }

    .cardContent .description {
      color: #d1d5db;
    }
  }

  .toolbar {
    .ant-input {
      background: #374151;
      border-color: #4b5563;
      color: #ffffff;

      &::placeholder {
        color: #9ca3af;
      }
    }

    .ant-select {
      .ant-select-selector {
        background: #374151;
        border-color: #4b5563;
        color: #ffffff;
      }
    }
  }
}

// 动画效果
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.templateCard {
  animation: slideInUp 0.3s ease-out;
}

.templatesContent .categorySection {
  animation: slideInUp 0.4s ease-out;
}

// 高对比度模式支持
@media (prefers-contrast: high) {
  .templateCard {
    border-width: 2px;
    
    &:hover {
      border-width: 3px;
    }
  }

  .cardContent .contentPreview {
    border-width: 2px;
  }
}

// 减少动画模式支持
@media (prefers-reduced-motion: reduce) {
  .templateCard {
    animation: none;
    transition: none;
    
    &:hover {
      transform: none;
    }
  }

  .templatesContent .categorySection {
    animation: none;
  }
}
